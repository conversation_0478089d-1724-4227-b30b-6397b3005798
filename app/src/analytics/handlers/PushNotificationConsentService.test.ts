import Airship, { PermissionStatus } from '@ua/react-native-airship';
import { logger } from '@utils/logger';

import { AirshipAnalyticsService } from './airship';
import { PushNotificationConsentService } from './PushNotificationConsentService';

jest.mock('@ua/react-native-airship', () => ({
  push: {
    getNotificationStatus: jest.fn(),
    enableUserNotifications: jest.fn(),
  },
  PermissionStatus: {
    Granted: 'granted',
    Denied: 'denied',
    NotDetermined: 'not_determined',
  },
}));

jest.mock('@utils/logger', () => ({
  logger: {
    info: jest.fn(),
  },
}));

jest.mock('./airship', () => ({
  AirshipAnalyticsService: {
    setSalesforcePushConsentAttribute: jest.fn(),
  },
}));

describe('PushNotificationConsentService', () => {
  let service: PushNotificationConsentService;

  beforeEach(() => {
    service = new PushNotificationConsentService();
    jest.clearAllMocks();
  });

  describe('isPushNotificationAllowedByUser', () => {
    it('should return true if permission is granted', async () => {
      (Airship.push.getNotificationStatus as jest.Mock).mockResolvedValue({
        notificationPermissionStatus: PermissionStatus.Granted,
      });

      const result = await service.isPushNotificationAllowedByUser();

      expect(result).toBe(true);
      expect(logger.info).toHaveBeenCalledWith(
        'Device Push Notification Constent : ',
        PermissionStatus.Granted,
      );
    });

    it('should return false if permission is not granted', async () => {
      (Airship.push.getNotificationStatus as jest.Mock).mockResolvedValue({
        notificationPermissionStatus: PermissionStatus.Denied,
      });

      const result = await service.isPushNotificationAllowedByUser();

      expect(result).toBe(false);
      expect(logger.info).toHaveBeenCalledWith(
        'Device Push Notification Constent : ',
        PermissionStatus.Denied,
      );
    });
  });

  describe('isSalesForcePushConsentAccepted', () => {
    it('should return true and set Airship attribute when consent includes Push channel and accepted is true', async () => {
      const consentData = [
        { channel: 'Email', accepted: false },
        { channel: 'Push', accepted: true },
      ];

      const result = await service.isSalesForcePushConsentAccepted(consentData);

      expect(result).toBe(true);
      expect(logger.info).toHaveBeenCalledWith(
        'SalesForce Push Notification Constent Acceptance',
        true,
      );
      expect(
        AirshipAnalyticsService.setSalesforcePushConsentAttribute,
      ).toHaveBeenCalledWith(true);
    });

    it('should return false and set Airship attribute when Push channel is not accepted', async () => {
      const consentData = [
        { channel: 'Push', accepted: false },
        { channel: 'Email', accepted: true },
      ];

      const result = await service.isSalesForcePushConsentAccepted(consentData);

      expect(result).toBe(false);
      expect(logger.info).toHaveBeenCalledWith(
        'SalesForce Push Notification Constent Acceptance',
        false,
      );
      expect(
        AirshipAnalyticsService.setSalesforcePushConsentAttribute,
      ).toHaveBeenCalledWith(false);
    });
  });
});
