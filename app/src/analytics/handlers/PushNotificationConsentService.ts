import Airship, {
  PermissionStatus,
  PushNotificationStatus,
} from '@ua/react-native-airship';
import { logger } from '@utils/logger';

import { AirshipAnalyticsService } from './airship';

export class PushNotificationConsentService {
  // Checks Native Permission for Push Notification
  public async isPushNotificationAllowedByUser(): Promise<boolean> {
    const currentNotificationConsent: PushNotificationStatus =
      await Airship.push.getNotificationStatus();
    const notificationPermissionStatus =
      currentNotificationConsent.notificationPermissionStatus;
    logger.info(
      'Device Push Notification Constent : ',
      notificationPermissionStatus,
    );
    return notificationPermissionStatus === PermissionStatus.Granted;
  }

  // Checks Salesforce Consent for Push Notification
  public async isSalesForcePushConsentAccepted(
    consentData: any,
  ): Promise<boolean> {
    const pushNotificationConstentAccepted = consentData.some(
      (consent: { channel: string; accepted: boolean }) =>
        consent.channel === 'Push' && consent.accepted === true,
    );
    logger.info(
      'SalesForce Push Notification Constent Acceptance',
      pushNotificationConstentAccepted,
    );

    // Set the custom attribute in Airship
    await AirshipAnalyticsService.setSalesforcePushConsentAttribute(
      pushNotificationConstentAccepted,
    );

    return pushNotificationConstentAccepted;
  }

  public async nativePermissionStatus(): Promise<PushNotificationStatus> {
    return await Airship.push.getNotificationStatus();
  }
}
