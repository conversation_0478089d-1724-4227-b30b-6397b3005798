import { DEFAULT_VALUES } from '@config/defaults/config.default';
import { DEFAULT_VALUES_UK } from '@config/defaults/countries/uk';

jest.mock('@env', () => {
  const getSupportedBrand = require('@utils/brandHelper').getSupportedBrand;

  return {
    get BPPAY_WALLET_MICROSITE() {
      return process.env.BPPAY_WALLET_MICROSITE;
    },
    get STAGE() {
      return process.env.STAGE;
    },
    get BRAND() {
      return getSupportedBrand();
    },
  };
});

describe('Test DEFAULT_VALUES', () => {
  const LOCAL_ENV_URL = 'LOCAL ENV URL';
  const DEFAULT_URL =
    DEFAULT_VALUES_UK.external_links.bppay_wallet_microsite_link;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  it('should use the local wallet microsite link when STAGE env variable is undefined and a local env value is provided', () => {
    process.env.STAGE = undefined;
    process.env.BPPAY_WALLET_MICROSITE = LOCAL_ENV_URL;

    expect(
      DEFAULT_VALUES.UK.external_links.bppay_wallet_microsite_link,
    ).toEqual(DEFAULT_URL);
    expect(
      DEFAULT_VALUES.UK.external_links.bppay_wallet_microsite_link,
    ).not.toEqual(LOCAL_ENV_URL);
  });

  it('should use the default wallet microsite link when STAGE env variable is defined', () => {
    process.env.STAGE = 'QA';
    process.env.BPPAY_WALLET_MICROSITE = LOCAL_ENV_URL;

    expect(
      DEFAULT_VALUES.UK.external_links.bppay_wallet_microsite_link,
    ).toEqual(DEFAULT_URL);
    expect(
      DEFAULT_VALUES.UK.external_links.bppay_wallet_microsite_link,
    ).not.toEqual(LOCAL_ENV_URL);
  });

  it('should use default wallet microsite link when STAGE env variable is undefined and env var url is undefined', () => {
    process.env.STAGE = undefined;
    delete process.env.BPPAY_WALLET_MICROSITE;

    expect(
      DEFAULT_VALUES.UK.external_links.bppay_wallet_microsite_link,
    ).toEqual(DEFAULT_URL);
  });

  it('should use default wallet microsite link when STAGE env variable is undefined and env var url is an empty string', () => {
    process.env.STAGE = undefined;
    process.env.BPPAY_WALLET_MICROSITE = '';

    expect(
      DEFAULT_VALUES.UK.external_links.bppay_wallet_microsite_link,
    ).toEqual(DEFAULT_URL);
  });
});
