import { wait } from './wait';

jest.spyOn(global, 'setTimeout');

describe('wait', () => {
  it('waits for 1000 by default', async () => {
    wait();
    expect(setTimeout).toHaveBeenCalledTimes(1);
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 1000);
  });

  it('waits for specified time', async () => {
    wait(2000);
    expect(setTimeout).toHaveBeenCalledTimes(2);
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 2000);
  });
});
