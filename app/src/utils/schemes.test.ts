import { ChargepointSchemeName } from '@common/enums';

import dcsSiteMock from '../mockResponses/DcsSite';
import hasToBeSiteMock from '../mockResponses/HasToBeSite';
import { getChargepointAnalyticsScheme, isUberScheme } from './schemes';

describe('check for uberScheme', () => {
  it('hasToBe includes uberScheme', () => {
    const results = isUberScheme(hasToBeSiteMock);
    expect(results).toEqual(true);
  });

  it('DCS does not include uberScheme', () => {
    const results = isUberScheme(dcsSiteMock);
    expect(results).toEqual(false);
  });
});

describe('getChargepointAnalyticsScheme', () => {
  it('should return bp Hub - Uber for a CP with Uber Scheme', () => {
    const result = getChargepointAnalyticsScheme(hasToBeSiteMock);
    expect(result).toEqual(ChargepointSchemeName.UBER);
  });

  it('should return View_WP_Royal Mail for a CP with Royal Mail Scheme', () => {
    // @ts-ignore
    hasToBeSiteMock.chargepoints[0].schemes = [
      { schemeId: 2271, schemeName: 'Royal Mail' },
    ];
    const result = getChargepointAnalyticsScheme(hasToBeSiteMock);
    expect(result).toEqual(ChargepointSchemeName.ROYAL_MAIL);
  });

  it('should return BMW for a CP with BMW Scheme', () => {
    // @ts-ignore
    hasToBeSiteMock.chargepoints[0].schemes = [
      { schemeId: 92, schemeName: 'BMW' },
    ];
    const result = getChargepointAnalyticsScheme(hasToBeSiteMock);
    expect(result).toEqual(ChargepointSchemeName.BMW);
  });

  it('should return Polar for a CP with Polar Scheme', () => {
    // @ts-ignore
    hasToBeSiteMock.chargepoints[0].schemes = [
      { schemeId: 16, schemeName: 'Polar' },
    ];
    const result = getChargepointAnalyticsScheme(hasToBeSiteMock);
    expect(result).toEqual(ChargepointSchemeName.POLAR);
  });
});
