import { analyticsEvent } from '@analytics';
import {
  ChargeSelectConnectorUnentitled,
  MapSiteUnentitled,
} from '@analytics/events/SiteBanner';
import { Provider, Site } from '@bp/map-mfe';

import {
  analyticsExtraParamsType,
  BannerType,
  triggerAnalyticEvent,
} from './siteBannerHelper';

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

describe('triggerAnalyticEvent function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should trigger ChargeSelectConnectorUnentitled event when mfeName is "charge"', () => {
    const { site, analyticsExtraParams } = getTestData(
      'charge',
      'Test Connector Type',
      'Test Apollo Internal ID',
    );

    triggerAnalyticEvent(BannerType.UberSiteBanner, analyticsExtraParams, site);

    expect(analyticsEvent).toHaveBeenCalledWith(
      ChargeSelectConnectorUnentitled({
        siteDetails: {
          siteProvider: site.provider,
          siteCountry: site.siteDetails.country,
          cpScheme: expect.any(String),
          cpOperator: site.cpo,
        },
        bannerType: BannerType.UberSiteBanner,
        connectorType: 'Test Connector Type',
        cpId: 'Test Apollo Internal ID',
      }),
    );
  });

  it('should trigger MapSiteUnentitled event when mfeName is "map"', () => {
    const { site, analyticsExtraParams } = getTestData(
      'map',
      'Test Connector Type',
      'Test Apollo Internal ID',
      true,
    );

    triggerAnalyticEvent(BannerType.UberSiteBanner, analyticsExtraParams, site);

    expect(analyticsEvent).toHaveBeenCalledWith(
      MapSiteUnentitled({
        siteDetails: {
          siteId: site.siteId,
          siteProvider: site.provider,
          siteCountry: site.siteDetails.country,
          cpScheme: expect.any(String),
          cpOperator: site.cpo,
        },
        bannerType: BannerType.UberSiteBanner,
        connectorType: 'Test Connector Type',
        isLocationServiceEnabled: true,
      }),
    );
  });

  it('should not trigger any event when mfeName is neither "charge" nor "map"', () => {
    const { site, analyticsExtraParams } = getTestData(
      'unknown',
      BannerType.UberSiteBanner,
    );

    triggerAnalyticEvent(BannerType.UberSiteBanner, analyticsExtraParams, site);

    expect(analyticsEvent).not.toHaveBeenCalled();
  });
});

const getTestData = (
  mfeName: string,
  connectorType?: string,
  apolloInternalId?: string,
  isLocationServicesEnabled?: boolean,
) => {
  const site: Site = {
    __typename: 'Site',
    fuels: [],
    brandName: null,
    isAds: false,
    isOpen24Hours: null,
    siteId: 'Test Site ID',
    provider: Provider.BPCM,
    siteDetails: {
      country: 'Test Country',
      hours: {
        __typename: undefined,
        mon1: undefined,
        mon2: undefined,
        tue1: undefined,
        tue2: undefined,
        wed1: undefined,
        wed2: undefined,
        thu1: undefined,
        thu2: undefined,
        fri1: undefined,
        fri2: undefined,
        sat1: undefined,
        sat2: undefined,
        sun1: undefined,
        sun2: undefined,
      },
      location: {
        __typename: undefined,
        lat: 0,
        lon: 0,
      },
    },
    cpo: 'Test CPO',
    hasEvCharging: false,
    hasFuel: false,
    services: [],
  };
  const analyticsExtraParams: analyticsExtraParamsType = {
    mfeName,
    connector: { type: connectorType },
    apolloInternalId,
    isLocationServicesEnabled,
  };
  return { site, analyticsExtraParams };
};
