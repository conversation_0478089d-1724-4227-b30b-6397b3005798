import { ProfileUserInfo } from '@bp/profile-mfe';
import { TagStatus } from '@bp/rfid-mfe/dist/common/enums';

export const getRfidError = (tagIds: ProfileUserInfo['tagIds']) => {
  const erroredTags = [TagStatus.SUPPLIER_REJECTED, TagStatus.SUPPLIER_FAILED];
  const hasRejectedTag = tagIds?.some(
    ({ tagTypeName, tagStatus }) =>
      tagTypeName === 'physical' &&
      erroredTags.includes(tagStatus as TagStatus),
  );
  const hasValidTag = tagIds?.some(
    ({ tagTypeName, tagStatus }) =>
      tagTypeName === 'physical' &&
      !erroredTags.includes(tagStatus as TagStatus),
  );
  return hasRejectedTag && !hasValidTag;
};
