import { USER_JOURNEY } from '@analytics/events/Uber/UnlinkAccounts';
import { Route } from '@react-navigation/native';

export type UserJourney = USER_JOURNEY;

interface RouteWithUserJourney extends Route<string> {
  params?: {
    userJourney?: User<PERSON><PERSON><PERSON>;
  };
}

export const getUserJourney = (
  route: RouteWithUserJourney,
  defaultJourney: UserJourney = USER_JOURNEY.UBER_INELIGIBILITY,
): UserJourney => {
  // Check if journey was passed as a route parameter
  if (route.params?.userJourney) {
    return route.params.userJourney;
  }

  // If no journey found, return default
  return defaultJourney;
};
