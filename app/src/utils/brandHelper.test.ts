import { SupportedBrand } from '@bp/pulse-shared-types/lib/enums/SupportedBrands';

import { getSupportedBrand } from './brandHelper';

describe('getSupportedBrand', () => {
  test('should return bp when lowercase bp is stored in env', () => {
    // Arrange
    const envConfig = 'bp';

    // Act
    const result = getSupportedBrand(envConfig);

    //Assert
    expect(result).toEqual(SupportedBrand.BP);
  });

  test('should return bp when uppercase BP is stored in .env', () => {
    // Arrange
    const envConfig = 'BP';

    // Act
    const result = getSupportedBrand(envConfig);

    //Assert
    expect(result).toEqual(SupportedBrand.BP);
  });

  test('should return Aral when lowercase aral is stored in .env', () => {
    // Arrange
    const envConfig = 'aral';

    // Act
    const result = getSupportedBrand(envConfig);

    //Assert
    expect(result).toEqual(SupportedBrand.ARAL);
  });

  test('should return Aral when uppercase ARAL is stored in .env', () => {
    // Arrange
    const envConfig = 'ARAL';

    // Act
    const result = getSupportedBrand(envConfig);

    //Assert
    expect(result).toEqual(SupportedBrand.ARAL);
  });

  test('should return bp when an invalid brand value is stored in .env', () => {
    // Arrange
    const envConfig = 'someotherbrand';

    // Act
    const result = getSupportedBrand(envConfig);

    //Assert
    expect(result).toEqual(SupportedBrand.BP);
  });
});
