import {
  ApolloLink,
  execute,
  FetchResult,
  gql,
  GraphQLRequest,
  Observable,
  Operation,
} from '@apollo/client';
import { SupportedCountries } from '@common/enums';
import { NavigationContainer } from '@react-navigation/native';
import { render } from '@testing-library/react-native';
import React, { ReactElement } from 'react';

import ThemeProvider from './../providers/ThemeProvider';

interface Props {
  children: React.ReactNode;
  country?: SupportedCountries;
}

const AllTheProviders = ({ children }: Props) => {
  return (
    <ThemeProvider>
      <NavigationContainer>{children}</NavigationContainer>
    </ThemeProvider>
  );
};

const customRender = (ui: ReactElement) =>
  render(ui, { wrapper: AllTheProviders });

export * from '@testing-library/react-native';
export { customRender as render };

const MockQuery = gql`
  query {
    thing
  }
`;

interface LinkResult<T> {
  operation: Operation;
  result: FetchResult<T>;
}

const DEFAULT_REQUEST: GraphQLRequest = { query: MockQuery };

export async function testApolloLink<T = any>(
  linkToTest: ApolloLink,
  mockRequest: () => Partial<GraphQLRequest> = () => DEFAULT_REQUEST,
  mockResponse: () => FetchResult = () => ({ data: null }),
) {
  const linkResult = {} as LinkResult<T>;

  return new Promise<LinkResult<T>>((resolve, reject) => {
    const terminatingLink = new ApolloLink(operation => {
      linkResult.operation = operation;
      return Observable.of(mockResponse());
    });

    execute(ApolloLink.from([linkToTest, terminatingLink]), {
      ...DEFAULT_REQUEST,
      ...mockRequest(),
    }).subscribe(
      result => {
        linkResult.result = result as FetchResult<T>;
      },
      error => {
        reject(error);
      },
      () => {
        resolve(linkResult);
      },
    );
  });
}
