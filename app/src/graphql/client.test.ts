import { Apollo<PERSON>lient, createHttpLink } from '@apollo/client';
import {
  createAbortLink,
  createAuthLink,
  createRequestLink,
  createResponseLink,
  createTimerLink,
  createXrayLink,
} from '@bp/mfe-helper-apollo';

import { createApolloClient } from './client';

jest.mock('@apollo/client');
jest.mock('@apollo/client/link/error');
jest.mock('@bp/mfe-helper-apollo');

describe('graphql/client', () => {
  it('should attach links', () => {
    const client = createApolloClient('apiURL', 'apiKey', undefined);

    expect(createHttpLink).toBeCalledTimes(1);
    expect(createAbortLink).toBeCalledTimes(1);
    expect(createAuthLink).toBeCalledTimes(1);
    expect(createRequestLink).toBeCalledTimes(1);
    expect(createResponseLink).toBeCalledTimes(1);
    expect(createTimerLink).toBeCalledTimes(1);
    expect(createXrayLink).toBeCalledTimes(1);
    expect(client).toBeInstanceOf(ApolloClient);
  });
});
