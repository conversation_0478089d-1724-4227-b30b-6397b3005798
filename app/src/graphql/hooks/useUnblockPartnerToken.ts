import { useApolloClient } from '@apollo/client';
import {
  UNBLOCK_PARTNER_TOKEN_MUTATION,
  UnblockPartnerTokenResult,
  UnblockPartnerTokenVariables,
} from '@graphql/mutations/unblockPartnerToken';
import { useCallback } from 'react';

export type UnblockPartnerTokenOptions = {
  variables: UnblockPartnerTokenVariables;
  abortController?: AbortController;
};

export const useUnblockPartnerToken = () => {
  const apolloClient = useApolloClient();

  return useCallback(
    (options: UnblockPartnerTokenOptions) => {
      return apolloClient.mutate<UnblockPartnerTokenResult>({
        variables: options.variables,
        mutation: UNBLOCK_PARTNER_TOKEN_MUTATION,
        fetchPolicy: 'no-cache',
        errorPolicy: 'ignore',
        context: {
          fetchOptions: {
            signal: options.abortController?.signal,
          },
        },
      });
    },
    [apolloClient],
  );
};
