import { analyticsEvent } from '@analytics';
import {
  AuthProvider,
  CIPScope,
  LoginAnalyticsEventType as AuthAnalyticsEventsType,
  SFConsent,
} from '@bp/pulse-auth-sdk';
import env from '@env';
import { navigate } from '@utils/navigation';
import React from 'react';

interface Props {
  children: React.ReactNode;
}

const Render = ({ children }: Props) => {
  const scopes = [
    CIPScope.OPEN_ID,
    CIPScope.EMAIL,
    CIPScope.PHONE,
    CIPScope.PROFILE,
    CIPScope.B2C_CONSENT,
    CIPScope.B2C_CONTACT,
    CIPScope.B2C_PROFILE,
  ];

  const onLoginFailure = () => {
    navigate('Error', {}, false);
  };

  const onAnalyticsEvent = (event: AuthAnalyticsEventsType) => {
    analyticsEvent(event);
  };

  const onRegistrationSuccess = () => {
    navigate('Registration', {}, true);
  };

  return (
    <AuthProvider
      baseUrl={env.CIP_BASE_URL}
      clientId={env.CIP_CLIENT_ID}
      callbackUrl={env.CIP_REDIRECT_URL}
      callbackUrlEmail={env.CIP_EMAIL_SUCCESS_REDIRECT_URL}
      scopes={scopes}
      msBaseUrl={env.CIP_IHUB_BASE_URL}
      onAnalyticsEvent={onAnalyticsEvent}
      onLoginFailure={onLoginFailure}
      onRegistrationSuccess={onRegistrationSuccess}
      requiredConsents={[SFConsent.EV_TERMS_AND_CONDITIONS]}>
      {children}
    </AuthProvider>
  );
};

export default Render;
