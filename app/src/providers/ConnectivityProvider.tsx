import { addEventListener } from '@react-native-community/netinfo';
import React, { createContext, useContext, useEffect, useState } from 'react';

export interface IConnectivityContext {
  isInternetReachable: boolean;
}

export const ConnectivityContext = createContext<IConnectivityContext>({
  isInternetReachable: true,
});
export const useConnectivity = (): IConnectivityContext =>
  useContext(ConnectivityContext);

const Render = ({ children }: { children: React.ReactNode }) => {
  const [isInternetReachable, setIsInternetReachable] = useState<boolean>(true);

  useEffect(() => {
    const unsubscribe = addEventListener(state => {
      if (state.isInternetReachable === null) {
        return;
      }
      if (state.isInternetReachable === true) {
        setIsInternetReachable(true);
      } else {
        setIsInternetReachable(false);
      }
    });
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <ConnectivityContext.Provider
      value={{
        isInternetReachable,
      }}>
      {children}
    </ConnectivityContext.Provider>
  );
};

export default Render;
