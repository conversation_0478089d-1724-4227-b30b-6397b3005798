import { useLanguage } from '@bp/profile-mfe';
import i18n from '@translations';
import React, { createContext, useContext, useEffect } from 'react';
import { I18nextProvider } from 'react-i18next';

interface HostAppTranslationsProviderProps {
  children?: React.ReactNode;
}

export const HostAppTranslations = createContext({});
export const useHostAppTranslations = () => useContext(HostAppTranslations);
const HostAppTranslationsProvider = ({
  children,
}: HostAppTranslationsProviderProps) => {
  const { language } = useLanguage();
  useEffect(() => {
    i18n.changeLanguage(language.replace('-', '_'));
  }, [language]);

  return (
    <HostAppTranslations.Provider value={{}}>
      <I18nextProvider i18n={i18n}>{children}</I18nextProvider>
    </HostAppTranslations.Provider>
  );
};

export default HostAppTranslationsProvider;
