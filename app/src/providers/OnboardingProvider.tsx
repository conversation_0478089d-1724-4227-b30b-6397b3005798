import { analyticsEvent } from '@analytics';
import { useGuestPay } from '@bp/guest_feature-mfe';
import { OnboardingProvider } from '@bp/onboarding-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import env from '@env';
import { logger } from '@utils/logger';
import React, { useMemo } from 'react';

interface Props {
  children?: React.ReactNode;
}

const Render = ({ children }: Props) => {
  const { user, authenticated, getAccessToken } = useAuth();

  const { authenticated: guestAuthenticated } = useGuestPay();

  const getApiURL = useMemo(() => {
    if (authenticated || guestAuthenticated) {
      return env.GATEWAY_URL_PRIVATE;
    }
    return env.GATEWAY_URL_PUBLIC;
  }, [authenticated, guestAuthenticated]);

  return (
    <OnboardingProvider
      apiURL={getApiURL}
      apiKey={env.API_GATEWAY_KEY}
      user={{
        userId: user?.userId,
        authenticated: !!authenticated,
      }}
      getToken={getAccessToken}
      onError={() => logger.warn('Onboarding MFE: Error onboarding user')}
      analytics={analyticsEvent}>
      {children}
    </OnboardingProvider>
  );
};

export default Render;
