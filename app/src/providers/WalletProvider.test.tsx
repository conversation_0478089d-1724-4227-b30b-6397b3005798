import { MockedProvider } from '@apollo/client/testing';
import { ChargeScreenNamesToNavigate } from '@common/enums';
import { render } from '@testing-library/react-native';
import { navigate } from '@utils/navigation';
import React from 'react';
import { Text } from 'react-native';

import {
  onExit,
  onMySubsPress,
  onNavigateToCharge,
  onPaymentComplete,
  onPressOverdrawnModal,
  WalletProvider,
} from './WalletProvider';

jest.mock('@utils/navigation', () => ({
  navigate: jest.fn(),
  navigation: {
    addListener: jest.fn(),
  },
}));

jest.mock('@bp/mfe-subscription', () => ({
  SubscriptionScreenNames: {
    MySubscription: 'MySubscription',
  },
}));

jest.mock('@bp/charge-mfe', () => ({
  ChargeScreenNames: {
    SelectConnector: 'SelectConnector',
  },
}));

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

jest.mock('./ConnectivityProvider', () => ({
  useConnectivity: () => ({ isInternetReachable: true }),
}));

jest.mock('@bp/pulse-auth-sdk', () => ({
  useAuth: () => ({
    user: { userId: 'test-user' },
    authenticated: true,
    getAccessToken: jest.fn(),
  }),
}));

jest.mock('@bp/profile-mfe', () => ({
  useLanguage: () => ({ language: 'en' }),
  useAppSettings: () => ({ userInfo: { userType: 'PAYG-WALLET' } }),
}));

jest.mock('@providers/ConfigProvider', () => ({
  useConfig: () => ({
    external_links: { bppay_wallet_microsite_link: 'https://example.com' },
    profile_mfe: { pulseMembership: true },
  }),
}));

jest.mock('@bp/onboarding-mfe', () => ({
  useOnboarding: () => ({ onboardingStatus: { country: 'UK' } }),
}));

jest.mock('@env', () => ({
  GATEWAY_URL_PRIVATE: 'https://api.example.com',
  API_GATEWAY_KEY: 'test-api-key',
  BRAND: 'TestBrand',
}));

interface WalletContextProviderProps {
  children: React.ReactNode;
}

jest.mock('@bp/bppay-wallet-feature', () => ({
  WalletContextProvider: ({ children }: WalletContextProviderProps) => (
    <>{children}</>
  ),
}));

describe('WalletProvider callback functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('onMySubsPress navigates to Subscription screen', () => {
    onMySubsPress();
    expect(navigate).toHaveBeenCalledWith(
      'Subscription',
      { screen: 'MySubscription' },
      false,
    );
  });

  test('onPaymentComplete navigates to ChargeActivity screen', () => {
    onPaymentComplete();
    expect(navigate).toHaveBeenCalledWith('ChargeActivity', {}, false);
  });

  test('onPressOverdrawnModal navigates to ChargeActivity screen', () => {
    onPressOverdrawnModal();
    expect(navigate).toHaveBeenCalledWith('ChargeActivity', {}, false);
  });

  test('onExit navigates to Profile screen', () => {
    onExit();
    expect(navigate).toHaveBeenCalledWith('Profile', {}, false);
  });

  test('onNavigateToCharge navigates to Charge screen', () => {
    onNavigateToCharge(ChargeScreenNamesToNavigate.ConnectVehicle);
    expect(navigate).toHaveBeenCalledWith(
      'Charge',
      { screen: ChargeScreenNamesToNavigate.ConnectVehicle },
      false,
    );
  });
});

describe('WalletProvider', () => {
  const TestComponent = () => {
    return (
      <MockedProvider>
        <WalletProvider>
          <Text testID="testComponent">{'WalletProvider'}</Text>
        </WalletProvider>
      </MockedProvider>
    );
  };

  it('renders correctly', () => {
    const { getByTestId } = render(<TestComponent />);
    expect(getByTestId('testComponent')).toBeTruthy();
  });
});
