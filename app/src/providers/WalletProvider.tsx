import { analyticsEvent } from '@analytics';
import { WalletContextProvider } from '@bp/bppay-wallet-feature';
import { SubscriptionScreenNames } from '@bp/mfe-subscription';
import { useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings, useLanguage } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { ChargeScreenNamesToNavigate, SupportedUserTypes } from '@common/enums';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import { navigate, navigation } from '@utils/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useConnectivity } from './ConnectivityProvider';

interface Props {
  children?: React.ReactNode;
}

export const onMySubsPress = () => {
  navigate(
    'Subscription',
    { screen: SubscriptionScreenNames.MySubscription },
    false,
  );
};

export const onPaymentComplete = () => {
  navigate('ChargeActivity', {}, false);
};

export const onPressOverdrawnModal = () => {
  navigate('ChargeActivity', {}, false);
};

export const onExit = () => {
  navigate('Profile', {}, false);
};

export const onNavigateToCharge = (
  screenToNavigateTo: ChargeScreenNamesToNavigate,
) => {
  navigate('Charge', { screen: screenToNavigateTo }, false);
};

export const WalletProvider = ({ children }: Props) => {
  const { isInternetReachable } = useConnectivity();
  const { user, authenticated, getAccessToken } = useAuth();
  const { language } = useLanguage();
  const [subsFlag, setSubsFlag] = useState<boolean>(false);
  const { userInfo } = useAppSettings();

  const {
    external_links: externalLinks,
    profile_mfe: { pulseMembership },
  } = useConfig();
  const {
    onboardingStatus: { country },
  } = useOnboarding();

  const micrositeUrl = useMemo(() => {
    return __DEV__
      ? env.BPPAY_WALLET_MICROSITE
      : externalLinks.bppay_wallet_microsite_link;
  }, [externalLinks.bppay_wallet_microsite_link]);

  useEffect(() => {
    if (navigation) {
      return navigation.addListener('state', e => {
        const state = e.data.state;
        const routes = state?.routes;
        const index = state?.index;
        if (routes && index && routes[index].name === 'Subscription') {
          setSubsFlag(true);
        } else {
          setSubsFlag(false);
        }
      });
    }
    return undefined;
  }, []);

  const userId = user?.userId ?? '';
  const loggedInUser = !!authenticated && !!country;
  const isWalletUser =
    userInfo?.userType === SupportedUserTypes['PAYG-WALLET'] ||
    userInfo?.userType === SupportedUserTypes['SUBS-WALLET'];
  const homeCountry = country || 'UK';

  const onMySubsPressCb = useCallback(onMySubsPress, []);
  const onPaymentCompleteCb = useCallback(onPaymentComplete, []);
  const onPressOverdrawnModalCb = useCallback(onPressOverdrawnModal, []);
  const onExitCb = useCallback(onExit, []);
  return (
    <WalletContextProvider
      apiUrl={env.GATEWAY_URL_PRIVATE}
      apiKey={env.API_GATEWAY_KEY}
      micrositeUrl={micrositeUrl}
      pulseMembership={pulseMembership}
      userId={userId}
      country={homeCountry}
      locale={language}
      isInternetReachable={isInternetReachable}
      navigation={navigation}
      authenticated={loggedInUser && isWalletUser}
      getToken={getAccessToken}
      onPaymentCompleted={onPaymentCompleteCb}
      overdrawnModalOnClick={onPressOverdrawnModalCb}
      onMySubsPress={onMySubsPressCb}
      onExit={onExitCb}
      onNavigateToChargeSpecificScreen={(
        screenToNavigateTo: ChargeScreenNamesToNavigate,
      ) => onNavigateToCharge(screenToNavigateTo)}
      externalLinks={externalLinks}
      onAnalyticsEvent={analyticsEvent}
      brand={env.BRAND}
      subsFlag={subsFlag}>
      {children}
    </WalletContextProvider>
  );
};

export default WalletProvider;
