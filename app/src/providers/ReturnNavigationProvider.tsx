import { logger } from '@utils/logger';
import { navigate } from '@utils/navigation';
import React, {
  createContext,
  Dispatch,
  SetStateAction,
  useContext,
  useState,
} from 'react';

interface ReturnParams {
  screen: string;
  params: any;
}

interface IReturnNavigationContext {
  returnParams: ReturnParams | undefined;
  setReturnParams: Dispatch<SetStateAction<ReturnParams | undefined>>;
  returnNavigate: () => void;
}

export const ReturnNavigationContext = createContext<IReturnNavigationContext>({
  returnParams: undefined,
  returnNavigate: () => {},
  setReturnParams: () => {},
});

export const useReturnNavigation = (): IReturnNavigationContext =>
  useContext(ReturnNavigationContext);

export function ReturnNavigationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [returnParams, setReturnParams] = useState<ReturnParams | undefined>();

  const returnNavigate = () => {
    if (returnParams) {
      navigate(returnParams.screen, returnParams.params);
      setReturnParams(undefined);
    } else {
      logger.error('No return params set');
      navigate('Error', {}, false);
    }
  };

  return (
    <ReturnNavigationContext.Provider
      value={{
        returnParams,
        setReturnParams,
        returnNavigate,
      }}>
      {children}
    </ReturnNavigationContext.Provider>
  );
}
