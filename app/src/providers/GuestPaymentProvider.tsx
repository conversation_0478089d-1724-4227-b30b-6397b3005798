import { analyticsEvent } from '@analytics';
import { GuestPayProvider } from '@bp/guest_feature-mfe';
import { useLanguage } from '@bp/profile-mfe';
import env from '@env';
import { logger } from '@utils/logger';
import React from 'react';

interface Props {
  children?: React.ReactNode;
}

const Render = ({ children }: Props) => {
  const { language } = useLanguage();
  return (
    <GuestPayProvider
      onAnalyticsEvent={analyticsEvent}
      // @ts-ignore
      language={language}
      micrositeUrl={env.BPPAY_GUEST_MICROSITE}
      tokenEndpoint={env.GUEST_AUTH_URL}
      tokenApiKey={env.GUEST_AUTH_KEY}
      apiUrl={env.GATEWAY_URL_PRIVATE}
      apiKey={env.API_GATEWAY_KEY}
      analytics={() => {}}
      onPreauthCompleted={() => logger.log('Handle preauth completed...')}
      onPreauthFailed={() => logger.log('<PERSON><PERSON> preauth failed...')}>
      {children}
    </GuestPayProvider>
  );
};

export default Render;
