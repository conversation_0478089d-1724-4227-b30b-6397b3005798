import { ChargeStateContextProvider, ChargeUserType } from '@bp/charge-mfe';
import { useGuestPay } from '@bp/guest_feature-mfe';
import { useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings, useLanguage } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { PaymentMethod } from '@common/interfaces';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import {
  useGuestChargeProviders,
  useGuestPriceProviders,
} from '@screens/Map/hooks';
import { findSupportedCountry } from '@utils/chargeSupportedCountries';
import React, { useCallback, useMemo } from 'react';

export interface ChargeStateProviderProps {
  children?: React.ReactNode;
}

const ChargeStateProvider = ({ children }: ChargeStateProviderProps) => {
  const { authenticated, user, getAccessToken } = useAuth();
  const { userInfo } = useAppSettings();
  const { language } = useLanguage();
  const {
    charge_mfe: featureFlags,
    app_shell: {
      enableSoftOnboarding,
      es_joint_venture_operator,
      minimum_credit = 10,
    },
  } = useConfig();
  const { getGuestToken, authenticated: guestAuthenticated } = useGuestPay();
  const {
    onboardingStatus: { country },
  } = useOnboarding();

  const loggedInUser = !!authenticated && !!country;

  const homeCountry = findSupportedCountry(country);

  const apiURL = useMemo(() => {
    if (loggedInUser || guestAuthenticated) {
      return env.GATEWAY_URL_PRIVATE;
    }

    return env.GATEWAY_URL_PUBLIC;
  }, [loggedInUser, guestAuthenticated]);

  const getToken = useCallback(() => {
    if (loggedInUser) {
      return getAccessToken();
    }
    if (guestAuthenticated) {
      return getGuestToken();
    }
    return null;
  }, [loggedInUser, guestAuthenticated, getAccessToken, getGuestToken]);

  const guestChargeProviders = useGuestChargeProviders();
  const guestPriceProviders = useGuestPriceProviders();

  const userInfoMemo = useMemo(() => {
    return {
      userId: user?.userId,
      loggedIn: loggedInUser,
      guestCharging: !loggedInUser && guestAuthenticated,
      getToken: getToken,
      schemes: userInfo.userScheme ?? undefined,
      userBalance: userInfo.userBalance ?? undefined,
      userType: userInfo.userType as ChargeUserType,
      originEntitlementId: userInfo.Origin_Entitlement_ID,
      chargepointsAvailable: userInfo.Chargepoints_Available ?? [],
      paymentMethods: userInfo.Payment_Methods as Array<PaymentMethod>,
      rfidEnabled: userInfo.RFID_Enabled,
      addressLineOne: userInfo.address?.addressLine1,
      addressPostCode: userInfo.address?.postCode,
    };
  }, [
    getToken,
    guestAuthenticated,
    loggedInUser,
    user?.userId,
    userInfo.userBalance,
    userInfo.Chargepoints_Available,
    userInfo.Origin_Entitlement_ID,
    userInfo.Payment_Methods,
    userInfo.RFID_Enabled,
    userInfo.userScheme,
    userInfo.userType,
    userInfo.address,
  ]);

  return (
    <ChargeStateContextProvider
      country={homeCountry}
      brand={env.BRAND}
      locale={language}
      apiKey={env.API_GATEWAY_KEY}
      apiURL={apiURL}
      featureFlags={{
        ...featureFlags,
        enable_soft_onboarding_check: enableSoftOnboarding,
        joint_venture_operator_name: es_joint_venture_operator,
        minimum_credit,
      }}
      guestChargeProviders={guestChargeProviders}
      guestPriceProviders={guestPriceProviders}
      userInfo={userInfoMemo}>
      {children}
    </ChargeStateContextProvider>
  );
};

export default ChargeStateProvider;
