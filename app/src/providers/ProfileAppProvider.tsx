import { analyticsEvent } from '@analytics';
import { useOnboarding } from '@bp/onboarding-mfe';
import {
  AppContextProvider as AppContextProviderProps,
  ProfileAppContextProvider,
} from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import React, { useMemo } from 'react';

export type ProfileAppProviderProps = {
  children: React.ReactNode;
};

const ProfileAppProvider = ({ children }: ProfileAppProviderProps) => {
  const { app_shell } = useConfig();
  const { authenticated, user, getAccessToken } = useAuth();
  const {
    onboardingStatus: { country },
  } = useOnboarding();

  const homeCountry = country ?? 'UK';

  const userInfo = useMemo<AppContextProviderProps['userInfo']>(() => {
    const userAddress =
      user?.addressLine1 && user.postCode
        ? {
            addressLine1: user?.addressLine1 ?? '',
            addressLine2: user?.addressLine2 ?? '',
            city: user?.city ?? '',
            postCode: user?.postCode ?? '',
            state: user?.state ?? '',
            country: user?.country ?? '',
          }
        : undefined;

    return {
      userId: user?.userId,
      authenticated: !!authenticated && !!country,
      familyName: user?.lastName ?? '',
      givenName: user?.firstName ?? '',
      email: user?.email ?? '',
      phoneNumber: user?.phoneNumber ?? '',
      phoneNumberVerified: user?.phoneNumberVerified,
      address: userAddress,
    };
  }, [
    user?.userId,
    user?.lastName,
    user?.firstName,
    user?.email,
    user?.phoneNumber,
    user?.phoneNumberVerified,
    user?.addressLine1,
    user?.addressLine2,
    user?.city,
    user?.postCode,
    user?.state,
    user?.country,
    authenticated,
    country,
  ]);

  return (
    <ProfileAppContextProvider
      apiKey={env.API_GATEWAY_KEY}
      apiURL={env.GATEWAY_URL_PRIVATE}
      country={homeCountry}
      userInfo={userInfo}
      getToken={getAccessToken}
      analytics={analyticsEvent}
      brand={env.BRAND}
      enableCustomerMigration={app_shell.enable_customer_migration}>
      {children}
    </ProfileAppContextProvider>
  );
};

export default ProfileAppProvider;
