import styled from 'styled-components/native';

type CountrySelectionViewProps = {
  isBorder: boolean;
};

export const CountryLabelCard = styled.TouchableOpacity<CountrySelectionViewProps>`
  background-color: rgb(255, 255, 255);
  shadow-color: rgb(35, 35, 35);
  shadow-offset: 0px 4px;
  shadow-opacity: 0.08;
  shadow-radius: 4px;
  border-radius: 2px;
  flex-direction: row;
  margin: 0px 30px 16px 30px;
  padding: 15px 16px 15px 16px;
  justify-content: space-between;
  border-width: 1px;
  border-color: ${({ isBorder }: { isBorder: boolean }) =>
    isBorder ? '#111111' : '#1111111a'};
`;

export const Container = styled.View`
  flex: 1;
  flex-direction: row;
  align-items: center;
`;
export const ImageContainer = styled.View`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 32px 45px 28px 45px;
`;
export const CountryLabel = styled.Text`
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.1px;
  color: rgb(17, 17, 17);
  margin-left: 14px;
  flex-shrink: 1;
  flex-wrap: wrap;
`;

export const CountryIcon = styled.Text`
  min-height: 20px;
  margin-top: 5px;
`;

export const TextWrapper = styled.View`
  flex-shrink: 1;
  flex: 1;
  justify-content: center;
`;

export const RadioButtonWrapper = styled.View`
  justify-content: center;
`;
