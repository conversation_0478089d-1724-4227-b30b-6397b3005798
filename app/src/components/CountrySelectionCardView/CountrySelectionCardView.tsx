import { RadioButton } from '@bp/ui-components/mobile/core';
import { useHelpPageCountries } from '@config/config.hooks';
import React from 'react';

import * as S from './CountrySelectionCardView.styles';
import {
  CountryArray,
  CountrySelectionCardViewProps,
} from './CountrySelectionCardView.types';

const CountrySelectionCardView = ({
  selectedCountry,
  setSelectedCountry,
}: CountrySelectionCardViewProps) => {
  const countryCodes = useHelpPageCountries();

  const countries = CountryArray.filter(c =>
    countryCodes.includes(c.countryCode),
  );

  return (
    <>
      {countries.map(countryItem => {
        return (
          <S.CountryLabelCard
            key={countryItem.countryName}
            isBorder={selectedCountry === countryItem.countryName}
            onPress={() => setSelectedCountry(countryItem.countryCode)}
            disabled={selectedCountry === countryItem.countryCode}
            accessibilityLabel={countryItem.countryName}
            accessibilityHint={countryItem.countryName}
            testID={`${countryItem.countryName}CountryLabelCardView`}>
            <S.Container>
              <S.CountryIcon>{countryItem.countryIcon}</S.CountryIcon>
              <S.TextWrapper>
                <S.CountryLabel>{countryItem.countryName}</S.CountryLabel>
              </S.TextWrapper>
            </S.Container>
            <S.RadioButtonWrapper>
              <RadioButton
                selected={selectedCountry === countryItem.countryCode}
                onPress={() => setSelectedCountry(countryItem.countryCode)}
                disabled={selectedCountry === countryItem.countryCode}
                accessibilityLabel={countryItem.countryCode}
                accessibilityHint={countryItem.countryCode}
                testID={`CountrySelectionCard.RadioButton.${countryItem.countryCode}`}
              />
            </S.RadioButtonWrapper>
          </S.CountryLabelCard>
        );
      })}
    </>
  );
};

export default CountrySelectionCardView;
