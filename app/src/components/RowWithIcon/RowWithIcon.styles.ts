import styled from 'styled-components/native';

export const Title = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
  font-size: 16px;
  letter-spacing: 0.1px;
  line-height: 28px;

  padding: 0 24px 16px 24px;
`;

export const TableRow = styled.View`
  flex-direction: row;
  align-items: flex-start;
  padding: 0 16px;
  margin-left: 10px;
`;

export const LeftImageView = styled.View<{ type?: string }>`
  width: 20px;
  margin-right: 15px;
  margin-top: 3px;
`;

export const WrapperText = styled.Text`
  padding: 0 40px 12px 0;
`;

export const TableTitle = styled.Text`
  text-align: left;
  font-size: 16px;
  font-weight: bold;
  line-height: 28px;
  color: #111111;
  font-family: Roboto;
  letter-spacing: 0.1px;
`;
export const TableSubTitle = styled.Text`
  text-align: left;
  font-family: Roboto;
  font-size: 13px;
  line-height: 23px;
  color: #111111;
  letter-spacing: 0.2px;
`;
export const LinkText = styled.Text`
  color: rgb(0, 100, 204);
  text-decoration-line: underline;
  font-size: 13px;
  line-height: 24px;
  font-family: Roboto;
`;
