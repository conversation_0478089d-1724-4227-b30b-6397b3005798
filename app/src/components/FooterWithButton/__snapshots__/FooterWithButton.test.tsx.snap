// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FooterWithButton Should render properly and match snapshot 1`] = `
<View
  style={
    {
      "backgroundColor": "white",
      "paddingBottom": 24,
      "paddingLeft": 24,
      "paddingRight": 24,
      "paddingTop": 24,
      "shadowColor": "grey",
      "shadowOffset": {
        "height": 50,
        "width": 0,
      },
      "shadowOpacity": 1,
      "shadowRadius": 40,
      "width": "100%",
    }
  }
>
  <View
    accessibilityHint="AccessibilityHint"
    accessibilityLabel="AccessibilityLabel"
    accessibilityRole="button"
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": false,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#000096",
        "borderBottomLeftRadius": 28,
        "borderBottomRightRadius": 28,
        "borderColor": "transparent",
        "borderStyle": "solid",
        "borderTopLeftRadius": 28,
        "borderTopRightRadius": 28,
        "borderWidth": 0,
        "justifyContent": "center",
        "minHeight": 56,
        "opacity": 1,
        "paddingHorizontal": 29.5,
        "paddingVertical": 15,
      }
    }
    testID="button"
  >
    <View>
      <Text
        disabled={false}
        inverted={false}
        size="xlarge"
        style={
          {
            "color": "#ffffff",
            "fontFamily": "Roboto-Regular",
            "fontSize": 16,
            "letterSpacing": 0.7,
            "textAlign": "center",
          }
        }
        type="primary"
      >
        button text
      </Text>
    </View>
  </View>
  <View
    style={
      {
        "alignSelf": "center",
        "backgroundColor": "rgb(255, 255, 255)",
        "flexDirection": "'column'",
        "justifyContent": "center",
        "paddingBottom": 20,
        "paddingLeft": 20,
        "paddingRight": 20,
        "paddingTop": 20,
      }
    }
    testID="footerData"
  >
    <Text
      style={
        {
          "color": "rgba(17, 17, 17, 0.7)",
          "fontFamily": "Roboto-Regular",
          "fontSize": 13,
          "letterSpacing": 0.2,
          "lineHeight": 18,
          "paddingTop": 10,
          "textAlign": "center",
        }
      }
    >
      Footer msg
      <Text
        onPress={[MockFunction]}
        style={
          {
            "color": "rgb(0, 100, 204)",
          }
        }
        testID="linkButton"
      >
         
        Footer link text
      </Text>
    </Text>
  </View>
</View>
`;
