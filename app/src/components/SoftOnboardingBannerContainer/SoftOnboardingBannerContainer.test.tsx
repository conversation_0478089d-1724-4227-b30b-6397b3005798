import { RemoteConfig } from '@config/config.types';
import { DEFAULT_VALUES } from '@config/defaults/config.default';
import { render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Platform } from 'react-native';

import SoftOnboardingBannerContainer from './SoftOnboardingBannerContainer';

const mockcfg = { ...DEFAULT_VALUES.DE };
mockcfg.app_shell.enableSoftOnboarding = true;
const mockConfigt = jest.fn().mockReturnValue(mockcfg);
jest.mock('@providers/ConfigProvider', () => ({
  useConfig: () => mockConfigt(),
}));

const mockCurrentRoute = jest
  .fn()
  .mockReturnValue({ name: 'ProfileMFE.Profile' });
jest.mock('@utils/navigation', () => {
  const originalModule = jest.requireActual('@utils/navigation');
  return {
    ...originalModule,
    navigate: jest.fn(),
    navigation: {
      getCurrentRoute: () => mockCurrentRoute(),
      isReady: () => true,
    },
  };
});

jest.mock('react-native/Libraries/Utilities/Platform', () => {
  let platform = {
    OS: 'ios',
    select: () => {},
  };

  const select = jest.fn().mockImplementation(obj => {
    const value = obj[platform.OS];
    return !value ? obj.default : value;
  });

  platform.select = select;

  return platform;
});

const renderComponent = () => render(<SoftOnboardingBannerContainer />);

describe('<SoftOnboardingBannerContainer />', () => {
  let cfg: RemoteConfig;

  beforeEach(() => {
    jest.clearAllMocks();
    cfg = DEFAULT_VALUES.DE;
  });

  it('renders SoftOnboardingBannerContainer component for iOS platform', async () => {
    Platform.OS = 'ios';
    cfg.app_shell.enableSoftOnboarding = true;
    mockConfigt.mockImplementation(() => cfg);

    await waitFor(() => {
      const { toJSON } = renderComponent();
      expect(toJSON()).not.toBeNull();
    });
  });

  it('renders SoftOnboardingBannerContainer component for android platform', async () => {
    Platform.OS = 'android';
    cfg.app_shell.enableSoftOnboarding = true;
    mockConfigt.mockImplementation(() => cfg);

    await waitFor(() => {
      const { toJSON } = renderComponent();
      expect(toJSON()).not.toBeNull();
    });
  });

  it('when user as guest user Banner component should not visible', async () => {
    cfg = DEFAULT_VALUES.GUEST;
    cfg.app_shell.enableSoftOnboarding = false;
    mockConfigt.mockImplementation(() => cfg);

    Platform.OS = 'ios';

    await waitFor(() => {
      const { toJSON } = renderComponent();
      expect(toJSON()).toBeNull();
    });
  });

  it('when a user selects NL, Banner component should not visible', async () => {
    cfg = DEFAULT_VALUES.NL;
    cfg.app_shell.enableSoftOnboarding = false;
    mockConfigt.mockImplementation(() => cfg);

    Platform.OS = 'ios';

    await waitFor(() => {
      const { toJSON } = renderComponent();
      expect(toJSON()).toBeNull();
    });
  });
});
