import { s, vs } from '@utils/scale';
import styled from 'styled-components/native';

interface TopBorderProps {
  topBorderWidth?: string;
}

interface TextProps {
  color?: string;
  align?: string;
  alignSelf?: string;
}

interface HorizontalWrapperProps {
  flex?: number;
  flexDirection?: string;
}

export const SectionRowWrapper = styled.View<TopBorderProps>`
  background-color: rgb(255, 255, 255);
  flex-direction: row;
  border-bottom-width: 1px;
  border-color: rgb(222, 222, 222);
  border-top-width: ${({ topBorderWidth }: TopBorderProps) => topBorderWidth}px;
`;

export const Label = styled.Text<TextProps>`
  font-size: ${s(16)}px;
  line-height: ${s(24)}px;
  letter-spacing: ${s(0.7)}px;
  text-align: ${({ align }: TextProps) => align};
  align-self: ${({ alignSelf }: TextProps) => alignSelf};
  color: ${({ color }: TextProps) => color};
`;

export const SectionHorizontalWrapper = styled.View<HorizontalWrapperProps>`
  flex-direction: ${({ flexDirection }: HorizontalWrapperProps) =>
    flexDirection};
  flex: ${({ flex }: HorizontalWrapperProps) => flex};
  margin-top: ${s(32)}px;
  margin-bottom: ${s(32)}px;
  margin-left: ${vs(24)}px;
`;
