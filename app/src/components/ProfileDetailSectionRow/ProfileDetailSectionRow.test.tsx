import { render } from '@testing-library/react-native';
import React from 'react';

import ProfileDetailSectionRow from './ProfileDetailSectionRow';

describe('ChargeDetail component', () => {
  it('renders without crashing', () => {
    const profileDetailSectionRow = render(
      <ProfileDetailSectionRow
        text="FirstName"
        subText="last"
        topElement
        blueSubText={false}
      />,
    );
    expect(profileDetailSectionRow.toJSON()).toMatchSnapshot();
  });

  it('renders with testID', () => {
    const { getByTestId } = render(
      <ProfileDetailSectionRow
        text="FirstName"
        subText="last"
        topElement
        blueSubText={false}
      />,
    );
    expect(getByTestId('last')).toBeTruthy();
  });
});
