import { BpPulsUbarIcon, CombinedShape } from '@assets/images';
import React from 'react';
import { ViewStyle } from 'react-native';
import { SvgProps } from 'react-native-svg';

import * as S from './ImageWithTitles.styles';

export interface data {
  bgColor?: string;
  heading?: string;
  subheading?: string;
  Icon?: React.FC<SvgProps>;
  IconStyle?: ViewStyle;
  isUberIcon?: boolean;
}

export default function ImageWithTitles(props: data) {
  const { bgColor, heading, subheading, Icon, IconStyle, isUberIcon } = props;

  return (
    <S.ContentWrapper bgColor={bgColor}>
      <S.HeaderContainer>
        <S.HeaderContainerLeft>
          <CombinedShape />
        </S.HeaderContainerLeft>
        <S.HeaderContainerRight>
          <S.HeaderContainerRightTop>
            {heading && (
              <S.Title>
                {heading}
                {subheading && <S.TitleRight>{subheading}</S.TitleRight>}
              </S.Title>
            )}
          </S.HeaderContainerRightTop>
          {isUberIcon && (
            <S.HeaderContainerRightBottom>
              <BpPulsUbarIcon />
            </S.HeaderContainerRightBottom>
          )}
        </S.HeaderContainerRight>
      </S.HeaderContainer>
      <S.SubHeaderContainer>
        {Icon && (
          <S.ImageDrawContainer style={IconStyle}>
            <Icon />
          </S.ImageDrawContainer>
        )}
      </S.SubHeaderContainer>
    </S.ContentWrapper>
  );
}
