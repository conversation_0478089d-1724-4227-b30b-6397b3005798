import styled from 'styled-components/native';

type ContentWrapperProps = {
  bgColor?: string;
};

export const ContentWrapper = styled.ScrollView<ContentWrapperProps>`
  flex: 1;
  width: 100%;
  background-color: ${({ bgColor }) => bgColor || '#ffffff'};
`;
export const HeaderContainer = styled.View`
  flex: 1;
  background-color: #000096;
  display: flex;
  flex-direction: row;
  padding: 19px;
  margin: 34px 25px -1px 25px;
  border-radius: 38.3px 38.3px 38.3px 0px;
`;

export const SubHeaderContainer = styled.View`
  flex: 1;
  display: flex;
  flex-direction: row;
  margin-left: 25px;
`;

export const HeaderContainerLeft = styled.View`
  display: flex;
  padding-right: 16px;
`;
export const HeaderContainerRight = styled.View`
  flex: 0.9;
  display: flex;
  flex-direction: column;
  margin-top: 5px;
`;
export const HeaderContainerRightTop = styled.View`
  padding-right: 6px;
  flex-direction: row;
`;
export const HeaderContainerRightBottom = styled.View`
  margin-top: 20.7px;
  flex-direction: row;
`;
export const LineDrawContainer = styled.View`
  flex: 0.5;
`;
export const ImageDrawContainer = styled.View`
  flex: 1;
  display: flex;
  width: 100%;
  height: 100%;
`;
export const ImageStyled = styled.Image`
  width: 100%;
  height: 100%;
  resize-mode: contain;
`;
export const Line1 = styled.View`
  width: 5px;
  height: 22.5px;
  background-color: #000096;
  border-radius: 0px 0px 0px 90px;
`;
export const Title = styled.Text`
  font-family: 'Roboto-Bold';
  font-size: 30.6px;
  line-height: 33.4px;
  letter-spacing: 0.32px;
  color: #ffffff;
`;
export const TitleRight = styled.Text`
  font-family: 'Roboto-Bold';
  font-size: 30.6px;
  line-height: 33.4px;
  letter-spacing: 0.32px;
  color: #9bff00;
`;
