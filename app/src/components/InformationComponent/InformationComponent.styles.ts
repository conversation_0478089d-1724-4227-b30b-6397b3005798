import styled from 'styled-components/native';

export const Container = styled.View`
  border-radius: 4px;
  border-top-width: 4px;
  border-top-color: #a0d683;
  border-left-width: 1px;
  border-left-color: #e8e8e8;
  border-right-width: 1px;
  border-right-color: #e8e8e8;
  border-bottom-width: 1px;
  border-bottom-color: #e8e8e8;
  display: flex;
  flex-direction: column;
  shadow-color: #000000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 1;
  background-color: #fff;
  padding: 16px;
  margin-top: 8px;
`;

export const Title = styled.Text`
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.7px;
  color: #111111;
`;

export const TitleWrapper = styled.View`
  display: flex;
  flex-direction: row;
  margin-bottom: 4px;
  align-items: center;
`;

export const Description = styled.Text`
  font-family: 'Roboto';
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0.6px;
  color: #171717cc;
`;

export const DCSDescriptionWrapper = styled.View`
  display: flex;
  margin-top: 8px;
`;
