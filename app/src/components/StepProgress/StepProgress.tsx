import React, { ReactElement, useEffect, useState } from 'react';
import { Text } from 'react-native';

import * as S from './StepProgress.style';

type IStepObject = {
  no: string;
  name: string;
};

type IStepProgress = {
  steps: IStepObject[];
  activeStepCount: number;
  stepContents: ReactElement[];
  onSetStepCount?: (active: number) => void;
  isStepNameRequired?: boolean;
  isShortLine?: boolean;
};
const CheckLarge = require('../../../../app/src/assets/images/png/migrationFlow/CheckLarge/CheckLarge16.png');

const StepProgress: React.FC<IStepProgress> = ({
  steps,
  activeStepCount,
  stepContents,
  isStepNameRequired = true,
  isShortLine = true,
}) => {
  const [currentStep, setCurrentStep] = useState(activeStepCount);
  const errorMsg =
    'Error!!!, Misalignment of length between the step and step content';
  useEffect(() => {
    setCurrentStep(activeStepCount);
  }, [activeStepCount]);

  return (
    <S.Container>
      {(stepContents.length > 0 && steps.length === stepContents.length) ||
      steps ? (
        <>
          <S.StepContainer>
            {steps.map((step: IStepObject, index: number) => (
              <S.Step key={step.no}>
                {index > 0 && (
                  <S.Line
                    completed={index <= currentStep}
                    isTwoStep={steps.length === 2}
                    isShortLine={isShortLine}
                  />
                )}
                <S.Circle
                  testID={`stepper${step.no}`}
                  active={index === currentStep}
                  prevStep={index < currentStep}>
                  {index < currentStep ? (
                    <S.CheckImage
                      testID={`stepperImg${step.no}`}
                      source={CheckLarge}
                    />
                  ) : (
                    <S.StepText
                      testID={`step${step.no}`}
                      active={index === currentStep}>
                      {step.no}
                    </S.StepText>
                  )}
                </S.Circle>
                {isStepNameRequired && (
                  <S.TextContainer>
                    <S.CircleText>
                      <S.StepName testID={`stepName${step.no}`}>
                        {step.name}
                      </S.StepName>
                    </S.CircleText>
                  </S.TextContainer>
                )}
              </S.Step>
            ))}
          </S.StepContainer>
          <S.ContentContainer>{stepContents[currentStep]}</S.ContentContainer>
        </>
      ) : (
        <Text testID="errorMsg">{errorMsg}</Text>
      )}
    </S.Container>
  );
};

export default StepProgress;
