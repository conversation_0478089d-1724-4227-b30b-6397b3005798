import { Dimensions, TextProps, ViewProps } from 'react-native';
import styled from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width;
const stepPerLineWidth = phoneWidth > 700 ? 210 : phoneWidth > 300 ? 110 : 100;

interface CircleProps extends ViewProps {
  active: boolean;
  prevStep: boolean;
}

interface StepTextProps extends TextProps {
  active: boolean;
}

export const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const CircleText1 = styled.View`
  flex-direction: row;
  align-items: center;
  height: 110px;
  border-radius: 20px;

  justify-content: center;
  color: black;
`;

export const StepNo = styled.Text`
  color: black;
`;

export const StepContainer = styled.View`
  flex-direction: row;
  width: 100%;
  justify-content: center;
`;

export const Step = styled.View`
  flex-direction: row;
  align-items: center;
`;

export const Circle = styled.View.attrs<CircleProps>({})`
  width: 30px;
  height: 30px;
  border-radius: 20px;
  background-color: #ededed;
  justify-content: center;
  align-items: center;
  border-width: 0.15px;
  border-color: grey;
  background-color: ${({ active, prevStep }) => {
    if (active) {
      return 'black';
    }
    if (prevStep) {
      return 'green';
    }
    return '#EDEDED';
  }};
`;

export const StepText = styled.Text.attrs<StepTextProps>({})`
  color: ${({ active }) => {
    if (active) {
      return 'white';
    }
    return 'grey';
  }};
  font-size: 12px;
`;

export const TextContainer = styled.View`
  flex: 1;
  justify-content: space-between;
  align-items: center;
`;
export const CircleText = styled.View`
  width: 100px;
  height: 100px;
  padding-top: 60px;
  padding-left: 10px;
  color: black;
`;

export const CircleNo = styled.View`
  align-items: center;
  justify-content: center;
`;

export const StepName = styled.Text`
  color: black;
  font-family: Roboto-Regular;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  width: 100%;
  margin-top: 20px;
  height: 300px;
  margin-left: -23px;
`;

export const Line = styled.View<{
  isTwoStep?: boolean;
  completed: boolean;
  isShortLine?: boolean;
}>`
  width: ${({ isTwoStep, isShortLine }) =>
    isTwoStep ? (isShortLine ? stepPerLineWidth : '210px') : '100px'};
  height: 1px;
  background-color: ${({ completed }) => (completed ? 'black' : '#dbd7d7')};
`;
export const ContentContainer = styled.View`
  flex: 1;
  flex-direction: row;
  margin-top: 10px;
`;

export const ButtonContainer = styled.View`
  flex-direction: row;
  margin-top: 20px;
`;

export const Button = styled.TouchableOpacity`
  padding: 10px;
  border-radius: 5px;
  background-color: blue;
  margin: 10px;
`;

export const ButtonText = styled.Text`
  color: white;
`;

export const CheckImage = styled.Image`
  width: 18px;
`;
