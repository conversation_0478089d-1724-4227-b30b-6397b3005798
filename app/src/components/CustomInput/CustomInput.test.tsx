import { render, screen } from '@testing-library/react-native';
import React from 'react';

import CustomInput from './CustomInput';

const renderComponent = () =>
  render(
    <CustomInput
      placeholder="placeholder"
      value="value"
      isValid={true}
      onChange={() => {}}
      label="label"
      error="error"
    />,
  );

describe('CustomInput', () => {
  it('has placeholder as accessibility label for input box', () => {
    renderComponent();
    const inputBox = screen.getByLabelText('placeholder');
    expect(inputBox).toBeDefined();
  });
});
