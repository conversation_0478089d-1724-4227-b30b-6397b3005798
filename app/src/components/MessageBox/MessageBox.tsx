import React from 'react';

import * as S from './MessageBox.Styles';

interface MessageDetailsProps {
  title?: string;
  description?: string;
  linkText?: string;
  linkUrl?: boolean;
  isSingleLine?: boolean;
  deviderDesign?: object;
  testID?: string;
  handlePress?: () => void;
}

const MessageBox: React.FC<MessageDetailsProps> = ({
  title,
  description,
  linkText,
  linkUrl,
  deviderDesign,
  testID,
  handlePress,
}) => {
  return (
    <S.Container>
      <S.Divider style={deviderDesign} />
      {title && <S.Title>{title}</S.Title>}
      <S.Subtitle>
        {description}
        {linkText && linkUrl && handlePress && (
          <S.LinkText testID={testID} onPress={handlePress}>
            {linkText}
          </S.LinkText>
        )}
      </S.Subtitle>
      <S.Divider />
    </S.Container>
  );
};

export default MessageBox;
