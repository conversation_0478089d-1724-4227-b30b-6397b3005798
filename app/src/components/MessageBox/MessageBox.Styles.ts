import styled from 'styled-components/native';

export const Container = styled.View`
  background-color: #ffffff;
  flex-direction: column;
`;
export const Divider = styled.View`
  height: 1px;
  background-color: #dedede;
  margin: 10px 0;
`;

export const Title = styled.Text`
  font-size: 16px;
  font-weight: regular;
  color: #111111;
  font-family: Roboto;
  padding: 10px 0 10px 23px;
`;

export const Subtitle = styled.Text`
  font-size: 12px;
  color: #111111b3;
  margin-top: 4px;
  font-weight: regular;
  font-family: Roboto;
  padding: 0px 120px 10px 23px;
  letter-spacing: 0.6px;
  line-height: 18px;
`;

export const LinkText = styled.Text`
  color: rgb(0, 100, 204);
  text-decoration-line: underline;
  padding-top: 40px;
  font-size: 12px;
  font-weight: regular;
  font-family: Roboto;
`;
