import { Provider, Site } from '@bp/map-mfe';
import { CipStartJourneyFlow } from '@bp/pulse-auth-sdk';
import { MapBanner } from '@bp/ui-components/mobile/core';
import { BannerType } from '@utils/siteBannerHelper';
import React from 'react';
import { useTranslation } from 'react-i18next';

export type UnauthenticatedSiteBannerProps = {
  site: Site;
  onAuth: (bannerType: string, cipServiceType: CipStartJourneyFlow) => void;
};

const UnauthenticatedSiteBanner = ({
  site,
  onAuth,
}: UnauthenticatedSiteBannerProps) => {
  const { t } = useTranslation();

  if (site.provider === Provider.DCS) {
    return (
      <MapBanner
        testID="DCSSite"
        headerText={t('map.dcsLoginBanner.header')}
        bodyText={t('map.dcsLoginBanner.body')}
        primaryButtonText={t('map.loginBanner.button')}
        primaryButtonPress={() =>
          onAuth(
            BannerType.UnauthenticatedSiteBannerDCS,
            CipStartJourneyFlow.LOGIN,
          )
        }
        secondaryButtonText={t('map.loginBanner.createAccount')}
        secondaryButtonPress={() =>
          onAuth(
            BannerType.UnauthenticatedSiteBannerDCS,
            CipStartJourneyFlow.REGISTRATION,
          )
        }
      />
    );
  }

  return (
    <MapBanner
      testID="PublicSiteAuth"
      headerText={t('map.authBanner.header')}
      bodyText={t('map.authBanner.body')}
      primaryButtonText={t('map.loginBanner.button')}
      primaryButtonPress={() =>
        onAuth(
          BannerType.UnauthenticatedSiteBannerAuth,
          CipStartJourneyFlow.LOGIN,
        )
      }
      secondaryButtonText={t('map.loginBanner.createAccount')}
      secondaryButtonPress={() =>
        onAuth(
          BannerType.UnauthenticatedSiteBannerAuth,
          CipStartJourneyFlow.REGISTRATION,
        )
      }
    />
  );
};

export default UnauthenticatedSiteBanner;
