import { OnboardingContext, useOnboarding } from '@bp/onboarding-mfe';
import { useConfig } from '@providers/ConfigProvider';
import { waitFor } from '@testing-library/react-native';
import * as utilsNavigation from '@utils/navigation';
import { render } from '@utils/test-utils';
import React from 'react';
import { Alert } from 'react-native';
import { hide } from 'react-native-bootsplash';

import { LaunchHandler } from './LaunchHandler';

jest.mock('@providers/ConfigProvider');

jest.mock('react-native-bootsplash', () => ({
  hide: jest.fn(),
}));

jest.mock('react-native-device-info', () => ({
  getVersion: () => '3.2.0',
}));

const mockedUseOnboarding = jest.mocked(useOnboarding);
const mockedUseConfig = jest.mocked(useConfig);

mockedUseConfig.mockImplementation(
  () =>
    ({
      fetched: false,
      app_shell: { forced_update: {} },
    } as any),
);

describe('<Launch Handler />', () => {
  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('navigate useEffect', () => {
    it('displays the Guest User Data notice to users on an Android device', async () => {
      mockedUseOnboarding.mockImplementation(
        () =>
          ({
            onboardingStatus: {},
          } as OnboardingContext),
      );
      jest.mock('react-native/Libraries/Utilities/Platform', () => ({
        OS: 'android',
        select: () => null,
      }));

      const navigateMock = jest.fn();
      jest.spyOn(utilsNavigation, 'navigate').mockImplementation(navigateMock);

      render(<LaunchHandler />);

      await waitFor(() =>
        expect(navigateMock).toHaveBeenCalledWith(
          'DataPrivacyNotice',
          {},
          true,
        ),
      );
    });

    it('does not navigate to any screen if user is not authenticated', async () => {
      const navigateMock = jest.fn();
      jest.spyOn(utilsNavigation, 'navigate').mockImplementation(navigateMock);

      render(<LaunchHandler />);

      await waitFor(() => expect(navigateMock).not.toHaveBeenCalled());
    });
  });

  describe('updateRequired useEffect', () => {
    it('should hide bootsplash', async () => {
      mockedUseConfig.mockImplementation(
        () =>
          ({
            fetched: true,
            app_shell: { forced_update: {} },
          } as any),
      );

      render(<LaunchHandler />);

      await waitFor(() => expect(hide).toBeCalled());
    });

    it('should require update, minimum version', async () => {
      jest.spyOn(Alert, 'alert');

      mockedUseConfig.mockImplementation(
        () =>
          ({
            fetched: true,
            app_shell: {
              forced_update: {
                minimum_version: '9.9.9',
                blocked_versions: [],
              },
            },
          } as any),
      );

      render(<LaunchHandler />);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalled();
      });
    });
  });
});
