import {
  ActionConfirmationModal,
  ActionConfirmationModalProps,
} from '@bp/ui-components/mobile/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

export type UnlinkUberErrorModalProps = {
  isVisible: boolean;
  onPressTryAgain: ActionConfirmationModalProps['primaryButtonOnPress'];
  onPressSecondaryButton: ActionConfirmationModalProps['secondaryButtonOnPress'];
  secondaryButtonText:
    | 'uber.unlinkUberError.secondaryButtonLabelContactUs'
    | 'uber.unlinkUberError.secondaryButtonLabel';
};

export const UberProUnlinkErrorModal = ({
  isVisible,
  onPressTryAgain,
  onPressSecondaryButton,
  secondaryButtonText,
}: UnlinkUberErrorModalProps) => {
  const { t } = useTranslation();

  return (
    <ActionConfirmationModal
      isVisible={isVisible}
      titleText={t('uber.unlinkUberError.title')}
      titleMessage={t('uber.unlinkUberError.description')}
      additionalText={t('uber.unlinkUberError.additionalText')}
      primaryButtonText={t('uber.unlinkUberError.primaryButtonLabel')}
      primaryButtonOnPress={onPressTryAgain}
      secondaryButtonText={t(secondaryButtonText)}
      secondaryButtonOnPress={onPressSecondaryButton}
    />
  );
};

export default UberProUnlinkErrorModal;
