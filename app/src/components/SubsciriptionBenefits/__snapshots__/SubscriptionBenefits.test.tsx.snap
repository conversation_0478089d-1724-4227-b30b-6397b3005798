// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubsciriptionBenefits rendering Should render correctly 1`] = `
<View
  style={
    {
      "backgroundColor": "rgba(255, 255, 255, 0.92)",
      "paddingBottom": 10,
      "paddingLeft": 54,
      "paddingRight": 54,
      "paddingTop": 10,
    }
  }
  testID="subsciriptionBenefits"
>
  <View
    style={
      {
        "alignItems": "center",
        "justifyContent": "center",
        "paddingTop": 17,
      }
    }
  >
    < />
  </View>
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontFamily": "Roboto-Regular",
        "fontSize": 16,
        "fontWeight": "400",
        "letterSpacing": 0.15,
        "lineHeight": 28,
        "textAlign": "center",
      }
    }
  />
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontFamily": "Roboto-Light",
        "fontSize": 16,
        "letterSpacing": 0.15,
        "lineHeight": 28,
        "textAlign": "center",
      }
    }
  >
    migrationScreen.SubsMigrationSucessScreen.subsBenefit.content1
  </Text>
  <View
    style={
      {
        "alignItems": "center",
        "justifyContent": "center",
        "paddingTop": 17,
      }
    }
  >
    < />
  </View>
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontFamily": "Roboto-Regular",
        "fontSize": 16,
        "fontWeight": "400",
        "letterSpacing": 0.15,
        "lineHeight": 28,
        "textAlign": "center",
      }
    }
  />
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontFamily": "Roboto-Light",
        "fontSize": 16,
        "letterSpacing": 0.15,
        "lineHeight": 28,
        "textAlign": "center",
      }
    }
  >
    migrationScreen.SubsMigrationSucessScreen.subsBenefit.content2
  </Text>
  <View
    style={
      {
        "alignItems": "center",
        "justifyContent": "center",
        "paddingTop": 17,
      }
    }
  >
    < />
  </View>
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontFamily": "Roboto-Regular",
        "fontSize": 16,
        "fontWeight": "400",
        "letterSpacing": 0.15,
        "lineHeight": 28,
        "textAlign": "center",
      }
    }
  />
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontFamily": "Roboto-Light",
        "fontSize": 16,
        "letterSpacing": 0.15,
        "lineHeight": 28,
        "textAlign": "center",
      }
    }
  >
    migrationScreen.SubsMigrationSucessScreen.subsBenefit.content3
  </Text>
</View>
`;
