import { CreditCard, CurrencyPoundCircle, RewardGift } from '@assets/images';
import { render } from '@testing-library/react-native';
import React from 'react';
import { useTranslation } from 'react-i18next';

import SubsciriptionBenefits from './SubsciriptionBenefits';

describe('SubsciriptionBenefits rendering', () => {
  const { t } = useTranslation();

  const subscriptionBenefitsContent = [
    {
      id: 1,
      icon: RewardGift,
      content: t(
        'migrationScreen.SubsMigrationSucessScreen.subsBenefit.content1',
      ),
    },
    {
      id: 2,
      icon: CurrencyPoundCircle,
      content: t(
        'migrationScreen.SubsMigrationSucessScreen.subsBenefit.content2',
      ),
    },
    {
      id: 3,
      icon: CreditCard,
      content: t(
        'migrationScreen.SubsMigrationSucessScreen.subsBenefit.content3',
      ),
    },
  ];

  const renderComponent = () =>
    render(<SubsciriptionBenefits data={subscriptionBenefitsContent} />);
  it('Should render correctly', () => {
    const result = renderComponent();
    expect(result.toJSON()).toMatchSnapshot();
  });
  it('Should render SubsciriptionBenefits componentes', () => {
    const { getByText } = renderComponent();
    expect(
      getByText(
        t('migrationScreen.SubsMigrationSucessScreen.subsBenefit.content1'),
      ),
    ).toBeDefined();
    expect(
      getByText(
        t('migrationScreen.SubsMigrationSucessScreen.subsBenefit.content2'),
      ),
    ).toBeDefined();
    expect(
      getByText(
        t('migrationScreen.SubsMigrationSucessScreen.subsBenefit.content3'),
      ),
    ).toBeDefined();
  });
});
