// @ts-nocheck
import { useOnboarding } from '@bp/onboarding-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { ScreenName } from '@bp/registration-mfe/src/common/enums';
import {
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react-native';
import { navigate } from '@utils/navigation';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import ThemeProvider from '../../providers/ThemeProvider';
import CompleteConsents from './CompleteConsents';

jest.mock('@utils/navigation');

const mockUpdateConsent = jest.fn().mockResolvedValue('');
const mockLogout = jest.fn();

jest.mock('@bp/pulse-auth-sdk', () => ({
  useAuth: jest.fn(),
  SFConsent: {
    EV_TERMS_AND_CONDITIONS: 'EV Terms and Conditions',
    EV_PRIVACY_POLICY: 'EV Privacy Policy',
    EV_MARKETING: 'EV Marketing',
  },
}));

const renderPage = () =>
  render(
    <ThemeProvider>
      <SafeAreaProvider>
        <CompleteConsents />
      </SafeAreaProvider>
    </ThemeProvider>,
  );

describe('Complete Consents Tests', () => {
  beforeEach(() => {
    useAuth.mockReset();
  });

  it('Pressing the close button opens the pop up modal and prompts the user to log out', done => {
    useAuth.mockImplementation(() => ({
      authenticated: true,
      logout: mockLogout,
      updateConsent: mockUpdateConsent,
    }));
    useOnboarding.mockImplementation(() => ({
      onboardingStatus: {},
    }));

    const { getByText, getByTestId } = renderPage();

    const closeButton = getByTestId('CompleteSetupHeader.CloseButton');
    fireEvent.press(closeButton);

    const logoutButton = getByText('completeSetUp.logout');

    fireEvent.press(logoutButton);

    expect(mockLogout).toBeCalledTimes(1);
    done();
  });

  it('You can press the continue button after checkboxes are pressed', done => {
    useAuth.mockImplementation(() => ({
      authenticated: true,
      logout: mockLogout,
      updateConsent: mockUpdateConsent,
      consentsValid: true,
    }));

    const { getAllByTestId } = renderPage();
    const { updateConsent } = useAuth();
    const checkboxes = getAllByTestId('checkbox');
    const continueButton = screen.getByTestId('button');

    fireEvent.press(continueButton);
    expect(updateConsent).not.toHaveBeenCalled();

    fireEvent.press(checkboxes[0]);
    fireEvent.press(checkboxes[1]);
    fireEvent.press(checkboxes[2]);

    fireEvent.press(continueButton);

    waitFor(() => {
      expect(updateConsent).toHaveBeenCalled();
      done();
    });
  });

  it('If email verification is false, the user is navigated to complete set up', done => {
    useAuth.mockImplementation(() => ({
      authenticated: true,
      logout: mockLogout,
      updateConsent: mockUpdateConsent,
      consentsValid: true,
      user: {
        emailVerified: false,
      },
    }));
    useOnboarding.mockImplementation(() => ({
      onboardingStatus: {},
    }));

    const { getAllByTestId } = renderPage();
    const { updateConsent } = useAuth();

    const checkboxes = getAllByTestId('checkbox');
    fireEvent.press(checkboxes[0]);
    fireEvent.press(checkboxes[1]);
    fireEvent.press(checkboxes[2]);

    const continueButton = screen.getByTestId('button');

    fireEvent.press(continueButton);
    expect(navigate).toBeCalledWith(
      'Registration',
      {
        screen: ScreenName.EMAIL_VERIFICATION,
      },
      true,
    );
    expect(updateConsent).toHaveBeenCalled();
    done();
  });

  it('If account is not onboarded, the user is navigated to account onboarding', done => {
    useAuth.mockImplementation(() => ({
      authenticated: true,
      logout: mockLogout,
      updateConsent: mockUpdateConsent,
      consentsValid: true,
      user: {
        emailVerified: true,
      },
    }));
    useOnboarding.mockImplementation(() => ({
      onboardingStatus: { account: false },
    }));

    const { getAllByTestId } = renderPage();
    const { updateConsent } = useAuth();

    const checkboxes = getAllByTestId('checkbox');
    fireEvent.press(checkboxes[0]);
    fireEvent.press(checkboxes[1]);
    fireEvent.press(checkboxes[2]);

    const continueButton = screen.getByTestId('button');

    fireEvent.press(continueButton);

    waitFor(() => {
      expect(navigate).toBeCalledWith(
        'Registration',
        {
          screen: ScreenName.ACCOUNT_ONBOARDING,
        },
        true,
      );
      expect(updateConsent).toHaveBeenCalled();
      done();
    });
  });
});
