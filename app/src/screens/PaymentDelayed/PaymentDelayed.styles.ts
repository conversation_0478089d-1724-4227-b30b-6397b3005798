import styled from 'styled-components/native';

export const SafeAreaContainer = styled.SafeAreaView`
  flex: 1;
  width: 100%;
  align-items: center;
  justify-content: center;
`;

export const ImageContainer = styled.View`
  width: 85%;
  height: 260px;
  margin-top: 20%;
`;

export const Title = styled.Text`
  text-align: center;
  font-size: 28px;
  font-family: Roboto-Light;
  width: 70%;
  margin-top: 42px;
  line-height: 40px;
  letter-spacing: 0.5px;
  color: rgb(17, 17, 17);
`;

export const Subtitle = styled.Text`
  text-align: center;
  font-size: 13px;
  font-family: Roboto-Light;
  width: 90%;
  margin-top: 24px;
  line-height: 23px;
  letter-spacing: 0.25px;
  color: rgb(17, 17, 17);
`;

export const ButtonContainer = styled.View`
  width: 90%;
  margin-top: 48px;
`;
