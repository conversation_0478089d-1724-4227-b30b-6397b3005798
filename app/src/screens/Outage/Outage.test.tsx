import { render } from '@testing-library/react-native';
import React from 'react';

import ThemeProvider from '../../providers/ThemeProvider';
import Outage from './Outage';

describe('<Outage />', () => {
  it('Should render properly', () => {
    const { getByTestId } = render(
      <ThemeProvider>
        <Outage heading="Heading" body="Body" />
      </ThemeProvider>,
    );

    const outageTitle = getByTestId('OutageTitle');
    expect(outageTitle.children[0]).toEqual('Heading');

    const outageBody = getByTestId('OutageBody');
    expect(outageBody.children[0]).toEqual('Body');
  });
});
