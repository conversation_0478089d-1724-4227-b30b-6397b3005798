import { PText } from '@bp/ui-components/mobile/core';
import { SafeAreaView } from 'react-native-safe-area-context';
import styled from 'styled-components/native';

export const OutageContainer = styled(SafeAreaView)`
  flex: 1;
`;

export const TitleBar = styled.View`
  border-bottom-color: #dedede;
  border-bottom-width: 1px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-bottom: 8px;
`;

export const TitleText = styled(PText)`
  text-align: center;
  font-size: 20px;
  line-height: 27px;
  letter-spacing: 1.2px;
`;

export const InputContainer = styled.View`
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 0px 24px 0px 24px;
`;

export const IconContainer = styled.View`
  padding: 32px 0px 20px 0px;
  align-items: center;
`;

export const OutageNotice = styled(PText)`
  text-align: left;
`;
