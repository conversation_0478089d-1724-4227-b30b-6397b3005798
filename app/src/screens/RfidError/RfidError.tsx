import { SupportedBrand } from '@bp/pulse-shared-types/lib/enums/SupportedBrands';
import {
  ActionConfirmationModal,
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import { getSupportedBrand } from '@utils/brandHelper';
import { navigate } from '@utils/navigation';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, View } from 'react-native';
import config from 'react-native-config';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import * as S from './RfidError.styles';

const CardNotOrderedBP = require('../../assets/images/png/rfidError/cardNotOrderedBP.png');
const CardNotOrderedAral = require('../../assets/images/png/rfidError/cardNotOrderedAral.png');
const CardNotOrderedImageBP = () => {
  return <Image source={CardNotOrderedBP} />;
};
const CardNotOrderedImageAral = () => {
  return <Image source={CardNotOrderedAral} />;
};
export const RfidError = () => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const brand = getSupportedBrand(config.BRAND);
  const isAralBrand = brand === SupportedBrand.ARAL;
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const handleDoLater = async () => {
    navigate('Tabs', { screen: 'Map', params: {} });
    setIsModalOpen(prev => !prev);
  };
  const handleOpenCloseModal = () => {
    setIsModalOpen(prev => !prev);
  };
  const handleNavigateToRFID = () => {
    navigate('RFID');
  };
  return (
    <S.SafeAreaContainer>
      <S.Container accessibilityLabel={t('rfidError.accessibilityLabel')}>
        <S.TopSection>
          <S.TitleContainer topInset={insets.top}>
            <S.Title>{t('rfidError.title')}</S.Title>
          </S.TitleContainer>
          <S.SvgContainer>
            {isAralBrand ? (
              <CardNotOrderedImageAral />
            ) : (
              <CardNotOrderedImageBP />
            )}
          </S.SvgContainer>
          <S.BodyContainer>
            <S.Heading>{t('rfidError.heading')}</S.Heading>
            <S.Subheading>{t('rfidError.subheading')}</S.Subheading>
          </S.BodyContainer>
        </S.TopSection>
        <View>
          <S.StepText>{t('rfidError.pleaseCheck')}</S.StepText>
          <S.StepText>1.{t('rfidError.correctAddress')}</S.StepText>
          <S.StepText>2.{t('rfidError.bankAccountLinked')}</S.StepText>
          <S.StepText>3.{t('rfidError.bpUpToDate')}</S.StepText>
        </View>
        <S.ButtonCard bottomInset={insets.bottom}>
          <Button
            testID="dataPrivacyContinueButton"
            onPress={handleNavigateToRFID}
            size={ButtonSize.DEFAULT}>
            {t('rfidError.reOrderButtonText')}
          </Button>
          <Button
            testID="dataPrivacyContinueButton"
            onPress={handleOpenCloseModal}
            type={ButtonAction.SECONDARY}
            size={ButtonSize.DEFAULT}>
            {t('rfidError.doLater')}
          </Button>
        </S.ButtonCard>
        <ActionConfirmationModal
          isVisible={isModalOpen}
          titleText={t('rfidError.modal.orderChargeCardLaterTitle')}
          titleMessage={t('rfidError.modal.orderChargeCardLaterMessage')}
          primaryButtonText={t('rfidError.modal.okay')}
          primaryButtonOnPress={handleDoLater}
          secondaryButtonText={t('rfidError.modal.goBack')}
          secondaryButtonOnPress={handleOpenCloseModal}
          verticalButtons
        />
      </S.Container>
    </S.SafeAreaContainer>
  );
};
