import { analyticsEvent } from '@analytics';
import {
  TabsAnalyticsEventActiveCharge,
  TabsAnalyticsEventNoActiveCharge,
  TabsAnalyticsEventOpenHelpTab,
  TabsAnalyticsEventOpenMapTab,
  TabsAnalyticsEventOpenProfileTab,
} from '@analytics/events/Tabs';
import {
  HelpIcon,
  HelpIconSelected,
  MapIcon,
  MapIconSelected,
  ProfileIcon,
  ProfileIconSelected,
} from '@assets/images';
import { ChargeScreenNames, useCharge } from '@bp/charge-mfe';
import { useNavigation } from '@react-navigation/native';
import { navigate } from '@utils/navigation';
import React from 'react';
import { TouchableOpacity } from 'react-native';

import ChargeTabIcon from '../../components/ChargeTabIcon/ChargeTabIcon';

interface IconWrapperProps {
  screenId: string;
  children: React.ReactNode;
}

const IconWrapper: React.FC<IconWrapperProps> = ({ screenId, children }) => {
  const { isCharging } = useCharge();
  const navigation = useNavigation();

  const handleTabPress = () => {
    if (screenId === 'Charge' && !isCharging && navigation.isFocused()) {
      navigate(
        'Tabs',
        {
          screen: 'Charge',
          params: { screen: ChargeScreenNames.SerialSearch },
        },
        false,
      );
    } else {
      switch (screenId) {
        case 'Map':
          analyticsEvent(TabsAnalyticsEventOpenMapTab());
          break;
        case 'Profile':
          analyticsEvent(TabsAnalyticsEventOpenProfileTab());
          break;
        case 'Help':
          analyticsEvent(TabsAnalyticsEventOpenHelpTab());
          break;
        case 'Charge':
          if (isCharging) {
            analyticsEvent(TabsAnalyticsEventActiveCharge());
          } else {
            analyticsEvent(TabsAnalyticsEventNoActiveCharge());
          }
          break;
        default:
      }

      navigate('Tabs', { screen: screenId }, false);
    }
  };

  return (
    <TouchableOpacity testID="IconWrapper" onPress={handleTabPress}>
      {children}
    </TouchableOpacity>
  );
};

interface IconPictureProps {
  focused: boolean;
  screenId: string;
}

const IconPicture: React.FC<IconPictureProps> = ({ focused, screenId }) => {
  const { isCharging } = useCharge();

  const renderUnfocusedPicture = () => {
    switch (screenId) {
      case 'Map':
        return <MapIcon width={24} height={24} />;
      case 'Charge':
        return <ChargeTabIcon isTabSelected={false} isCharging={isCharging} />;
      case 'Profile':
        return <ProfileIcon width={24} height={24} />;
      case 'Help':
        return <HelpIcon width={24} height={24} />;
      default:
        return null;
    }
  };

  const renderFocusedPicture = () => {
    switch (screenId) {
      case 'Map':
        return <MapIconSelected width={24} height={24} />;

      case 'Charge':
        return <ChargeTabIcon isTabSelected={true} isCharging={isCharging} />;

      case 'Profile':
        return <ProfileIconSelected width={24} height={24} />;

      case 'Help':
        return <HelpIconSelected width={24} height={24} />;

      default:
        return null;
    }
  };

  return (
    <IconWrapper screenId={screenId}>
      {focused ? renderFocusedPicture() : renderUnfocusedPicture()}
    </IconWrapper>
  );
};

export default IconPicture;
