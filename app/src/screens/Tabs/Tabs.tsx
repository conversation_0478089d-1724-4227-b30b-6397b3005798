import { useOnboarding } from '@bp/onboarding-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { navigate } from '@utils/navigation';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import ChargeScreen from '../Charge/Charge';
import { HelpScreen } from '../Help/Help';
import LiveMap from '../Map/LiveMap';
import ProfileScreen from '../Profile/Profile';
import TabIcons from './TabIcons';

// Initialise bottom navigator
const Tab = createBottomTabNavigator();

export default () => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const { authenticated, user, consentsValid } = useAuth();
  const { getStatus } = useOnboarding();

  useEffect(() => {
    const validateAccountStatus = async () => {
      if (user && authenticated) {
        const onboardingStatus = await getStatus();
        const { firstName, lastName, country, email, emailVerified } = user;
        if (
          !firstName ||
          !lastName ||
          !country ||
          !consentsValid ||
          !email ||
          !emailVerified ||
          !onboardingStatus.account
        ) {
          navigate('Registration', {}, true);
        }
      }
    };
    validateAccountStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const screens = useMemo(
    () => [
      {
        id: 'Map',
        label: t('bottomTabs.map'),
        component: LiveMap,
        showTabHeader: false,
        unmountOnBlur: false,
      },
      {
        id: 'Charge',
        label: t('bottomTabs.charge'),
        component: ChargeScreen,
        showTabHeader: false,
        unmountOnBlur: true,
      },
      {
        id: 'Profile',
        label: t('bottomTabs.profile'),
        component: ProfileScreen,
        showTabHeader: false,
        unmountOnBlur: false,
      },
      {
        id: 'Help',
        label: t('bottomTabs.help'),
        component: HelpScreen,
        showTabHeader: false,
        unmountOnBlur: false,
      },
    ],
    [t],
  );

  return (
    <Tab.Navigator>
      {screens.map(screen => {
        const tabBarIcon = useCallback(
          (props: { focused: boolean }) => {
            const { focused } = props;
            return <TabIcons focused={focused} screenId={screen.id} />;
          },
          [screen.id],
        );

        return (
          <Tab.Screen
            key={screen.id}
            name={screen.id}
            component={screen.component}
            options={{
              headerShown: screen.showTabHeader,
              tabBarIcon,
              tabBarAccessibilityLabel: screen.label,
              tabBarLabel: screen.label,
              tabBarInactiveTintColor: 'rgb(17,17 ,17)',
              tabBarActiveTintColor: '#000096',
              tabBarLabelStyle: {
                fontFamily: 'Roboto-Regular',
                fontSize: 10,
                textTransform: 'uppercase',
                letterSpacing: 1.8,
              },
              tabBarStyle: {
                height: insets.bottom + 58,
                paddingTop: 10,
                paddingBottom: insets.bottom || 10,
              },
              unmountOnBlur: screen.unmountOnBlur,
            }}
          />
        );
      })}
    </Tab.Navigator>
  );
};
