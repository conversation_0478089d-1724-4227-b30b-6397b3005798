import { MockedProvider } from '@apollo/client/testing';
import { NavigationContainer } from '@react-navigation/native';
import { render, screen } from '@testing-library/react-native';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import ThemeProvider from '../../providers/ThemeProvider';
import Tabs from './Tabs';

jest.mock('@bp/map-mfe');

jest.mock('@bp/favourites-mfe', () => {
  return {
    BrandId: {
      BP_PULSE: 'BP_PULSE',
    },
    AssetType: {
      SITE: 'SITE',
    },
    useFavourites: () => ({
      getFavourites: jest.fn(),
      addFavourite: jest.fn(),
      removeFavourite: jest.fn(),
      favourites: [],
    }),
  };
});

jest.mock('@bp/charge-history-mfe', () => {
  return {
    __esModule: true,
    ChargeHistoryAnalyticsEvent: jest.fn(() => 'link'),
  };
});

jest.mock('@bp/profile-mfe', () => {
  return {
    __esModule: true,
    useAppSettings: jest.fn(() => ({ userInfo: {} })),
    useLanguage: jest.fn(() => ({ language: 'en' })),
  };
});

const mockUseCharge = jest.fn().mockReturnValue({
  isCharging: true,
});

jest.mock('@bp/charge-mfe', () => ({
  useCharge: () => mockUseCharge(),
}));

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

jest.mock('@analytics/events/HelpScreen', () => ({
  HelpPageAnalyticsEventHelpScreenFaqsSelect: jest.fn(),
  HelpPageAnalyticsEventHelpScreenOpen: jest.fn(),
  HelpPageAnalyticsEventHelpScreenPhoneSelect: jest.fn(),
  HelpPageAnalyticsEventWebFormClick: jest.fn(),
}));

const renderComponent = () =>
  render(
    <ThemeProvider>
      <MockedProvider>
        <NavigationContainer>
          <SafeAreaProvider>
            <Tabs />
          </SafeAreaProvider>
        </NavigationContainer>
      </MockedProvider>
    </ThemeProvider>,
  );

describe('Tabs screen', () => {
  let spy: jest.SpyInstance;
  beforeEach(() => {
    spy = jest.spyOn(console, 'error').mockImplementation(() => null);
  });
  afterEach(() => {
    spy.mockRestore();
    jest.clearAllMocks();
  });

  it('should render accessibilityLabels correctly for all the tab buttons', () => {
    renderComponent();

    const mapTabButton = screen.getByLabelText('bottomTabs.map');
    expect(mapTabButton).toBeDefined();

    const chargeTabButton = screen.getByLabelText('bottomTabs.charge');
    expect(chargeTabButton).toBeDefined();

    const profileTabButton = screen.getByLabelText('bottomTabs.profile');
    expect(profileTabButton).toBeDefined();

    const helpTabButton = screen.getByLabelText('bottomTabs.help');
    expect(helpTabButton).toBeDefined();
  });
});
