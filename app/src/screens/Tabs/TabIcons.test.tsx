import { ChargeScreenNames } from '@bp/charge-mfe';
import { fireEvent, render, screen } from '@testing-library/react-native';
import { navigate as mockNavigate } from '@utils/navigation';
import React from 'react';
import { Text } from 'react-native';

import TabIcons from './TabIcons';

const mockUseCharge = jest.fn().mockReturnValue({
  isCharging: false,
});

jest.mock('@bp/charge-mfe', () => ({
  useCharge: () => mockUseCharge(),
  ChargeScreenNames: { SerialSearch: 'ChargeMFE.SerialSearch' },
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    isFocused: () => true,
  }),
}));

jest.mock('@utils/navigation', () => ({
  navigate: jest.fn(),
}));

const MockText = (props: any) => {
  return <Text>{props.children}</Text>;
};

jest.mock('@assets/images', () => ({
  MapIcon: () => <MockText>{'Map Icon'}</MockText>,
  MapIconSelected: () => <MockText>{'Map Icon Selected'}</MockText>,
  ProfileIcon: () => <MockText>{'Profile Icon'}</MockText>,
  ProfileIconSelected: () => <MockText>{'Profile Icon Selected'}</MockText>,
  ChargeIcon: () => <MockText>{'Charge Icon'}</MockText>,
  ChargeIconSelected: () => <MockText>{'Charge Icon Selected'}</MockText>,
  HelpIcon: () => <MockText>{'Help Icon'}</MockText>,
  HelpIconSelected: () => <MockText>{'Help Icon Selected'}</MockText>,
}));

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

describe('TabIcons', () => {
  it('Renders Map Icon unfocused', async () => {
    render(<TabIcons focused={false} screenId={'Map'} />);
    expect(await screen.findAllByText('Map Icon')).toBeDefined();
  });

  it('Renders Map Icon focused', () => {
    render(<TabIcons focused={true} screenId={'Map'} />);
    expect(screen.findAllByText('Map Icon Selected')).toBeDefined();
  });

  it('Renders Profile Icon unfocused', () => {
    render(<TabIcons focused={false} screenId={'Profile'} />);
    expect(screen.findAllByText('Profile Icon')).toBeDefined();
  });

  it('Renders Profile Icon focused', () => {
    render(<TabIcons focused={true} screenId={'Profile'} />);
    expect(screen.findAllByText('Profile Icon Selected')).toBeDefined();
  });

  it('Renders Charge Icon unfocused', () => {
    render(<TabIcons focused={false} screenId={'Charge'} />);
    expect(screen.findAllByText('Charge Icon')).toBeDefined();
  });

  it('Renders Charge Icon focused', () => {
    render(<TabIcons focused={true} screenId={'Charge'} />);
    expect(screen.findAllByText('Charge Icon Selected')).toBeDefined();
  });

  it('Renders Help Icon unfocused', () => {
    render(<TabIcons focused={false} screenId={'Help'} />);
    expect(screen.findAllByText('Help Icon')).toBeDefined();
  });

  it('Renders Help Icon focused', () => {
    render(<TabIcons focused={true} screenId={'Help'} />);
    expect(screen.findAllByText('Help Icon Selected')).toBeDefined();
  });

  it('navigates to Charge screen if not charging and tab is selected', async () => {
    render(<TabIcons focused={true} screenId={'Charge'} />);

    const icon = await screen.getByTestId('IconWrapper');

    fireEvent.press(icon);

    expect(mockNavigate).toHaveBeenCalledWith(
      'Tabs',
      {
        screen: 'Charge',
        params: { screen: ChargeScreenNames.SerialSearch },
      },
      false,
    );
  });
});
