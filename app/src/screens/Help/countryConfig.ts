import { SupportedCountries } from '@common/enums';

export type SupportedHelpConfigCountries = SupportedCountries;

export type HelpConfig = {
  telNo: string;
  callingNo: string;
  url: string;
  contactUrl: string;
  countryName: string;
  contentText: string;
  flag: string;
};

export type HelpCountryConfig = Record<
  SupportedHelpConfigCountries,
  HelpConfig
>;

const contentText = 'helpScreen.getInTouch';

export const countryConfig: HelpCountryConfig = {
  UK: {
    telNo: '+44 ************',
    callingNo: '+448004643444',
    url: 'https://www.bppulse.co.uk/help-centre/charging/faq/ev-charging-on-the-go/bp-pulse-app',
    contactUrl: 'https://www.bppulse.co.uk/help-centre/contact',
    countryName: 'country.GB',
    contentText: 'helpScreen.callUsAnyTime',
    flag: '🇬🇧',
  },
  NL: {
    telNo: '+31 85 002 22 86',
    callingNo: '+31850022286',
    url: 'https://www.bp.com/nl_nl/netherlands/home/<USER>/pulse/FAQ.html#accordion_bp%20en%20bp%20pulse%20in%20Nederland',
    contactUrl:
      'https://www.bp.com/nl_nl/netherlands/home/<USER>/pulse/Contact.html',
    countryName: 'country.NL',
    contentText,
    flag: '🇳🇱',
  },
  ES: {
    telNo: '+34 900 948 088',
    callingNo: '+34900948088',
    url: 'https://www.bp.com/es_es/spain/home/<USER>/para-su-vehiculo/bp-pulse/preguntas-frecuentes.html',
    contactUrl:
      'https://www.bp.com/es_es/spain/home/<USER>/para-su-vehiculo/bp-pulse/contacto.html',
    countryName: 'country.ES',
    contentText,
    flag: '🇪🇸',
  },
  DE: {
    telNo: '+49 80 013 53 511',
    callingNo: '+498001353511',
    url: 'https://www.aral.de/de/global/retail/pulse/faq-kundensupport.html',
    contactUrl: 'https://service.aral.de/kontakt/pulse',
    countryName: 'country.DE',
    contentText,
    flag: '🇩🇪',
  },
  US: {
    telNo: '************',
    callingNo: '************',
    url: 'https://www.bp.com/en_us/united-states/home/<USER>/faqs.html.html#tab_bp-pulse',
    contactUrl:
      'https://www.bp.com/en_us/united-states/home/<USER>/contact-us.html#tab_bp-pulse',
    countryName: 'country.US',
    contentText,
    flag: '🇺🇸',
  },
};
