import { render } from '@utils/test-utils';
import React from 'react';

import CountryBanner from './CountryBanner';

const changeCountry = jest.fn();
const setHelpLocale = jest.fn();

const renderComponent = () =>
  render(<CountryBanner helpLocale={null} setHelpLocale={setHelpLocale} />);

describe('<CountryBanner />', () => {
  it('should render changeCountry without calling the button ', () => {
    renderComponent();
    expect(changeCountry).not.toHaveBeenCalled();
  });
});
