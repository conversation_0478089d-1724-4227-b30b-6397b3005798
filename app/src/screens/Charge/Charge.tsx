import { analyticsEvent } from '@analytics';
import {
  PaymentCard,
  useWallet,
  WalletScreenNames,
} from '@bp/bppay-wallet-feature';
import {
  ChargeContextProvider,
  ChargeRouteParams,
  Screens,
} from '@bp/charge-mfe';
import {
  ChargeScreenNames,
  ChargeUserType,
} from '@bp/charge-mfe/dist/common/enums';
import {
  Chargepoint,
  Connector,
  GeoPoint,
} from '@bp/charge-mfe/dist/types/graphql/graphql';
import { useFavourites } from '@bp/favourites-mfe';
import { MapScreenParamsViewSite } from '@bp/map-mfe';
import { useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings, useLanguage } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { ScreenWrapper, SiteBanner, SoftOnboardingBanner } from '@components';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import { useConnectivity } from '@providers/ConnectivityProvider';
import {
  assetType,
  brandId,
  favouriteParams,
  getFavouriteIds,
} from '@utils/favourites';
import { logger } from '@utils/logger';
import { navigate, navigation } from '@utils/navigation';
import { openWebshopLink } from '@utils/webshoplinking';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

export type NavChargeRouteParams = {
  route: {
    params: ChargeRouteParams;
  };
};

const Render = ({ route }: NavChargeRouteParams) => {
  const { language } = useLanguage();
  const { favourites, getFavourites, addFavourite, removeFavourite } =
    useFavourites();

  const { authenticated, user, loginOrRegister } = useAuth();

  const {
    selectedCard,
    triggerAddPaymentCardFlow,
    triggerRegisteredPreAuthFlow,
  } = useWallet();

  const { refetchUserInfo, userInfo } = useAppSettings();

  const { external_links: externalLinks } = useConfig();

  const authStateRef = useRef<boolean | null>(null);

  const webshopUrl = externalLinks?.de_webshop_link;

  const {
    onboardingStatus: { roaming, charging },
    onboardCharging,
    onboardRoaming,
  } = useOnboarding();

  const { isInternetReachable } = useConnectivity();

  const [paymentCard, setPaymentCard] = useState<React.ReactNode>(
    <PaymentCard />,
  );

  const favouriteIds = useMemo(() => getFavouriteIds(favourites), [favourites]);

  // Determine user type based on authenticated state
  const userType = authenticated
    ? ChargeUserType['PAYG-WALLET']
    : ChargeUserType.GUEST;

  const handleOpenWebshopLink = useCallback(
    (connectorExternalId: string) => {
      openWebshopLink(webshopUrl, connectorExternalId);
    },
    [webshopUrl],
  );

  const handleAddFavourite = useCallback(
    (id: string) => {
      return addFavourite(favouriteParams(id));
    },
    [addFavourite],
  );

  const handleRemoveFavourite = useCallback(
    (id: string) => {
      return removeFavourite(favouriteParams(id));
    },
    [removeFavourite],
  );

  // Onboards a user for charging capability
  const onEnableCharging = () => onboardCharging(user?.email);

  // Handle exiting Charge MFE action
  const onExitMFE = useCallback(
    () => navigate('Tabs', { screen: 'Map' }, true),
    [],
  );

  const onNavigateToMap = useCallback(
    (selectedSiteId: string | undefined, mapPosition: GeoPoint | undefined) => {
      navigate(
        'Tabs',
        {
          screen: 'Map',
          params: {
            screen: 'MapMFE.MapScreen',
            params: MapScreenParamsViewSite({
              selectedSiteId,
              mapPosition,
            }),
          },
        },
        true,
      );
    },
    [],
  );

  const onNavigateToError = () => {
    navigate('Error', {}, false);
  };

  const onPaymentRequired = useCallback(
    () => navigate('ChargeActivity', {}, false),
    [],
  );

  const onRequestPayment = () => {
    navigate('Credit', {}, false);
  };

  // Refetch user info to get updated balance
  const onCheckPayment = useCallback(() => {
    refetchUserInfo();
    logger.log('Handle onCheckPayment');
  }, [refetchUserInfo]);

  // Request login for registered user charging
  const onRequestLogin = useCallback(
    () => loginOrRegister(language),
    [loginOrRegister, language],
  );

  // Handles checking the validity of payment method for a charge
  const paymentMethodValid = useMemo(() => {
    return (
      userType === ChargeUserType['PAYG-WALLET'] &&
      !!selectedCard &&
      !selectedCard?.isExpired
    );
  }, [userType, selectedCard]);

  const onChargeHistoryNavigation = useCallback(() => {
    navigate('ChargeActivity', {}, false);
  }, []);

  const onChargeComplete = useCallback(() => {
    // navigate to map
    navigate('Tabs', { screen: 'Map' }, true);

    // update user balance
    refetchUserInfo();
  }, [refetchUserInfo]);

  const handleWalletPreAuthFlow = useCallback(
    (chargepoint: Chargepoint, connector: Connector | undefined) => {
      const analyticsData = {
        CP_ID: chargepoint?.apolloInternalId,
        Site_Provider: chargepoint?.provider,
        Site_Country: chargepoint?.site?.siteDetails?.country,
        ConnectorType: connector?.type,
        CP_Scheme: chargepoint?.schemes[0]?.schemeName,
        CP_Operator: chargepoint?.site?.cpo ?? undefined,
        Connector_ID: connector?.connectorInternalId,
      };
      triggerRegisteredPreAuthFlow({
        onFailure: () =>
          navigate(WalletScreenNames.HostRoot, {
            screen: WalletScreenNames.PreAuthError,
          }),
        onSuccess: () =>
          navigate('Charge', {
            screen: ChargeScreenNames.ConnectVehicle,
          }),
        analyticsData,
      });
    },
    [triggerRegisteredPreAuthFlow],
  );

  // Resolves card component not update on method add / change
  useEffect(() => {
    setPaymentCard(<PaymentCard />);
  }, [selectedCard]);

  useEffect(() => {
    if (authenticated && authenticated !== authStateRef.current) {
      getFavourites({
        brandId,
        assetType,
      });
    }
    authStateRef.current = authenticated;
  }, [authenticated, getFavourites]);

  const isOverdrawn =
    userInfo && userInfo.userBalance !== undefined && userInfo.userBalance < 0;

  const paymentAuthType = userInfo?.paymentAuthType || undefined;

  return (
    <ScreenWrapper>
      <ChargeContextProvider
        mapsApiKey={env.G_MAPS_KEY}
        hostLoading={false}
        chargingEnabled={!!charging}
        roamingEnabled={!!roaming}
        paymentMethodValid={paymentMethodValid}
        isOverdrawn={isOverdrawn}
        paymentAuthType={paymentAuthType}
        navigation={navigation}
        routeParams={route.params}
        cardComponent={paymentCard}
        isInternetReachable={isInternetReachable}
        favourites={favouriteIds}
        webshopUrl={webshopUrl}
        ChargerBanner={SiteBanner}
        onFavouriteAdd={handleAddFavourite}
        onFavouriteRemove={handleRemoveFavourite}
        onAnalyticsEvent={analyticsEvent}
        onOpenWebshopLink={handleOpenWebshopLink}
        onChargeComplete={onChargeComplete}
        onRequestPayment={onRequestPayment}
        onCheckPayment={onCheckPayment}
        onEnableCharging={onEnableCharging}
        onEnableRoaming={onboardRoaming}
        onRequestLogin={onRequestLogin}
        onPaymentRequired={onPaymentRequired}
        onChargeHistoryNavigation={onChargeHistoryNavigation}
        onNavigateToMap={onNavigateToMap}
        onNavigateToErrorScreen={onNavigateToError}
        onExitMFE={onExitMFE}
        onAddPaymentCardFlow={triggerAddPaymentCardFlow}
        onNavigateToWalletPreAuth={handleWalletPreAuthFlow}
        SoftOnboardingBanner={SoftOnboardingBanner}>
        <Screens />
      </ChargeContextProvider>
    </ScreenWrapper>
  );
};

export default Render;
