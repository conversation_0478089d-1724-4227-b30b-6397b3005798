import { useAuth } from '@bp/pulse-auth-sdk';
import { ErrorScreen } from '@bp/ui-components/mobile/core';
import { navigate } from '@utils/navigation';
import React from 'react';
import { useTranslation } from 'react-i18next';

const Error = () => {
  const { t } = useTranslation();
  const { authenticated } = useAuth();
  const handleContinueButtonPress = () => {
    navigate('Tabs', { screen: 'Map' }, true);
  };

  return (
    <ErrorScreen
      screenTitle={t('errorScreen.title')}
      buttonText={
        !authenticated
          ? t('errorScreen.buttonText1')
          : t('errorScreen.buttonText2')
      }
      contentText={{
        text1: t('errorScreen.contentText1'),
        text2: t('errorScreen.contentText2'),
        text3: t('errorScreen.contentText3'),
      }}
      handleButtonPress={handleContinueButtonPress}
    />
  );
};

export default Error;
