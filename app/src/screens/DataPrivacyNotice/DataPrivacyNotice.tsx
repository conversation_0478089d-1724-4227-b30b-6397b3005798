import { BrandAgnostic } from '@assets/images';
import { Button, ButtonSize } from '@bp/ui-components/mobile/core';
import { useGuestConsents } from '@hooks/guestPrivacy';
import { navigate } from '@utils/navigation';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import * as S from './DataPrivacyNotice.styles';

export const DataPrivacyNotice = () => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const { setGuestPrivacyConsent } = useGuestConsents();

  const onClickDataPrivacyButton = async () => {
    setGuestPrivacyConsent(true);
    navigate('Tabs', { screen: 'Map', params: {} });
  };

  return (
    <S.Container accessibilityLabel={t('dataPrivacyNotice.accessibilityLabel')}>
      <S.TopSection>
        <S.TitleContainer topInset={insets.top}>
          <S.Title>{t('dataPrivacyNotice.title')}</S.Title>
        </S.TitleContainer>
        <S.SvgContainer>
          <BrandAgnostic />
        </S.SvgContainer>

        <S.Heading>{t('dataPrivacyNotice.heading')}</S.Heading>

        <S.Description>{t('dataPrivacyNotice.description')}</S.Description>
      </S.TopSection>

      <S.ButtonCard bottomInset={insets.bottom}>
        <Button
          testID="dataPrivacyContinueButton"
          onPress={onClickDataPrivacyButton}
          size={ButtonSize.MEDIUM}>
          {t('dataPrivacyNotice.continue')}
        </Button>
      </S.ButtonCard>
    </S.Container>
  );
};
