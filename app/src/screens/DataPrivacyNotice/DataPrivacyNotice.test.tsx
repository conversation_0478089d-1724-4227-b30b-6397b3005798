import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import ThemeProvider from '../../providers/ThemeProvider';
import { DataPrivacyNotice } from './DataPrivacyNotice';

const mockGuestPrivacyConsent = jest.fn();

jest.mock('@hooks/guestPrivacy', () => ({
  useGuestConsents: () => ({
    setGuestPrivacyConsent: mockGuestPrivacyConsent,
  }),
}));

describe('<DataPrivacyNotice />', () => {
  it('@ContinueButtonClick', async () => {
    const { getByTestId } = render(
      <ThemeProvider>
        <SafeAreaProvider>{<DataPrivacyNotice />}</SafeAreaProvider>
      </ThemeProvider>,
    );

    await waitFor(() => {
      const button = getByTestId('dataPrivacyContinueButton');
      fireEvent.press(button);
    });

    expect(mockGuestPrivacyConsent).toBeCalledWith(true);
  });
});
