import * as FavouritesMfe from '@bp/favourites-mfe';
import { Country, Provider } from '@bp/map-mfe';
import * as ProfileMfe from '@bp/profile-mfe';
import { SettingsAppContextProvider } from '@bp/profile-mfe';
import * as CipIdp from '@bp/pulse-auth-sdk';
import { ContextProps as CipContextProps } from '@bp/pulse-auth-sdk';
import { PulseAppConfig } from '@config/config.types';
import * as Config from '@providers/ConfigProvider';
import React from 'react';

import { AVAILABLE_CONNECTORS } from './config';
import {
  useApiUrl,
  useAvailableConnectors,
  useAvailableCountries,
  useAvailableProviders,
  useFavouriteIds,
  useGuestChargeProviders,
  useGuestPriceProviders,
  useUserInfo,
  useUserSchemes,
  useWebshops,
} from './hooks';

jest.mock('@bp/map-mfe');
jest.mock('@bp/profile-mfe');
jest.mock('@bp/favourites-mfe');
jest.mock('@env', () => ({
  GATEWAY_URL_PUBLIC: 'http://public.test',
  GATEWAY_URL_PRIVATE: 'http://private.test',
}));

jest.spyOn(React, 'useMemo').mockImplementation(fn => fn());

type UseFavouritesReturn = ReturnType<typeof FavouritesMfe.useFavourites>;

describe('Map/hooks', () => {
  describe('useAvailableProviders()', () => {
    it('should include providers marked as true', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          availableProviders: {
            BPCM: true,
            hasToBe: true,
            DCS: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useAvailableProviders();

      expect(providers).toEqual([
        Provider.BPCM,
        Provider.hasToBe,
        Provider.DCS,
      ]);
    });

    it('should exclude providers marked as false', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          availableProviders: {
            BPCM: true,
            hasToBe: false,
            DCS: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useAvailableProviders();

      expect(providers).toEqual([Provider.BPCM, Provider.DCS]);
    });

    it('should be case insensitive', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          availableProviders: {
            bpcm: true,
            hastobe: true,
            dCs: true,
            evc: true,
            ARAL: true,
            flEEt: true,
            Semarchy: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useAvailableProviders();

      expect(providers).toEqual([
        Provider.BPCM,
        Provider.hasToBe,
        Provider.DCS,
        Provider.EVC,
        Provider.aral,
        Provider.fleet,
        Provider.semarchy,
      ]);
    });
  });

  describe('useAvailableConnectors()', () => {
    it('@authenticated=false', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: false,
      } as unknown as CipContextProps);

      const connectors = useAvailableConnectors();
      expect(connectors).toEqual(AVAILABLE_CONNECTORS.ALL);
    });

    it('@authenticated=true, @country=UK', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: true,
        user: { country: 'UK' },
      } as unknown as CipContextProps);

      const connectors = useAvailableConnectors();
      expect(connectors).toEqual(AVAILABLE_CONNECTORS.UK);
    });

    it('@authenticated=true, @country=NL', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: true,
        user: { country: 'NL' },
      } as unknown as CipContextProps);

      const connectors = useAvailableConnectors();
      expect(connectors).toEqual(AVAILABLE_CONNECTORS.NL);
    });

    it('@authenticated=true, @country=DE', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: true,
        user: { country: 'DE' },
      } as unknown as CipContextProps);

      const connectors = useAvailableConnectors();
      expect(connectors).toEqual(AVAILABLE_CONNECTORS.DE);
    });

    it('@authenticated=true, @country=ES', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: true,
        user: { country: 'ES' },
      } as unknown as CipContextProps);

      const connectors = useAvailableConnectors();
      expect(connectors).toEqual(AVAILABLE_CONNECTORS.ES);
    });
  });

  describe('useAvailableCountries()', () => {
    it('should include countries marked as true', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          availableCountries: {
            UK: true,
            DE: true,
            NL: true,
          },
        },
      } as unknown as PulseAppConfig);

      const countries = useAvailableCountries();

      expect(countries).toEqual([Country.UK, Country.DE, Country.NL]);
    });

    it('should exclude countries marked as false', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          availableCountries: {
            UK: true,
            DE: false,
            NL: true,
          },
        },
      } as unknown as PulseAppConfig);

      const countries = useAvailableCountries();

      expect(countries).toEqual([Country.UK, Country.NL]);
    });

    it('should be case insensitive', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          availableCountries: {
            uk: true,
            De: true,
            nL: true,
            ES: true,
          },
        },
      } as unknown as PulseAppConfig);

      const countries = useAvailableCountries();

      expect(countries).toEqual([
        Country.UK,
        Country.DE,
        Country.NL,
        Country.ES,
      ]);
    });
  });

  describe('useWebshops()', () => {
    it('should populate DE and NL from config value', () => {
      const webshop_link = 'http://www.test.test';

      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        external_links: { de_webshop_link: webshop_link },
      } as unknown as PulseAppConfig);

      const webshops = useWebshops();

      expect(webshops).toEqual({
        DE: webshop_link,
        NL: webshop_link,
      });
    });
  });

  describe('useUserSchemes()', () => {
    it('should convert schemes into ids', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: true,
      } as unknown as CipContextProps);

      jest.spyOn(ProfileMfe, 'useAppSettings').mockReturnValueOnce({
        userInfo: {
          userScheme: [
            { schemeId: 1, schemeName: 'test1' },
            { schemeId: 2, schemeName: 'test2' },
          ],
        },
      } as unknown as SettingsAppContextProvider);

      const schemeIds = useUserSchemes();

      expect(schemeIds).toEqual([1, 2]);
    });

    it('should return undefined when user is not authenticated', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: false,
      } as unknown as CipContextProps);

      jest.spyOn(ProfileMfe, 'useAppSettings').mockReturnValueOnce({
        userInfo: {
          userScheme: [
            { schemeId: 1, schemeName: 'test1' },
            { schemeId: 2, schemeName: 'test2' },
          ],
        },
      } as unknown as SettingsAppContextProvider);

      const schemeIds = useUserSchemes();

      expect(schemeIds).toEqual([]);
    });
  });

  describe('useApiUrl()', () => {
    it('should route authenticated users to private gateway', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: true,
      } as unknown as CipContextProps);

      const url = useApiUrl();
      expect(url).toEqual('http://private.test');
    });

    it('should route un-authenticated users to public gateway', () => {
      jest.spyOn(CipIdp, 'useAuth').mockReturnValueOnce({
        authenticated: false,
      } as unknown as CipContextProps);

      const url = useApiUrl();
      expect(url).toEqual('http://public.test');
    });
  });

  describe('useFavouriteIds()', () => {
    it('should convert favourites into ids', () => {
      jest.spyOn(FavouritesMfe, 'useFavourites').mockReturnValueOnce({
        favourites: [
          {
            assetType: 'type',
            brandId: 'brand',
            serial: 'serial-1',
          },
          {
            assetType: 'type',
            brandId: 'brand',
            serial: 'serial-2',
          },
        ],
      } as unknown as UseFavouritesReturn);

      const favouriteIds = useFavouriteIds();

      expect(favouriteIds).toEqual(['serial-1', 'serial-2']);
    });
  });

  describe('useUserInfo()', () => {
    it('should convert Profile Mfe and SalesForce IDP info to Map Mfe userInfo shape', () => {
      const getAccessToken = jest.fn();

      jest.spyOn(CipIdp, 'useAuth').mockReturnValue({
        authenticated: true,
        getAccessToken,
        user: {
          userId: 'user-id-1',
        },
      } as unknown as CipContextProps);

      jest.spyOn(ProfileMfe, 'useAppSettings').mockReturnValue({
        userInfo: {
          Origin_Entitlement_ID: 1,
          Chargepoints_Available: ['UK-BPCM'],
          Payment_Methods: ['PAYG'],
          RFID_Enabled: true,
          userScheme: [
            { schemeId: 1, schemeName: 'test-1' },
            { schemeId: 2, schemeName: 'test-2' },
          ],
        },
      } as unknown as SettingsAppContextProvider);

      const userInfo = useUserInfo();

      expect(userInfo).toEqual({
        loggedIn: true,
        userId: 'user-id-1',
        schemes: [1, 2],
        originEntitlementId: 1,
        chargepointsAvailable: ['UK-BPCM'],
        paymentMethods: ['PAYG'],
        rfidEnabled: true,
        getToken: getAccessToken,
      });
    });
  });

  describe('useGuestChargeProviders()', () => {
    it('should include providers marked as true', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          guestChargeProviders: {
            BPCM: true,
            hasToBe: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useGuestChargeProviders();

      expect(providers).toEqual([Provider.BPCM, Provider.hasToBe]);
    });

    it('should exclude providers marked as false', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          guestChargeProviders: {
            BPCM: true,
            hasToBe: true,
            DCS: false,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useGuestChargeProviders();

      expect(providers).toEqual([Provider.BPCM, Provider.hasToBe]);
    });

    it('should be case insensitive', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          guestChargeProviders: {
            bpcm: true,
            hastobe: true,
            dCs: true,
            evc: true,
            ARAL: true,
            flEEt: true,
            Semarchy: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useGuestChargeProviders();

      expect(providers).toEqual([
        Provider.BPCM,
        Provider.hasToBe,
        Provider.DCS,
        Provider.EVC,
        Provider.aral,
        Provider.fleet,
        Provider.semarchy,
      ]);
    });
  });

  describe('useGuestPriceProviders()', () => {
    it('should include providers marked as true', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          guestPriceProviders: {
            BPCM: true,
            hasToBe: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useGuestPriceProviders();

      expect(providers).toEqual([Provider.BPCM, Provider.hasToBe]);
    });

    it('should exclude providers marked as false', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          guestPriceProviders: {
            BPCM: true,
            hasToBe: true,
            DCS: false,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useGuestPriceProviders();

      expect(providers).toEqual([Provider.BPCM, Provider.hasToBe]);
    });

    it('should be case insensitive', () => {
      jest.spyOn(Config, 'useConfig').mockReturnValueOnce({
        map_mfe_config: {
          guestPriceProviders: {
            bpcm: true,
            hastobe: true,
            dCs: true,
            evc: true,
            ARAL: true,
            flEEt: true,
            Semarchy: true,
          },
        },
      } as unknown as PulseAppConfig);

      const providers = useGuestPriceProviders();

      expect(providers).toEqual([
        Provider.BPCM,
        Provider.hasToBe,
        Provider.DCS,
        Provider.EVC,
        Provider.aral,
        Provider.fleet,
        Provider.semarchy,
      ]);
    });
  });
});
