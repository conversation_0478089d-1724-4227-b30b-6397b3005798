import {
  MapFilterByNetworkScreen,
  MapFiltersScreen,
  MapScreen,
  MapScreenName,
  MapScreenParams,
} from '@bp/map-mfe';
import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';

import MapProvider from './MapProvider';

const MapStack = createStackNavigator();

const LiveMap = () => {
  return (
    <MapProvider>
      <MapStack.Navigator
        initialRouteName={MapScreenName.MAP_SCREEN}
        screenOptions={{
          cardStyle: { backgroundColor: '#ffffff' },
          headerShown: false,
        }}>
        <MapStack.Screen
          name={MapScreenName.MAP_SCREEN}
          component={MapScreen}
          initialParams={{
            type: MapScreenParams.VIEW_USER_POSITION,
            payload: {},
          }}
        />
        <MapStack.Screen
          name={MapScreenName.MAP_FILTERS_SCREEN}
          component={MapFiltersScreen}
        />
        <MapStack.Screen
          name={MapScreenName.MAP_FILTER_BY_NETWORK_SCREEN}
          component={MapFilterByNetworkScreen}
        />
      </MapStack.Navigator>
    </MapProvider>
  );
};

export default LiveMap;
