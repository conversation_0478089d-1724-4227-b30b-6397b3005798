import { analyticsEvent } from '@analytics';
import { ChargeHistoryItem, useWallet } from '@bp/bppay-wallet-feature';
import { ChargeHistoryContextProvider, Screens } from '@bp/charge-history-mfe';
import { useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings, useLanguage } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { ScreenWrapper } from '@components';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import { navigate } from '@utils/navigation';
import React, { useCallback, useEffect } from 'react';

const ChargeActivity = () => {
  const { userInfo } = useAppSettings();
  const {
    charge_history_mfe: featureFlags,
    app_shell: { es_joint_venture_operator: jointVentureOperator },
  } = useConfig();
  const { authenticated, getAccessToken } = useAuth();
  const { language } = useLanguage();
  const { onMakePayment } = useWallet();
  const {
    onboardingStatus: { country },
  } = useOnboarding();

  const userId = userInfo?.userId;

  const loggedInUser = !!authenticated && !!country;

  const homeCountry = country || 'UK';

  // User display details
  const userDetails = {
    given_name: userInfo?.givenName ?? '',
    family_name: userInfo?.familyName ?? '',
  };

  const onUpgradeMembership = useCallback(() => {
    navigate('Subscription', {});
  }, []);

  const onPaymentWithWallet = useCallback(
    (historyRecord: ChargeHistoryItem) => {
      onMakePayment(historyRecord);
    },
    [onMakePayment],
  );

  const onExitProfile = useCallback(
    () => navigate('Tabs', { screen: 'Profile' }),
    [],
  );

  useEffect(() => {
    if (!loggedInUser) {
      navigate('Profile', {}, false);
    }
  }, [loggedInUser]);

  return (
    <ScreenWrapper>
      <ChargeHistoryContextProvider
        featureFlags={featureFlags}
        apiURL={env.GATEWAY_URL_PRIVATE}
        apiKey={env.API_GATEWAY_KEY}
        userId={userId}
        jointVentureOperator={jointVentureOperator}
        user={userDetails}
        userType={userInfo.userType}
        // @ts-expect-error history MFE country code needs updating to type rather than enum
        country={homeCountry}
        // @ts-expect-error locale type needs updating within the useLanguage context
        locale={language}
        getToken={getAccessToken}
        analytics={analyticsEvent}
        // @ts-expect-error number type set in history MFE should be history record
        onMakePayment={onPaymentWithWallet}
        onUpgradeMembership={onUpgradeMembership}
        onExitMFE={onExitProfile}>
        <Screens />
      </ChargeHistoryContextProvider>
    </ScreenWrapper>
  );
};

export default ChargeActivity;
