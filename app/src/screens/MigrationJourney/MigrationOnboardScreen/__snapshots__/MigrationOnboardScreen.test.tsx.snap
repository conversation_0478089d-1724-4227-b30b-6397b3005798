// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MigrationOnboardScreen Should render properly with snapshot 1`] = `
[
  <RCTSafeAreaView
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
      }
    }
    testID="MigrationOnboard"
  >
    <RCTScrollView
      style={
        {
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 1,
        }
      }
    >
      <View>
        <View
          style={
            {
              "flexBasis": 0,
              "flexGrow": 2,
              "flexShrink": 1,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingTop": 0,
                "position": "relative",
              }
            }
          >
            <View
              accessible={false}
              backgroundColor="#ffffff"
              border={true}
              hasLeftButton={false}
              hasRightButton={false}
              isElevated={false}
              isRelativePosition={true}
              style={
                {
                  "alignItems": "center",
                  "backgroundColor": "#ffffff",
                  "borderBottomColor": "#dedede",
                  "borderBottomWidth": 1,
                  "flexDirection": "row",
                  "justifyContent": "center",
                  "minHeight": 44,
                  "position": "relative",
                  "width": "100%",
                  "zIndex": 1,
                }
              }
              testID="Header"
            >
              <Text
                accessibilityHint="a section heading: migrationScreen.PayGSubsMigrationOnboardScreen.screenTitle"
                accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.screenTitle"
                alignCenter={true}
                fontFamily="AralPOS-Regular"
                fontSize="20px"
                maxFontSizeMultiplier={2}
                style={
                  {
                    "color": "#111111",
                    "fontFamily": "AralPOS-Regular",
                    "fontSize": 20,
                    "letterSpacing": 1.2,
                    "lineHeight": 27,
                    "textAlign": "center",
                  }
                }
                testID="migrationScreen.PayGSubsMigrationOnboardScreen.screenTitle"
              >
                migrationScreen.PayGSubsMigrationOnboardScreen.screenTitle
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "backgroundColor": "rgb(52, 52, 52)",
                "flexDirection": "row",
                "justifyContent": "center",
                "paddingBottom": 10,
                "paddingLeft": 10,
                "paddingRight": 10,
                "paddingTop": 10,
              }
            }
          >
            < />
            <Text
              style={
                {
                  "color": "rgb(255, 255, 255)",
                  "fontFamily": "Roboto-Light",
                  "fontSize": 18,
                  "letterSpacing": 0.2,
                  "lineHeight": 32,
                  "textAlign": "center",
                }
              }
            >
              migrationScreen.PayGSubsMigrationOnboardScreen.notification
            </Text>
          </View>
        </View>
        <View
          style={
            {
              "flexBasis": 0,
              "flexGrow": 1,
              "flexShrink": 1,
            }
          }
        >
          <View
            bgColor={false}
            style={
              {
                "backgroundColor": "#f692ec",
              }
            }
            testID="bgImg"
          >
            <RCTScrollView
              bgColor="#f692ec"
              style={
                {
                  "backgroundColor": "#f692ec",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "width": "100%",
                }
              }
            >
              <View>
                <View
                  style={
                    {
                      "backgroundColor": "#000096",
                      "borderBottomLeftRadius": 0,
                      "borderBottomRightRadius": 38.3,
                      "borderTopLeftRadius": 38.3,
                      "borderTopRightRadius": 38.3,
                      "display": "flex",
                      "flexBasis": 0,
                      "flexDirection": "row",
                      "flexGrow": 1,
                      "flexShrink": 1,
                      "marginBottom": -1,
                      "marginLeft": 25,
                      "marginRight": 25,
                      "marginTop": 34,
                      "paddingBottom": 19,
                      "paddingLeft": 19,
                      "paddingRight": 19,
                      "paddingTop": 19,
                    }
                  }
                >
                  <View
                    style={
                      {
                        "display": "flex",
                        "paddingRight": 16,
                      }
                    }
                  >
                    < />
                  </View>
                  <View
                    style={
                      {
                        "display": "flex",
                        "flexBasis": 0,
                        "flexDirection": "column",
                        "flexGrow": 0.9,
                        "flexShrink": 1,
                        "marginTop": 5,
                      }
                    }
                  >
                    <View
                      style={
                        {
                          "flexDirection": "row",
                          "paddingRight": 6,
                        }
                      }
                    >
                      <Text
                        style={
                          {
                            "color": "#ffffff",
                            "fontFamily": "Roboto-Bold",
                            "fontSize": 30.6,
                            "letterSpacing": 0.32,
                            "lineHeight": 33.4,
                          }
                        }
                      >
                        migrationScreen.PayGSubsMigrationOnboardScreen.paygTitle1
                        <Text
                          style={
                            {
                              "color": "#9bff00",
                              "fontFamily": "Roboto-Bold",
                              "fontSize": 30.6,
                              "letterSpacing": 0.32,
                              "lineHeight": 33.4,
                            }
                          }
                        >
                          migrationScreen.PayGSubsMigrationOnboardScreen.paygTitle2
                        </Text>
                      </Text>
                    </View>
                  </View>
                </View>
                <View
                  style={
                    {
                      "display": "flex",
                      "flexBasis": 0,
                      "flexDirection": "row",
                      "flexGrow": 1,
                      "flexShrink": 1,
                      "marginLeft": 25,
                    }
                  }
                />
              </View>
            </RCTScrollView>
            <View
              style={
                {
                  "backgroundColor": "rgba(255, 255, 255, 1)",
                  "borderBottomLeftRadius": 0,
                  "borderBottomRightRadius": 38.3,
                  "borderTopLeftRadius": 38.3,
                  "borderTopRightRadius": 38.3,
                  "display": "flex",
                  "marginTop": 40,
                  "paddingBottom": 0,
                  "paddingLeft": 0,
                  "paddingRight": 0,
                  "paddingTop": 0,
                }
              }
              testID="stepContainer"
            >
              <View
                style={
                  {
                    "paddingBottom": 20,
                  }
                }
              >
                <View
                  style={
                    {
                      "alignItems": "center",
                      "flexBasis": 0,
                      "flexGrow": 1,
                      "flexShrink": 1,
                      "justifyContent": "center",
                    }
                  }
                >
                  <View
                    style={
                      {
                        "flexDirection": "row",
                        "justifyContent": "center",
                        "width": "100%",
                      }
                    }
                  >
                    <View
                      style={
                        {
                          "alignItems": "center",
                          "flexDirection": "row",
                        }
                      }
                    >
                      <View
                        active={true}
                        prevStep={false}
                        style={
                          {
                            "alignItems": "center",
                            "backgroundColor": "black",
                            "borderBottomLeftRadius": 20,
                            "borderBottomRightRadius": 20,
                            "borderColor": "grey",
                            "borderTopLeftRadius": 20,
                            "borderTopRightRadius": 20,
                            "borderWidth": 0.15,
                            "height": 30,
                            "justifyContent": "center",
                            "width": 30,
                          }
                        }
                        testID="stepper1"
                      >
                        <Text
                          active={true}
                          style={
                            {
                              "color": "white",
                              "fontSize": 12,
                            }
                          }
                          testID="step1"
                        >
                          1
                        </Text>
                      </View>
                      <View
                        style={
                          {
                            "alignItems": "center",
                            "flexBasis": 0,
                            "flexGrow": 1,
                            "flexShrink": 1,
                            "justifyContent": "space-between",
                          }
                        }
                      >
                        <View
                          style={
                            {
                              "color": "black",
                              "height": 100,
                              "paddingLeft": 10,
                              "paddingTop": 60,
                              "width": 100,
                            }
                          }
                        >
                          <Text
                            style={
                              {
                                "color": "black",
                                "fontFamily": "Roboto-Regular",
                                "fontSize": 16,
                                "height": 300,
                                "lineHeight": 20,
                                "marginLeft": -23,
                                "marginTop": 20,
                                "textAlign": "center",
                                "width": "100%",
                              }
                            }
                            testID="stepName1"
                          >
                            migrationScreen.SubsMigrationSucessScreen.step.header1
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View
                      style={
                        {
                          "alignItems": "center",
                          "flexDirection": "row",
                        }
                      }
                    >
                      <View
                        completed={false}
                        isShortLine={true}
                        isTwoStep={true}
                        style={
                          {
                            "backgroundColor": "#dbd7d7",
                            "height": 1,
                            "width": 210,
                          }
                        }
                      />
                      <View
                        active={false}
                        prevStep={false}
                        style={
                          {
                            "alignItems": "center",
                            "backgroundColor": "#EDEDED",
                            "borderBottomLeftRadius": 20,
                            "borderBottomRightRadius": 20,
                            "borderColor": "grey",
                            "borderTopLeftRadius": 20,
                            "borderTopRightRadius": 20,
                            "borderWidth": 0.15,
                            "height": 30,
                            "justifyContent": "center",
                            "width": 30,
                          }
                        }
                        testID="stepper2"
                      >
                        <Text
                          active={false}
                          style={
                            {
                              "color": "grey",
                              "fontSize": 12,
                            }
                          }
                          testID="step2"
                        >
                          2
                        </Text>
                      </View>
                      <View
                        style={
                          {
                            "alignItems": "center",
                            "flexBasis": 0,
                            "flexGrow": 1,
                            "flexShrink": 1,
                            "justifyContent": "space-between",
                          }
                        }
                      >
                        <View
                          style={
                            {
                              "color": "black",
                              "height": 100,
                              "paddingLeft": 10,
                              "paddingTop": 60,
                              "width": 100,
                            }
                          }
                        >
                          <Text
                            style={
                              {
                                "color": "black",
                                "fontFamily": "Roboto-Regular",
                                "fontSize": 16,
                                "height": 300,
                                "lineHeight": 20,
                                "marginLeft": -23,
                                "marginTop": 20,
                                "textAlign": "center",
                                "width": "100%",
                              }
                            }
                            testID="stepName2"
                          >
                            migrationScreen.SubsMigrationSucessScreen.step.header2
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                  <View
                    style={
                      {
                        "flexBasis": 0,
                        "flexDirection": "row",
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "marginTop": 10,
                      }
                    }
                  />
                </View>
              </View>
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "fontFamily": "Roboto-Light",
                    "fontSize": 28,
                    "letterSpacing": 0.5,
                    "lineHeight": 40,
                    "paddingTop": 30,
                    "textAlign": "center",
                  }
                }
              >
                migrationScreen.PayGSubsMigrationOnboardScreen.subsBenefit.title
              </Text>
              <View
                style={
                  {
                    "backgroundColor": "rgba(255, 255, 255, 0.92)",
                    "paddingBottom": 10,
                    "paddingLeft": 54,
                    "paddingRight": 54,
                    "paddingTop": 10,
                  }
                }
                testID="subsciriptionBenefits"
              >
                <View
                  style={
                    {
                      "alignItems": "center",
                      "justifyContent": "center",
                      "paddingTop": 17,
                    }
                  }
                >
                  < />
                </View>
                <Text
                  style={
                    {
                      "color": "rgb(17, 17, 17)",
                      "fontFamily": "Roboto-Regular",
                      "fontSize": 16,
                      "fontWeight": "400",
                      "letterSpacing": 0.15,
                      "lineHeight": 28,
                      "textAlign": "center",
                    }
                  }
                />
                <Text
                  style={
                    {
                      "color": "rgb(17, 17, 17)",
                      "fontFamily": "Roboto-Light",
                      "fontSize": 16,
                      "letterSpacing": 0.15,
                      "lineHeight": 28,
                      "textAlign": "center",
                    }
                  }
                >
                  migrationScreen.PayGSubsMigrationOnboardScreen.paygBenefit.content1
                </Text>
                <View
                  style={
                    {
                      "alignItems": "center",
                      "justifyContent": "center",
                      "paddingTop": 17,
                    }
                  }
                >
                  < />
                </View>
                <Text
                  style={
                    {
                      "color": "rgb(17, 17, 17)",
                      "fontFamily": "Roboto-Regular",
                      "fontSize": 16,
                      "fontWeight": "400",
                      "letterSpacing": 0.15,
                      "lineHeight": 28,
                      "textAlign": "center",
                    }
                  }
                />
                <Text
                  style={
                    {
                      "color": "rgb(17, 17, 17)",
                      "fontFamily": "Roboto-Light",
                      "fontSize": 16,
                      "letterSpacing": 0.15,
                      "lineHeight": 28,
                      "textAlign": "center",
                    }
                  }
                >
                  migrationScreen.PayGSubsMigrationOnboardScreen.paygBenefit.content2
                </Text>
                <View
                  style={
                    {
                      "alignItems": "center",
                      "justifyContent": "center",
                      "paddingTop": 17,
                    }
                  }
                >
                  < />
                </View>
                <Text
                  style={
                    {
                      "color": "rgb(17, 17, 17)",
                      "fontFamily": "Roboto-Regular",
                      "fontSize": 16,
                      "fontWeight": "400",
                      "letterSpacing": 0.15,
                      "lineHeight": 28,
                      "textAlign": "center",
                    }
                  }
                />
                <Text
                  style={
                    {
                      "color": "rgb(17, 17, 17)",
                      "fontFamily": "Roboto-Light",
                      "fontSize": 16,
                      "letterSpacing": 0.15,
                      "lineHeight": 28,
                      "textAlign": "center",
                    }
                  }
                >
                  migrationScreen.PayGSubsMigrationOnboardScreen.paygBenefit.content3
                </Text>
              </View>
            </View>
          </View>
          <View
            style={
              {
                "backgroundColor": "rgb(255, 255, 240)",
                "borderColor": "rgb(255, 238, 92)",
                "borderStyle": "solid",
                "borderWidth": 1,
                "paddingBottom": 20,
                "paddingLeft": 14,
                "paddingRight": 24,
                "paddingTop": 20,
              }
            }
            testID="infoContainer"
          >
            <View
              style={
                {
                  "flexDirection": "row",
                }
              }
            >
              <View
                style={
                  {
                    "paddingLeft": 5,
                    "paddingRight": 5,
                    "paddingTop": 5,
                  }
                }
              >
                < />
              </View>
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "flexShrink": 1,
                    "fontFamily": "Roboto-Light",
                    "fontSize": 18,
                    "letterSpacing": 0.2,
                    "lineHeight": 32,
                  }
                }
              >
                migrationScreen.PayGSubsMigrationOnboardScreen.migrationOnboardDetails.paygInformation
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "backgroundColor": "rgb(255, 255, 255)",
                "paddingBottom": 24,
                "paddingLeft": 33,
                "paddingRight": 30,
                "paddingTop": 10,
              }
            }
          >
            <View
              style={
                {
                  "marginBottom": 26,
                }
              }
              testID="checkboxContainer"
            >
              <Text
                style={
                  {
                    "color": "rgb(0, 0, 0)",
                    "fontFamily": "Roboto-Medium",
                    "fontSize": 16,
                    "letterSpacing": 0.08,
                    "lineHeight": 26,
                    "marginTop": 23,
                  }
                }
              >
                migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.termsAndPolicyCheckbox.header
              </Text>
              <View
                style={
                  {
                    "gap": 16,
                    "marginTop": 23,
                  }
                }
              >
                <View
                  style={
                    {
                      "alignItems": "center",
                      "flexDirection": "row",
                      "marginBottom": 16,
                    }
                  }
                >
                  <View
                    accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.termsAndPolicyCheckbox.termsLabel"
                    accessibilityRole="checkbox"
                    accessibilityState={
                      {
                        "busy": undefined,
                        "checked": false,
                        "disabled": false,
                        "expanded": undefined,
                        "selected": undefined,
                      }
                    }
                    accessible={true}
                    checked={false}
                    disabled={false}
                    focusable={true}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                    style={
                      {
                        "backgroundColor": "#ffffff",
                        "borderColor": "#cccccc",
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "height": 24,
                        "width": 24,
                      }
                    }
                    testID="isTermsChecked"
                  />
                  <Text
                    style={
                      {
                        "color": "#111111d7",
                        "fontFamily": "Roboto-Regular",
                        "fontSize": 14,
                        "letterSpacing": 0.6,
                        "lineHeight": 21,
                        "marginLeft": 16,
                      }
                    }
                  >
                    migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.termsAndPolicyCheckbox.termsLabel
                  </Text>
                </View>
                <View
                  style={
                    {
                      "alignItems": "center",
                      "flexDirection": "row",
                      "marginBottom": 16,
                    }
                  }
                >
                  <View
                    accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.termsAndPolicyCheckbox.policyLabel"
                    accessibilityRole="checkbox"
                    accessibilityState={
                      {
                        "busy": undefined,
                        "checked": false,
                        "disabled": false,
                        "expanded": undefined,
                        "selected": undefined,
                      }
                    }
                    accessible={true}
                    checked={false}
                    disabled={false}
                    focusable={true}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                    style={
                      {
                        "backgroundColor": "#ffffff",
                        "borderColor": "#cccccc",
                        "borderStyle": "solid",
                        "borderWidth": 1,
                        "height": 24,
                        "width": 24,
                      }
                    }
                    testID="isPrivacyChecked"
                  />
                  <Text
                    style={
                      {
                        "color": "#111111d7",
                        "fontFamily": "Roboto-Regular",
                        "fontSize": 14,
                        "letterSpacing": 0.6,
                        "lineHeight": 21,
                        "marginLeft": 16,
                      }
                    }
                  >
                    migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.termsAndPolicyCheckbox.policyLabel
                  </Text>
                </View>
              </View>
              <Text
                style={
                  {
                    "color": "rgb(0, 0, 0)",
                    "fontFamily": "Roboto-Medium",
                    "fontSize": 16,
                    "letterSpacing": 0.08,
                    "lineHeight": 26,
                    "marginTop": 23,
                  }
                }
              >
                migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.header
              </Text>
              <View
                style={
                  {
                    "marginTop": 23,
                  }
                }
              >
                <View
                  style={
                    {
                      "flexDirection": "row",
                      "flexWrap": "nowrap",
                      "marginVertical": 8,
                      "width": "100%",
                    }
                  }
                >
                  <View
                    accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.generalCommunication"
                    accessibilityRole="checkbox"
                    accessibilityState={
                      {
                        "busy": undefined,
                        "checked": undefined,
                        "disabled": undefined,
                        "expanded": undefined,
                        "selected": undefined,
                      }
                    }
                    accessibilityValue={
                      {
                        "max": undefined,
                        "min": undefined,
                        "now": undefined,
                        "text": undefined,
                      }
                    }
                    accessible={true}
                    collapsable={false}
                    focusable={true}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                    style={
                      {
                        "alignItems": "center",
                        "backgroundColor": "#green",
                        "borderColor": "#B3B3B3",
                        "borderWidth": 1,
                        "height": 24,
                        "justifyContent": "center",
                        "opacity": 1,
                        "width": 24,
                      }
                    }
                    testID="isMarketingCommunicationStatusChecked"
                  />
                  <View
                    style={
                      {
                        "display": "flex",
                        "flexBasis": 0,
                        "flexDirection": "row",
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "fontFamily": "Roboto-Regular",
                        "fontSize": 14,
                        "letterSpacing": 0,
                        "lineHeight": 21,
                        "marginLeft": 16,
                      }
                    }
                  >
                    <Text
                      style={
                        {
                          "color": "black",
                          "fontFamily": "Roboto-Regular",
                          "fontSize": 14,
                          "letterSpacing": 0,
                          "lineHeight": 21,
                          "paddingLeft": 6,
                          "width": "100%",
                        }
                      }
                    >
                      migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.generalCommunication
                    </Text>
                  </View>
                </View>
              </View>
              <View
                style={
                  {
                    "gap": 16,
                    "marginTop": 16,
                    "paddingLeft": 38,
                  }
                }
                testID="marketingCheckbox"
              >
                <View
                  style={
                    {
                      "flexDirection": "row",
                      "flexWrap": "nowrap",
                      "marginVertical": 8,
                      "width": "100%",
                    }
                  }
                >
                  <View
                    accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.email"
                    accessibilityRole="checkbox"
                    accessibilityState={
                      {
                        "busy": undefined,
                        "checked": undefined,
                        "disabled": undefined,
                        "expanded": undefined,
                        "selected": undefined,
                      }
                    }
                    accessibilityValue={
                      {
                        "max": undefined,
                        "min": undefined,
                        "now": undefined,
                        "text": undefined,
                      }
                    }
                    accessible={true}
                    collapsable={false}
                    focusable={true}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                    style={
                      {
                        "alignItems": "center",
                        "backgroundColor": "#green",
                        "borderColor": "#B3B3B3",
                        "borderWidth": 1,
                        "height": 24,
                        "justifyContent": "center",
                        "opacity": 1,
                        "width": 24,
                      }
                    }
                    testID="isEmailChecked"
                  />
                  <View
                    style={
                      {
                        "display": "flex",
                        "flexBasis": 0,
                        "flexDirection": "row",
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "fontFamily": "Roboto-Regular",
                        "fontSize": 14,
                        "letterSpacing": 0,
                        "lineHeight": 21,
                        "marginLeft": 16,
                      }
                    }
                  >
                    <Text
                      style={
                        {
                          "color": "black",
                          "fontFamily": "Roboto-Regular",
                          "fontSize": 14,
                          "letterSpacing": 0,
                          "lineHeight": 21,
                          "paddingLeft": 6,
                          "width": "100%",
                        }
                      }
                    >
                      migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.email
                    </Text>
                  </View>
                </View>
                <View
                  style={
                    {
                      "flexDirection": "row",
                      "flexWrap": "nowrap",
                      "marginVertical": 8,
                      "width": "100%",
                    }
                  }
                >
                  <View
                    accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.textMessage"
                    accessibilityRole="checkbox"
                    accessibilityState={
                      {
                        "busy": undefined,
                        "checked": undefined,
                        "disabled": undefined,
                        "expanded": undefined,
                        "selected": undefined,
                      }
                    }
                    accessibilityValue={
                      {
                        "max": undefined,
                        "min": undefined,
                        "now": undefined,
                        "text": undefined,
                      }
                    }
                    accessible={true}
                    collapsable={false}
                    focusable={true}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                    style={
                      {
                        "alignItems": "center",
                        "backgroundColor": "#green",
                        "borderColor": "#B3B3B3",
                        "borderWidth": 1,
                        "height": 24,
                        "justifyContent": "center",
                        "opacity": 1,
                        "width": 24,
                      }
                    }
                    testID="isTextChecked"
                  />
                  <View
                    style={
                      {
                        "display": "flex",
                        "flexBasis": 0,
                        "flexDirection": "row",
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "fontFamily": "Roboto-Regular",
                        "fontSize": 14,
                        "letterSpacing": 0,
                        "lineHeight": 21,
                        "marginLeft": 16,
                      }
                    }
                  >
                    <Text
                      style={
                        {
                          "color": "black",
                          "fontFamily": "Roboto-Regular",
                          "fontSize": 14,
                          "letterSpacing": 0,
                          "lineHeight": 21,
                          "paddingLeft": 6,
                          "width": "100%",
                        }
                      }
                    >
                      migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.textMessage
                    </Text>
                  </View>
                </View>
                <View
                  style={
                    {
                      "flexDirection": "row",
                      "flexWrap": "nowrap",
                      "marginVertical": 8,
                      "width": "100%",
                    }
                  }
                >
                  <View
                    accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.notifications"
                    accessibilityRole="checkbox"
                    accessibilityState={
                      {
                        "busy": undefined,
                        "checked": undefined,
                        "disabled": undefined,
                        "expanded": undefined,
                        "selected": undefined,
                      }
                    }
                    accessibilityValue={
                      {
                        "max": undefined,
                        "min": undefined,
                        "now": undefined,
                        "text": undefined,
                      }
                    }
                    accessible={true}
                    collapsable={false}
                    focusable={true}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                    style={
                      {
                        "alignItems": "center",
                        "backgroundColor": "#green",
                        "borderColor": "#B3B3B3",
                        "borderWidth": 1,
                        "height": 24,
                        "justifyContent": "center",
                        "opacity": 1,
                        "width": 24,
                      }
                    }
                    testID="isPushNotificationsChecked"
                  />
                  <View
                    style={
                      {
                        "display": "flex",
                        "flexBasis": 0,
                        "flexDirection": "row",
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "fontFamily": "Roboto-Regular",
                        "fontSize": 14,
                        "letterSpacing": 0,
                        "lineHeight": 21,
                        "marginLeft": 16,
                      }
                    }
                  >
                    <Text
                      style={
                        {
                          "color": "black",
                          "fontFamily": "Roboto-Regular",
                          "fontSize": 14,
                          "letterSpacing": 0,
                          "lineHeight": 21,
                          "paddingLeft": 6,
                          "width": "100%",
                        }
                      }
                    >
                      migrationScreen.PayGSubsMigrationOnboardScreen.checkBoxDetails.marketingCheckbox.notifications
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            <View
              accessibilityHint="migrationScreen.PayGSubsMigrationOnboardScreen.button.title"
              accessibilityLabel="migrationScreen.PayGSubsMigrationOnboardScreen.button.title"
              accessibilityRole="button"
              accessibilityState={
                {
                  "busy": undefined,
                  "checked": undefined,
                  "disabled": true,
                  "expanded": undefined,
                  "selected": undefined,
                }
              }
              accessibilityValue={
                {
                  "max": undefined,
                  "min": undefined,
                  "now": undefined,
                  "text": undefined,
                }
              }
              accessible={true}
              collapsable={false}
              focusable={true}
              onClick={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                {
                  "alignItems": "center",
                  "backgroundColor": "#00009680",
                  "borderBottomLeftRadius": 23,
                  "borderBottomRightRadius": 23,
                  "borderColor": "transparent",
                  "borderStyle": "solid",
                  "borderTopLeftRadius": 23,
                  "borderTopRightRadius": 23,
                  "borderWidth": 0,
                  "justifyContent": "center",
                  "minHeight": 46,
                  "opacity": 1,
                  "paddingHorizontal": 25.5,
                  "paddingVertical": 12,
                }
              }
              testID="updateAccount"
            >
              <View>
                <Text
                  disabled={true}
                  inverted={false}
                  size="large"
                  style={
                    {
                      "color": "#ffffff",
                      "fontFamily": "Roboto-Regular",
                      "fontSize": 15,
                      "letterSpacing": 0.7,
                      "textAlign": "center",
                    }
                  }
                  type="primary"
                >
                  migrationScreen.PayGSubsMigrationOnboardScreen.button.title
                </Text>
              </View>
            </View>
          </View>
          <View
            style={
              {
                "borderColor": "rgb(193, 192, 192)",
                "borderStyle": "solid",
                "borderWidth": 0.6,
              }
            }
          />
          <View
            style={
              {
                "backgroundColor": "rgb(255, 255, 255)",
                "paddingBottom": 24,
                "paddingLeft": 33,
                "paddingRight": 30,
                "paddingTop": 10,
              }
            }
          >
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 16,
                  "letterSpacing": 0.7,
                  "lineHeight": 24,
                }
              }
            >
              migrationScreen.PayGSubsMigrationOnboardScreen.migrationOnboardDetails.fullDetailsTitle
            </Text>
            <View
              style={
                {
                  "flexDirection": "'row'",
                }
              }
            >
              <Text
                style={
                  {
                    "color": "rgba(17, 17, 17, 0.7)",
                    "fontFamily": "Roboto-Regular",
                    "fontSize": 12,
                    "letterSpacing": 0.6,
                    "lineHeight": 18,
                    "paddingTop": 10,
                  }
                }
                testID="userView"
              >
                migrationScreen.PayGSubsMigrationOnboardScreen.migrationOnboardDetails.description
                <Text
                  onPress={[Function]}
                  style={
                    {
                      "color": "rgb(0, 100, 204)",
                      "paddingTop": 40,
                      "textDecorationLine": "underline",
                    }
                  }
                  testID="linkText"
                >
                  migrationScreen.PayGSubsMigrationOnboardScreen.migrationOnboardDetails.linkPage.label
                </Text>
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "borderColor": "rgb(193, 192, 192)",
                "borderStyle": "solid",
                "borderWidth": 0.6,
              }
            }
          />
        </View>
      </View>
    </RCTScrollView>
  </RCTSafeAreaView>,
  ",",
]
`;
