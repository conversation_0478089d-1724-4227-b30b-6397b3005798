// @ts-nocheck
import { UserTypes } from '@bp/profile-mfe/dist/common/enums';
import { SFConsent, SFConsentChannel } from '@bp/pulse-auth-sdk';
import { SupportedCountries } from '@common/enums';
import { NavigationContainer } from '@react-navigation/native';
import { fireEvent, render } from '@testing-library/react-native';
import { navigate } from '@utils/navigation';
import React from 'react';

import ThemeProvider from '../../../providers/ThemeProvider';
import { prepareConsentValues } from './MigrationOnboardConstants';
import MigrationOnboardScreen from './MigrationOnboardScreen';

jest.mock('@utils/navigation');

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

const useMigrationMockObj = {
  dismiss: jest.fn(),
  required: true,
};

const mockUseMigration = jest.fn().mockReturnValue(useMigrationMockObj);
jest.mock('@providers/MigrationContextProvider', () => ({
  useMigration: () => mockUseMigration(),
}));

const mockUserInfoObj = {
  userInfo: {
    userCountry: SupportedCountries.UK,
    userType: UserTypes.PAYG,
    loggedIn: true,
  },
};

const mockedAppSettings = jest.fn().mockReturnValue(mockUserInfoObj);

jest.mock('@bp/profile-mfe', () => ({
  useAppSettings: () => mockedAppSettings(),
}));

describe('MigrationOnboardScreen', () => {
  const button = 'updateAccount';
  const linkText = 'linkText';
  const userView = 'userView';

  const renderComponent = () =>
    render(
      <NavigationContainer>
        <ThemeProvider>
          <MigrationOnboardScreen />
        </ThemeProvider>
        ,
      </NavigationContainer>,
    );
  it('Should render properly with snapshot', () => {
    const result = renderComponent();
    expect(result.toJSON()).toMatchSnapshot();
  });

  it('should render the page container and elements', () => {
    const { getByTestId } = renderComponent();

    // Check that essential elements exist
    expect(getByTestId('MigrationOnboard')).toBeDefined();
    expect(getByTestId('bgImg')).toBeDefined();
    expect(getByTestId('stepContainer')).toBeDefined();
    expect(getByTestId('checkboxContainer')).toBeDefined();
    expect(getByTestId('isTermsChecked')).toBeDefined();
    expect(getByTestId('isPrivacyChecked')).toBeDefined();
    expect(getByTestId('isEmailChecked')).toBeDefined();
    expect(getByTestId('marketingCheckbox')).toBeDefined();
    expect(getByTestId('infoContainer')).toBeDefined();
    expect(getByTestId(userView)).toBeDefined();
    expect(getByTestId(button)).toBeDefined();
    fireEvent.press(getByTestId(button));
    expect(getByTestId(linkText)).toBeDefined();
    fireEvent.press(getByTestId(linkText));
  });

  it('should hide close button is required flag is true', () => {
    const { queryByTestId } = renderComponent();
    expect(queryByTestId('CloseButton')).toBeNull();
  });

  //Test Close Button interaction
  it('should call the dismiss method when the close button is pressed', () => {
    mockUseMigration.mockImplementation(() => {
      return {
        dismiss: jest.fn(),
        required: false,
      };
    });
    const { getByTestId } = renderComponent();

    const closeButton = getByTestId('CloseButton');

    const handleCloseButton = jest.fn();
    fireEvent.press(closeButton);
    expect(handleCloseButton).toBeTruthy();
  });

  it('does not render terms wrapper for non-subs users', () => {
    const { queryByTestId } = renderComponent();

    // Check if the terms wrapper is not rendered
    expect(queryByTestId('termsWrapper')).toBeNull();
  });

  //  Test if the link opens the correct URL
  it('should open the correct URL when the link is pressed', () => {
    const { getByTestId } = renderComponent();
    const onFAQLinkPress = jest.fn();
    fireEvent.press(getByTestId(linkText));
    expect(onFAQLinkPress).toBeTruthy();
  });

  it('should call handleUpdateAccount method when updateAccount button is pressed', () => {
    const { getByTestId } = renderComponent();
    const isPrivacyChecked = getByTestId('isPrivacyChecked');
    const isTermsChecked = getByTestId('isTermsChecked');
    expect(isPrivacyChecked).toBeDefined();
    expect(isTermsChecked).toBeDefined();

    fireEvent.press(isPrivacyChecked);
    fireEvent.press(isTermsChecked);

    const updateButton = getByTestId('updateAccount');

    const handleUpdateAccount = jest.fn();
    fireEvent.press(updateButton);
    expect(handleUpdateAccount).toBeTruthy();
  });

  it('should call handleCheckboxPress method when checkbox is checked', () => {
    const { getByTestId } = renderComponent();
    const handleCheckboxPress = jest.fn();

    const isEmailChecked = getByTestId('isEmailChecked');
    const isTermsChecked = getByTestId('isTermsChecked');

    fireEvent.press(isEmailChecked);
    expect(handleCheckboxPress).toBeTruthy();

    fireEvent.press(isTermsChecked);
    expect(handleCheckboxPress).toBeTruthy();

    expect(handleCheckboxPress).toBeTruthy();
  });

  it('should navigate the loading screen when updateAccount button is pressed', async () => {
    const { getByTestId } = renderComponent();
    const updateButton = getByTestId('updateAccount');

    fireEvent.press(updateButton);

    expect(navigate).toBeCalledWith('MigrationLoadingScreen', {
      checkBoxState: {
        isPrivacyChecked: true,
        isTermsChecked: true,
      },
      mcheckBoxState: {
        isMarketingCommunicationStatusChecked: false,
        isPushNotificationsChecked: false,
        isEmailChecked: false,
        isTextChecked: false,
      },
    });
  });

  it('should return MarketingConsents values when prepareConsentValues is called', () => {
    const checkBoxState = {
      isPrivacyChecked: true,
      isTermsChecked: true,
    };

    const mcheckBoxState = {
      isPushNotificationsChecked: true,
      isEmailChecked: true,
      isTextChecked: true,
    };
    const result = prepareConsentValues(checkBoxState, mcheckBoxState, true);

    expect(result).toEqual([
      {
        accepted: true,
        consentType: SFConsent.EV_TERMS_AND_CONDITIONS,
        version: '1.0',
      },
      {
        accepted: true,
        consentType: SFConsent.EV_PRIVACY_POLICY,
        version: '1.0',
      },
      {
        accepted: true,
        consentType: SFConsent.EV_MARKETING,
        channel: ['Email' as SFConsentChannel],
      },
      {
        accepted: true,
        consentType: SFConsent.EV_MARKETING_SMS,
        channel: ['SMS' as SFConsentChannel],
      },
      {
        accepted: true,
        consentType: SFConsent.EV_MARKETING_PUSH,
        channel: ['Push' as SFConsentChannel],
      },
    ]);
  });

  it('render terms wrapper for subs users', () => {
    mockedAppSettings.mockReturnValue({
      userInfo: {
        userType: UserTypes.SUBS,
        userCountry: SupportedCountries.UK,
      },
    } as any);
    const { queryByTestId } = renderComponent();

    // Check if the terms wrapper is not rendered
    expect(queryByTestId('termsWrapper')).toBeDefined();
  });
});
