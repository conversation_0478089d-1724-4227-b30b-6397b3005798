import { Platform, StatusBar } from 'react-native';
import styled from 'styled-components/native';

export type SuccessContainerProps = {
  topInset: number;
};
export const SafeAreaView = styled.SafeAreaView`
  flex: 1;
  background-color: #ffffff;
  padding-top: ${Platform.OS === 'android' ? StatusBar.currentHeight : 0}px;
`;
export const SuccessContainer = styled.View<SuccessContainerProps>`
  flex: 4;
  flex-direction: coloum;
  padding: ${({ topInset }: SuccessContainerProps) => topInset + 10}px 0px 0px
    0px;
  background-color: #79e5c5;
`;
export const contentWrapper = styled.ScrollView`
  flex: 1;
  width: 100%;
  background-color: #ffffff;
`;
export const subContentWrapper = styled.View`
  flex: 1;
  width: 100%;
  background-color: #79e5c5;
`;
export const BottomView = styled.View`
  justify-content: flex-end;
  padding: 23px;
`;
export const heading = styled.Text`
  font-size: 20px;
  line-height: 30px;
  color: #000096;
  font-family: 'Roboto-Medium';
  letter-spacing: 0.1px;
  margin-bottom: 16px;
  padding: 0 18px 0px 25px;
  font-weight: 500;
`;
export const infoTitle = styled.Text`
  font-size: 20px;
  line-height: 30px;
  color: #000000;
  font-family: 'Roboto-Medium';
  letter-spacing: 0.1px;
  padding: 0 18px 0px 25px;
  font-weight: 500;
  margin: 10px 0 10px 0;
`;
export const wrapper = styled.View`
  padding: 0 18px 0px 18px;
`;
export const Subtitle = styled.Text`
  font-size: 12px;
  color: #111111b3;
  margin-top: 4px;
  font-weight: regular;
  font-family: 'Roboto-Regular';
  padding: 0px 0 10px 23px;
  background-color: #ffffff;
`;
export const LinkText = styled.Text`
  color: #0064cc;
  text-decoration: underline;
  font-size: 12px;
  font-weight: regular;
  font-family: 'Roboto-Regular';
  background-color: #ffffff;
`;
