import { analyticsEvent } from '@analytics';
import {
  UberMigrationScreenDismissClick,
  UberMigrationScreenMoveToSubsClick,
  UberMigrationScreenOpen,
  UberMigrationScreenUpdateAccountClick,
} from '@analytics/events/UberMigration';
import {
  AtomIconLineCustomPayAsYouGo,
  AtomIconLineCustomTapcard,
  ChargeCar,
  ChargingOnTheGo,
} from '@assets/images';
import { useAppSettings } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { CloseIcon } from '@bp/ui-components/mobile';
import { Button, Header } from '@bp/ui-components/mobile/core';
import { CurveView } from '@components';
import ImageWithTitles from '@components/ImageWithTitles/ImageWithTitles';
import MessageBox from '@components/MessageBox/MessageBox';
import NotificationBanner from '@components/NotificationBanner/NotificationBanner';
import { RowWithIcon } from '@components/RowWithIcon/RowWithIcon';
import { useConfig } from '@providers/ConfigProvider';
import { useMigration } from '@providers/MigrationContextProvider';
import {
  applyBackHandleListener,
  navigate,
  removeBackHandleListener,
} from '@utils/navigation';
import React, { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking, TouchableOpacity } from 'react-native';

import * as S from './UberMigrationOnboardScreen.styles';

const UberMigrationOnboardScreen = () => {
  const { external_links: ExternalLinks } = useConfig();
  const { dismiss, required } = useMigration();
  const { t } = useTranslation();
  const { userInfo } = useAppSettings();
  const { logout } = useAuth();
  const migrationStatusConst =
    userInfo?.migrationUserStatus || 'missing migration status';

  useEffect(() => {
    analyticsEvent(
      UberMigrationScreenOpen({ migration_status: migrationStatusConst }),
    );
    if (required) {
      applyBackHandleListener();
    }
    return () => {
      removeBackHandleListener();
    };
  }, [required, migrationStatusConst]);

  const handlePressLink = useCallback(async () => {
    analyticsEvent(
      UberMigrationScreenUpdateAccountClick({
        migration_status: migrationStatusConst,
      }),
    );

    const uberMicrositeLink = ExternalLinks?.emsp_uber_microsite_link;

    if (uberMicrositeLink) {
      const url = new URL(uberMicrositeLink);
      url.searchParams.append('isMigrating', 'true');
      url.searchParams.append('country', 'UK');
      Linking.openURL(url.toString());
      await logout();
    }
  }, [ExternalLinks?.emsp_uber_microsite_link, logout, migrationStatusConst]);

  const handleNotUberdriverPress = useCallback(() => {
    navigate('MigrationOnboardScreen', { isBackEnable: true }, true);

    analyticsEvent(
      UberMigrationScreenMoveToSubsClick({
        migration_status: migrationStatusConst,
      }),
    );
  }, [migrationStatusConst]);

  return (
    <S.SafeAreaView>
      <S.contentWrapper testID="scrollContainer">
        <S.subContentWrapper>
          <Header
            title={t('migrationScreen.migrationOnboardingScreen.header')}
            fontSize={'20px'}
            fontFamily={'AralPOS-Regular'}
            hasRightButton={!required}
            RightComponent={
              <TouchableOpacity
                style={{ padding: 10 }}
                testID="CloseButton"
                onPress={() => {
                  dismiss();
                  analyticsEvent(
                    UberMigrationScreenDismissClick({
                      migration_status: migrationStatusConst,
                    }),
                  );
                }}
                accessibilityLabel="exit">
                <CloseIcon color="black" />
              </TouchableOpacity>
            }
          />
          <NotificationBanner
            message={t(
              'migrationScreen.migrationOnboardingScreen.5MinutesComplete',
            )}
          />
          <ImageWithTitles
            bgColor="#79e5c5"
            heading={t(
              'migrationScreen.migrationOnboardingScreen.updateNowHeading',
            )}
            subheading={t(
              'migrationScreen.migrationOnboardingScreen.updateNowSubHeading',
            )}
            isUberIcon={true}
            Icon={ChargeCar}
          />
          <CurveView>
            <S.heading>
              {t('migrationScreen.migrationOnboardingScreen.quicklyUpdateAcc')}
            </S.heading>
            <RowWithIcon
              icon={<AtomIconLineCustomPayAsYouGo />}
              title={t(
                'migrationScreen.migrationOnboardingScreen.keepSavingOnEveryCharge',
              )}
              subTitle={t(
                'migrationScreen.migrationOnboardingScreen.keepYourUberdiscounts',
              )}
            />
            <RowWithIcon
              icon={<AtomIconLineCustomTapcard />}
              title={t(
                'migrationScreen.migrationOnboardingScreen.getNewBpPulseChargeCard',
              )}
              subTitle={t(
                'migrationScreen.migrationOnboardingScreen.orderYourNewBpPulseCharge',
              )}
            />
            <RowWithIcon
              icon={<ChargingOnTheGo />}
              title={t(
                'migrationScreen.migrationOnboardingScreen.easilyManageYourCharging',
              )}
              subTitle={t(
                'migrationScreen.migrationOnboardingScreen.viewUberPrices',
              )}
            />
            <S.infoTitle>
              {t(
                'migrationScreen.migrationOnboardingScreen.UberUnlessUpdateAccount',
              )}
            </S.infoTitle>
            <S.BottomView>
              <Button
                accessibilityLabel={t(
                  'migrationScreen.migrationOnboardingScreen.UpdateMyAccount',
                )}
                onPress={handlePressLink}
                testID="updateAccount">
                {t('migrationScreen.migrationOnboardingScreen.UpdateMyAccount')}
              </Button>
            </S.BottomView>
          </CurveView>
          <MessageBox
            title={t(
              'migrationScreen.migrationOnboardingScreen.fullDetailInbox',
            )}
            description={t(
              'migrationScreen.migrationOnboardingScreen.checkYourEmail',
            )}
          />
          <MessageBox
            deviderDesign={{
              marginVertical: 0,
              height: 0,
              borderColor: '#dedede',
              borderBottomWidth: 0.1,
            }}
            description={t(
              'migrationScreen.migrationOnboardingScreen.notUberdriver',
            )}
            linkText={t(
              'migrationScreen.migrationOnboardingScreen.updateYourBpPulseAccount',
            )}
            linkUrl={true}
            handlePress={handleNotUberdriverPress}
            isSingleLine={true}
            testID="linkText"
          />
        </S.subContentWrapper>
      </S.contentWrapper>
    </S.SafeAreaView>
  );
};

export default UberMigrationOnboardScreen;
