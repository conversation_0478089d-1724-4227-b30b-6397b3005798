import { NavigationContainer } from '@react-navigation/native';
import { fireEvent, render } from '@testing-library/react-native';
import { navigate } from '@utils/navigation';
import React from 'react';

import ThemeProvider from '../../../providers/ThemeProvider';
import UberMigrationOnboardScreen from './UberMigrationOnboardScreen';

jest.mock('@env');
jest.mock('@apollo/client');
jest.mock('@utils/navigation');
const useMigrationMockObj = {
  dismiss: jest.fn(),
  required: true,
};
const mockUseMigration = jest.fn().mockReturnValue(useMigrationMockObj);
jest.mock('@providers/MigrationContextProvider', () => ({
  useMigration: () => mockUseMigration(),
}));
const useReturnNavigationMock = () => ({
  setReturnParams: jest.fn(),
  returnNavigate: jest.fn(),
});

jest.mock('@providers/ReturnNavigationProvider', () => ({
  useReturnNavigation: () => useReturnNavigationMock(),
}));

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

describe('UberMigrationOnboardScreen', () => {
  const button = 'updateAccount';
  const linkText = 'linkText';
  const CloseButton = 'CloseButton';
  const renderComponent = () =>
    render(
      <NavigationContainer>
        <ThemeProvider>
          <UberMigrationOnboardScreen />
        </ThemeProvider>
        ,
      </NavigationContainer>,
    );
  it('should render the page container and elements', () => {
    const { getByTestId } = renderComponent();
    expect(getByTestId('scrollContainer')).toBeDefined();
    expect(getByTestId(button)).toBeDefined();
    fireEvent.press(getByTestId(button));
    expect(getByTestId(linkText)).toBeDefined();
    fireEvent.press(getByTestId(linkText));
  });
  it('should hide close button is required flag is true', () => {
    const { queryByTestId } = renderComponent();
    expect(queryByTestId(CloseButton)).toBeNull();
  });

  //Test Close Button interaction
  it('should call the dismiss method when the close button is pressed', () => {
    mockUseMigration.mockImplementation(() => {
      return {
        dismiss: jest.fn(),
        required: false,
      };
    });
    const { getByTestId } = renderComponent();
    const closeButton = getByTestId(CloseButton);
    const handleCloseButton = jest.fn();
    fireEvent.press(closeButton);
    expect(handleCloseButton).toBeTruthy();
  });
  // Text Text available
  it('should render text', () => {
    const { getByText } = renderComponent();
    expect(
      getByText('migrationScreen.migrationOnboardingScreen.header'),
    ).toBeTruthy();
    expect(
      getByText(
        'migrationScreen.migrationOnboardingScreen.updateNowHeading' +
          'migrationScreen.migrationOnboardingScreen.updateNowSubHeading',
      ),
    ).toBeTruthy();

    expect(
      getByText('migrationScreen.migrationOnboardingScreen.quicklyUpdateAcc'),
    ).toBeTruthy();
    expect(
      getByText(
        'migrationScreen.migrationOnboardingScreen.UberUnlessUpdateAccount',
      ),
    ).toBeTruthy();
  });
  //Test button navigation
  it('should call update consent method when link is pressed', async () => {
    const { getByTestId } = renderComponent();
    const updateButton = getByTestId('linkText');
    fireEvent.press(updateButton);
    expect(navigate).toBeCalledWith(
      'MigrationOnboardScreen',
      { isBackEnable: true },
      true,
    );
  });
});
