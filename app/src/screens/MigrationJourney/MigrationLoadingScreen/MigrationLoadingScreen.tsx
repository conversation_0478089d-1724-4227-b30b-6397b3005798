import { useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings } from '@bp/profile-mfe';
import { UserTypes } from '@bp/profile-mfe/dist/common/enums';
import { useAuth } from '@bp/pulse-auth-sdk';
import { SupportedCountries } from '@common/enums';
import { LoaderScreen } from '@components/LoaderScreen.tsx/LoaderScreen';
import { useNetInfo } from '@react-native-community/netinfo';
import { RouteProp, useRoute } from '@react-navigation/native';
import { logger } from '@utils/logger';
import { navigate } from '@utils/navigation';
import { poll } from '@utils/poll';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import {
  CheckBoxState,
  MCheckBoxState,
} from '../MigrationOnboardScreen/interface';
import {
  AnalyticsMigrationEvent,
  handleMigrationAnalytics,
  prepareConsentValues,
} from '../MigrationOnboardScreen/MigrationOnboardConstants';
import { PAGE_NAME } from '../utill/PageUtill';
import * as S from './MigrationLoadingScreen.styles';

type MigrationLoadingScreenRouteProp = RouteProp<
  {
    MigrationLoadingScreen: {
      checkBoxState: CheckBoxState;
      mcheckBoxState: MCheckBoxState;
    };
  },
  'MigrationLoadingScreen'
>;

const MigrationLoadingScreen: React.FC = () => {
  const route = useRoute<MigrationLoadingScreenRouteProp>();
  const { checkBoxState, mcheckBoxState } = route.params || {};
  const authContext = useAuth();
  const { getUser } = authContext;
  const { getStatus, onboardAccount } = useOnboarding();
  const { t } = useTranslation();
  const { userInfo, refetchUserInfo } = useAppSettings();
  const netInfo = useNetInfo();
  const userType = userInfo?.userType;
  const { updateConsent } = useAuth();

  useEffect(() => {
    onInitiateOnboarding();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const navigateUserToSuccessScreen = (UserType: string | undefined) => {
    if (UserType === UserTypes.SUBS) {
      navigate(PAGE_NAME.MIGRATION_SUBS_SUCCESS_SCREEN);
    } else {
      navigate(PAGE_NAME.MIGRATION_PAYG_SUCCESS_SCREEN);
    }
  };

  const pollForOnboardingStatus = async () => {
    return poll({
      fn: getStatus,
      fnCondition: (status: { account: boolean }) => !status.account,
      ms: 5000,
      retryLimit: 11,
    });
  };

  const onboardingFail = (error?: any) => {
    handleMigrationAnalytics(
      AnalyticsMigrationEvent.ERROR,
      userType as UserTypes.SUBS | UserTypes.PAYG,
      {
        error: error,
      },
    );
    navigate(PAGE_NAME.MIGRATION_ERROR);
  };

  const onInitiateOnboarding = async () => {
    logger.info('[Migration]: onInitiateOnboarding called');
    try {
      if (netInfo !== null && !netInfo) {
        navigate(PAGE_NAME.MIGRATION_ERROR);
      }

      //Update Consent
      await updateConsent(
        prepareConsentValues(
          checkBoxState,
          mcheckBoxState,
          userInfo?.userCountry === 'UK' ? true : false,
        ),
      );

      // Refetch latest user details
      const updatedUser = await getUser();
      if (!updatedUser) {
        navigate(PAGE_NAME.MIGRATION_ERROR);
        return;
      }

      // Safeguard against unverified emails attempting to onboard
      const { email, emailVerified, firstName, lastName } = updatedUser;

      if (!emailVerified) {
        navigate(PAGE_NAME.MIGRATION_ERROR);
      }

      // Attempt to onboard account
      const onboardAccountResponse = await onboardAccount({
        firstName,
        lastName,
        email,
        homeCountry: SupportedCountries.UK,
        migratedUser: true,
      });

      if (onboardAccountResponse.success) {
        const onboardingStatusPollResponse = await pollForOnboardingStatus();

        if (onboardingStatusPollResponse?.account) {
          logger.info('[Migration]: onOnboarding success');
          refetchUserInfo();
          navigateUserToSuccessScreen(userType);
          return;
        }
        logger.info('[Migration]: onOnboarding Failed');
        onboardingFail();
        return;
      }
      logger.info('[Migration]: onOnboarding Failed');
      onboardingFail();
    } catch (error) {
      onboardingFail(error);
    }
  };

  return (
    <S.PageWrapper testID="MigrationLoadingScreenPage">
      <LoaderScreen
        header={t('migrationScreen.AccountMigrationLoadingScreen.header')}
        description={t(
          'migrationScreen.AccountMigrationLoadingScreen.descrption',
        )}
      />
    </S.PageWrapper>
  );
};
export default MigrationLoadingScreen;
