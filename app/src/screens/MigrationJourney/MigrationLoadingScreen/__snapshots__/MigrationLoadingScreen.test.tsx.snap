// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MigrationLoadingScreen render properly with snapshot 1`] = `
<View
  style={
    {
      "alignItems": "center",
      "backgroundColor": "rgb(255, 255, 255)",
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
      "justifyContent": "center",
    }
  }
  testID="MigrationLoadingScreenPage"
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "rgb(255, 255, 255)",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "justifyContent": "center",
      }
    }
    testID="loaderScreen"
  >
    <View
      style={
        {
          "justifyContent": "center",
        }
      }
      testID="loadingIndicator"
    >
      <View
        style={
          {
            "alignSelf": "center",
            "marginBottom": 12,
          }
        }
      >
        <View
          collapsable={false}
          style={
            {
              "transform": [
                {
                  "rotate": "0deg",
                },
              ],
            }
          }
        >
          <RNSVGSvgView
            bbHeight="42"
            bbWidth="42"
            focusable={false}
            height="42"
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 42,
                  "width": 42,
                },
              ]
            }
            width="42"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGDefs>
                <RNSVGLinearGradient
                  gradient={
                    [
                      0,
                      -15658735,
                      0.52383,
                      -15658735,
                      1,
                      -15658735,
                    ]
                  }
                  gradientTransform={null}
                  gradientUnits={0}
                  name="a"
                  x1="100%"
                  x2="12.578%"
                  y1="68.182%"
                  y2="12.882%"
                />
              </RNSVGDefs>
              <RNSVGPath
                d="M187 607c-9.941 0-18 8.059-18 18s8.059 18 18 18 18-8.059 18-18"
                fill={null}
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -166,
                    -604,
                  ]
                }
                propList={
                  [
                    "fill",
                    "stroke",
                    "strokeWidth",
                    "strokeLinecap",
                  ]
                }
                stroke={
                  {
                    "brushRef": "a",
                    "type": 1,
                  }
                }
                strokeLinecap={1}
                strokeWidth="4.2"
              />
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
    </View>
    <Text
      style={
        {
          "color": "rgb(17, 17, 17)",
          "fontFamily": "Roboto-Regular",
          "fontSize": 20,
          "letterSpacing": 0.5,
          "lineHeight": 30,
          "paddingBottom": 12,
          "paddingLeft": 39,
          "paddingRight": 39,
          "paddingTop": 14,
          "textAlign": "center",
        }
      }
    >
      migrationScreen.AccountMigrationLoadingScreen.header
    </Text>
    <Text
      style={
        {
          "color": "rgb(17, 17, 17)",
          "fontFamily": "Roboto-Regular",
          "fontSize": 16,
          "letterSpacing": 0.5,
          "lineHeight": 28,
          "paddingLeft": 58,
          "paddingRight": 58,
          "textAlign": "center",
        }
      }
    >
      migrationScreen.AccountMigrationLoadingScreen.descrption
    </Text>
  </View>
</View>
`;
