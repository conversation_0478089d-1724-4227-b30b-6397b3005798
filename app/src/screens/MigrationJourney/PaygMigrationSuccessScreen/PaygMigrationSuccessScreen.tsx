import { analyticsEvent } from '@analytics';
import {
  PayGMigrationScreenDismissClick,
  PayGMigrationScreenFAQClick,
  PayGMigrationScreenMoveToSUBSClick,
  PayGMigrationScreenOrderRFIDClick,
} from '@analytics/events/MigrationFlow';
import {
  CreditCard,
  CurrencyPoundCircle,
  ElectricSign,
  OtgCharge,
} from '@assets/images';
import { useAppSettings } from '@bp/profile-mfe';
import { CloseIcon } from '@bp/ui-components/mobile';
import { Header } from '@bp/ui-components/mobile/core';
import FooterWithButton from '@components/FooterWithButton/FooterWithButton';
import ImageWithTitles from '@components/ImageWithTitles/ImageWithTitles';
import NotificationBanner from '@components/NotificationBanner/NotificationBanner';
import StepProgress from '@components/StepProgress/StepProgress';
import SubsciriptionBenefits from '@components/SubsciriptionBenefits/SubsciriptionBenefits';
import { useConfig } from '@providers/ConfigProvider';
import { useMigration } from '@providers/MigrationContextProvider';
import { useReturnNavigation } from '@providers/ReturnNavigationProvider';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Linking, TouchableOpacity } from 'react-native';

import EasyCharging from '../../../assets/images/png/migrationFlow/easyCharging/21.png';
import * as S from './PaygMigrationSuccessScreen.styles';

const PaygMigrationSuccessScreen = () => {
  const { t } = useTranslation();
  const { external_links: externalLinks } = useConfig();
  const { dismiss } = useMigration();
  const { setReturnParams } = useReturnNavigation();
  const { userInfo } = useAppSettings();
  const migrationStatusConst =
    userInfo?.migrationUserStatus || 'missing migration status';
  const payg_success_steps = [
    {
      no: '1',
      name: t('migrationScreen.SubsMigrationSucessScreen.step.header1'),
    },
    {
      no: '2',
      name: t('migrationScreen.SubsMigrationSucessScreen.step.header2'),
    },
  ];

  const paygBenefitsContent = [
    {
      id: 1,
      icon: ElectricSign,
      contentTitle: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content1Title',
      ),

      content: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content1',
      ),
    },
    {
      id: 2,
      icon: CurrencyPoundCircle,

      contentTitle: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content2Title',
      ),
      content: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content2',
      ),
      boldText: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content2Bold',
      ),
    },
    {
      id: 3,
      icon: CreditCard,
      contentTitle: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content3Title',
      ),
      content: t(
        'migrationScreen.PaygMigrationSucessScreen.subsBenefit.content3',
      ),
    },
  ];

  const onPressHander = () => {
    analyticsEvent(PayGMigrationScreenOrderRFIDClick());
    dismiss();
    setReturnParams({
      screen: 'RFID',
      params: {
        screen: 'RfidOrderReplacementCard',
        params: { userJourney: 'subs_migration' },
      },
    });
  };

  const subscribeNowHandler = () => {
    analyticsEvent(PayGMigrationScreenMoveToSUBSClick());
    dismiss();
    setReturnParams({
      screen: 'Subscription',
      params: {},
    });
  };

  const handleFAQ = () => {
    analyticsEvent(
      PayGMigrationScreenFAQClick({ migration_status: migrationStatusConst }),
    );
    const faqsLink = externalLinks?.emsp_faq_link;
    return faqsLink ? Linking.openURL(faqsLink) : null;
  };

  const handleViewPrice = () => {
    const emsp_view_price_link = externalLinks?.emsp_view_price_link;
    return emsp_view_price_link ? Linking.openURL(emsp_view_price_link) : null;
  };
  const handleCharges = () => {
    const emsp_charges_link = externalLinks?.emsp_charges_link;
    return emsp_charges_link ? Linking.openURL(emsp_charges_link) : null;
  };
  const handleCheckMap = () => {
    dismiss();
    setReturnParams({
      screen: 'map',
      params: {},
    });
  };

  const handleCloseButton = () => {
    analyticsEvent(
      PayGMigrationScreenDismissClick({
        migration_status: migrationStatusConst,
      }),
    );
    dismiss();
  };

  const linkUI = (
    onPress?: () => void,
    getaccessibilityLabel?: string,
    accessibilityHint?: string,
    testID?: string,
    lable?: string,
  ) => {
    return (
      <S.OrderPulseCard
        onPress={onPress}
        accessibilityLabel={getaccessibilityLabel}
        accessibilityHint={accessibilityHint}
        testID={testID}>
        {lable}
      </S.OrderPulseCard>
    );
  };

  return (
    <S.PageContainer testID="PaygMigrationSuccessScreenPage">
      <S.StatusHeader testID="Statusheader">
        <S.HeaderView>
          <Header
            title={t('migrationScreen.PaygMigrationSucessScreen.screenTitle')}
            fontSize={'20px'}
            fontFamily={'AralPOS-Regular'}
            hasRightButton
            RightComponent={
              <TouchableOpacity
                style={{ padding: 10 }}
                onPress={handleCloseButton}
                accessibilityLabel="exit"
                testID="CloseButton">
                <CloseIcon color="black" />
              </TouchableOpacity>
            }
          />
        </S.HeaderView>
      </S.StatusHeader>
      <S.ScreenWrapper testID="pageScroll">
        <NotificationBanner
          message={t('migrationScreen.PaygMigrationSucessScreen.notification')}
        />
        <S.ImgContainer>
          <ImageWithTitles
            bgColor="#AFDFFC"
            heading={t('migrationScreen.PaygMigrationSucessScreen.heading')}
            subheading={t(
              'migrationScreen.PaygMigrationSucessScreen.subHeading',
            )}
            Icon={OtgCharge}
          />

          <S.ContainerWrapper testID="container">
            <S.StepWrapper>
              <StepProgress
                steps={payg_success_steps}
                activeStepCount={1}
                stepContents={[]}
              />
            </S.StepWrapper>

            <S.Header>
              {t('migrationScreen.PaygMigrationSucessScreen.subsBenefit.title')}
            </S.Header>
            <S.CardView>
              <S.payGCardImage source={EasyCharging} />
            </S.CardView>
            <SubsciriptionBenefits data={paygBenefitsContent} />
          </S.ContainerWrapper>
        </S.ImgContainer>
        <S.Container testID="infoContainer">
          <S.Content>
            {t('migrationScreen.PaygMigrationSucessScreen.subsBenefit.info')}
          </S.Content>
        </S.Container>

        <S.Line />

        <S.UserViewWrapper testID="userView">
          <S.UserViewContentTitle>
            {t(
              'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.title1',
            )}
          </S.UserViewContentTitle>
          <S.InnerWrapper>
            <S.UserViewContent>
              {t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.description',
              )}{' '}
              {linkUI(
                () => handleFAQ(),
                t(
                  'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPage.accessibility',
                ),
                t(
                  'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPage.hint',
                ),
                'onPaygFAQLink',
                t(
                  'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPage.label',
                ),
              )}
            </S.UserViewContent>
          </S.InnerWrapper>
        </S.UserViewWrapper>

        <S.Line />

        <S.TCWrapper testID="tcView">
          <S.TermsAndCondition>
            {t(
              'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.termsApplyTitle',
            )}
            {linkUI(
              () => handleViewPrice(),
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageViewPrice.accessibility',
              ),
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageViewPrice.hint',
              ),
              'termsViewPriceLink',
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageViewPrice.label',
              ),
            )}
            {' ' +
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.termsApply1',
              )}
            {t(
              'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.termsApply1Sub',
            )}
            {linkUI(
              () => handleCharges(),
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageCharge.accessibility',
              ),
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageCharge.hint',
              ),
              'termsChargesLink',
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageCharge.label',
              ),
            )}
            {' ' +
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.termsApply2',
              )}
            {linkUI(
              () => handleCheckMap(),
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageCheckMap.accessibility',
              ),
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageCheckMap.hint',
              ),
              'termsCheckMapLink',
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.linkPageCheckMap.label',
              ),
            )}
            {' ' +
              t(
                'migrationScreen.PaygMigrationSucessScreen.membershipBenefitDetails.termsApply3',
              )}
          </S.TermsAndCondition>
        </S.TCWrapper>
      </S.ScreenWrapper>

      <FooterWithButton
        testId={'paygMigrationTestId'}
        buttonText={t('migrationScreen.PaygMigrationSucessScreen.button.text')}
        accessibilityLabel={t(
          'migrationScreen.PaygMigrationSucessScreen.button.accessibility',
        )}
        accessibilityHint={t(
          'migrationScreen.PaygMigrationSucessScreen.button.hint',
        )}
        buttonAction={onPressHander}
        footerMessage={t('migrationScreen.PaygMigrationSucessScreen.footerMsg')}
        footerLinkText={t(
          'migrationScreen.PaygMigrationSucessScreen.footerLink.label',
        )}
        linkAccessibilityLabel={t(
          'migrationScreen.PaygMigrationSucessScreen.footerLink.accessibility',
        )}
        linkAccessibilityHint={t(
          'migrationScreen.PaygMigrationSucessScreen.footerLink.hint',
        )}
        onLinkPress={() => subscribeNowHandler()}
        linkTestId="subscribeNowTestId"
      />
    </S.PageContainer>
  );
};

export default PaygMigrationSuccessScreen;
