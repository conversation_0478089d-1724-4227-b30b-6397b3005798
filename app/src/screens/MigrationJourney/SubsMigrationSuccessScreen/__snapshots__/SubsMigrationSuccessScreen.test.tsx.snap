// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubsMigrationSuccessScreen Should render properly with snapshot 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
    }
  }
  testID="SubsMigrationSuccessScreenPage"
>
  <View
    style={
      {
        "alignItems": "center",
        "flexDirection": "row",
        "justifyContent": "space-between",
      }
    }
    testID="Statusheader"
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingTop": 0,
          "position": "relative",
        }
      }
    >
      <View
        accessible={false}
        backgroundColor="#ffffff"
        border={true}
        hasRightButton={true}
        isElevated={false}
        isRelativePosition={true}
        style={
          {
            "alignItems": "center",
            "backgroundColor": "#ffffff",
            "borderBottomColor": "#dedede",
            "borderBottomWidth": 1,
            "flexDirection": "row",
            "justifyContent": "center",
            "minHeight": 44,
            "paddingBottom": 0,
            "paddingLeft": 50,
            "paddingRight": 50,
            "paddingTop": 0,
            "position": "relative",
            "width": "100%",
            "zIndex": 1,
          }
        }
        testID="Header"
      >
        <Text
          accessibilityHint="a section heading: migrationScreen.SubsMigrationSucessScreen.screenTitle"
          accessibilityLabel="migrationScreen.SubsMigrationSucessScreen.screenTitle"
          alignCenter={true}
          fontFamily="AralPOS-Regular"
          fontSize="20px"
          maxFontSizeMultiplier={2}
          style={
            {
              "color": "#111111",
              "fontFamily": "AralPOS-Regular",
              "fontSize": 20,
              "letterSpacing": 1.2,
              "lineHeight": 27,
              "textAlign": "center",
            }
          }
          testID="migrationScreen.SubsMigrationSucessScreen.screenTitle"
        >
          migrationScreen.SubsMigrationSucessScreen.screenTitle
        </Text>
        <View
          accessibilityHint="closes current screen"
          accessibilityLabel="close"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={false}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "height": 44,
              "justifyContent": "center",
              "marginRight": 8,
              "opacity": 1,
              "position": "absolute",
              "right": 0,
              "width": 44,
            }
          }
          testID="right-button"
        >
          <View
            accessibilityLabel="exit"
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": undefined,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={true}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              {
                "opacity": 1,
                "padding": 10,
              }
            }
          >
            <RNSVGSvgView
              align="xMidYMid"
              bbHeight="16px"
              bbWidth="16px"
              focusable={false}
              height="16px"
              meetOrSlice={0}
              minX={0}
              minY={0}
              style={
                [
                  {
                    "backgroundColor": "transparent",
                    "borderWidth": 0,
                  },
                  {
                    "flex": 0,
                    "height": 16,
                    "width": 16,
                  },
                ]
              }
              vbHeight={16}
              vbWidth={16}
              width="16px"
            >
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
              >
                <RNSVGGroup
                  fill={null}
                  name="illustarations-and-Icons"
                  propList={
                    [
                      "fill",
                      "stroke",
                    ]
                  }
                  stroke={null}
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        -592,
                        -2998,
                      ]
                    }
                    name="Icons"
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          588,
                          2994,
                        ]
                      }
                      name="atom-/-icon-/-line-/-custom-/-close"
                    >
                      <RNSVGPath
                        d="M5.2843055,4.58859116 L5.35355339,4.64644661 L12,11.293 L18.6464466,4.64644661 L18.7156945,4.58859116 C18.9105626,4.45359511 19.179987,4.47288026 19.3535534,4.64644661 C19.5488155,4.84170876 19.5488155,5.15829124 19.3535534,5.35355339 L19.3535534,5.35355339 L12.707,12 L19.3535534,18.6464466 C19.5488155,18.8417088 19.5488155,19.1582912 19.3535534,19.3535534 C19.179987,19.5271197 18.9105626,19.5464049 18.7156945,19.4114088 L18.6464466,19.3535534 L12,12.707 L5.35355339,19.3535534 L5.2843055,19.4114088 C5.08943736,19.5464049 4.82001296,19.5271197 4.64644661,19.3535534 C4.45118446,19.1582912 4.45118446,18.8417088 4.64644661,18.6464466 L4.64644661,18.6464466 L11.293,12 L4.64644661,5.35355339 C4.45118446,5.15829124 4.45118446,4.84170876 4.64644661,4.64644661 C4.82001296,4.47288026 5.08943736,4.45359511 5.2843055,4.58859116 Z"
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        name="Combined-Shape"
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGSvgView>
          </View>
        </View>
      </View>
    </View>
  </View>
  <RCTScrollView
    alwaysBounceVertical={false}
    bounces={false}
    contentContainerStyle={
      {
        "flexGrow": 1,
      }
    }
    keyboardShouldPersistTaps="handled"
    showsVerticalScrollIndicator={false}
    style={
      {
        "backgroundColor": "rgb(255, 255, 255)",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
      }
    }
    testID="pageScroll"
  >
    <View>
      <View
        style={
          {
            "backgroundColor": "rgb(175, 223, 252)",
          }
        }
      >
        <View
          style={
            {
              "alignItems": "center",
              "backgroundColor": "rgb(52, 52, 52)",
              "flexDirection": "row",
              "justifyContent": "center",
              "paddingBottom": 10,
              "paddingLeft": 10,
              "paddingRight": 10,
              "paddingTop": 10,
            }
          }
        >
          < />
          <Text
            style={
              {
                "color": "rgb(255, 255, 255)",
                "fontFamily": "Roboto-Light",
                "fontSize": 18,
                "letterSpacing": 0.2,
                "lineHeight": 32,
                "textAlign": "center",
              }
            }
          >
            migrationScreen.SubsMigrationSucessScreen.notification
          </Text>
        </View>
        <RCTScrollView
          bgColor="#AFDFFC"
          style={
            {
              "backgroundColor": "#AFDFFC",
              "flexBasis": 0,
              "flexGrow": 1,
              "flexShrink": 1,
              "width": "100%",
            }
          }
        >
          <View>
            <View
              style={
                {
                  "backgroundColor": "#000096",
                  "borderBottomLeftRadius": 0,
                  "borderBottomRightRadius": 38.3,
                  "borderTopLeftRadius": 38.3,
                  "borderTopRightRadius": 38.3,
                  "display": "flex",
                  "flexBasis": 0,
                  "flexDirection": "row",
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "marginBottom": -1,
                  "marginLeft": 25,
                  "marginRight": 25,
                  "marginTop": 34,
                  "paddingBottom": 19,
                  "paddingLeft": 19,
                  "paddingRight": 19,
                  "paddingTop": 19,
                }
              }
            >
              <View
                style={
                  {
                    "display": "flex",
                    "paddingRight": 16,
                  }
                }
              >
                < />
              </View>
              <View
                style={
                  {
                    "display": "flex",
                    "flexBasis": 0,
                    "flexDirection": "column",
                    "flexGrow": 0.9,
                    "flexShrink": 1,
                    "marginTop": 5,
                  }
                }
              >
                <View
                  style={
                    {
                      "flexDirection": "row",
                      "paddingRight": 6,
                    }
                  }
                >
                  <Text
                    style={
                      {
                        "color": "#ffffff",
                        "fontFamily": "Roboto-Bold",
                        "fontSize": 30.6,
                        "letterSpacing": 0.32,
                        "lineHeight": 33.4,
                      }
                    }
                  >
                    migrationScreen.SubsMigrationSucessScreen.heading
                    <Text
                      style={
                        {
                          "color": "#9bff00",
                          "fontFamily": "Roboto-Bold",
                          "fontSize": 30.6,
                          "letterSpacing": 0.32,
                          "lineHeight": 33.4,
                        }
                      }
                    >
                      migrationScreen.SubsMigrationSucessScreen.subHeading
                    </Text>
                  </Text>
                </View>
              </View>
            </View>
            <View
              style={
                {
                  "display": "flex",
                  "flexBasis": 0,
                  "flexDirection": "row",
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "marginLeft": 25,
                }
              }
            />
          </View>
        </RCTScrollView>
        <View
          style={
            {
              "backgroundColor": "rgb(255, 255, 255)",
              "borderBottomLeftRadius": 0,
              "borderBottomRightRadius": 38.3,
              "borderTopLeftRadius": 38.3,
              "borderTopRightRadius": 38.3,
              "display": "flex",
              "marginTop": 50,
              "paddingBottom": 0,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 0,
            }
          }
          testID="container"
        >
          <View
            style={
              {
                "paddingBottom": 20,
              }
            }
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "justifyContent": "center",
                }
              }
            >
              <View
                style={
                  {
                    "flexDirection": "row",
                    "justifyContent": "center",
                    "width": "100%",
                  }
                }
              >
                <View
                  style={
                    {
                      "alignItems": "center",
                      "flexDirection": "row",
                    }
                  }
                >
                  <View
                    active={false}
                    prevStep={true}
                    style={
                      {
                        "alignItems": "center",
                        "backgroundColor": "green",
                        "borderBottomLeftRadius": 20,
                        "borderBottomRightRadius": 20,
                        "borderColor": "grey",
                        "borderTopLeftRadius": 20,
                        "borderTopRightRadius": 20,
                        "borderWidth": 0.15,
                        "height": 30,
                        "justifyContent": "center",
                        "width": 30,
                      }
                    }
                    testID="stepper1"
                  >
                    <Image
                      source=""
                      style={
                        {
                          "width": 18,
                        }
                      }
                      testID="stepperImg1"
                    />
                  </View>
                  <View
                    style={
                      {
                        "alignItems": "center",
                        "flexBasis": 0,
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "justifyContent": "space-between",
                      }
                    }
                  >
                    <View
                      style={
                        {
                          "color": "black",
                          "height": 100,
                          "paddingLeft": 10,
                          "paddingTop": 60,
                          "width": 100,
                        }
                      }
                    >
                      <Text
                        style={
                          {
                            "color": "black",
                            "fontFamily": "Roboto-Regular",
                            "fontSize": 16,
                            "height": 300,
                            "lineHeight": 20,
                            "marginLeft": -23,
                            "marginTop": 20,
                            "textAlign": "center",
                            "width": "100%",
                          }
                        }
                        testID="stepName1"
                      >
                        migrationScreen.SubsMigrationSucessScreen.step.header1
                      </Text>
                    </View>
                  </View>
                </View>
                <View
                  style={
                    {
                      "alignItems": "center",
                      "flexDirection": "row",
                    }
                  }
                >
                  <View
                    completed={true}
                    isShortLine={true}
                    isTwoStep={true}
                    style={
                      {
                        "backgroundColor": "black",
                        "height": 1,
                        "width": 210,
                      }
                    }
                  />
                  <View
                    active={true}
                    prevStep={false}
                    style={
                      {
                        "alignItems": "center",
                        "backgroundColor": "black",
                        "borderBottomLeftRadius": 20,
                        "borderBottomRightRadius": 20,
                        "borderColor": "grey",
                        "borderTopLeftRadius": 20,
                        "borderTopRightRadius": 20,
                        "borderWidth": 0.15,
                        "height": 30,
                        "justifyContent": "center",
                        "width": 30,
                      }
                    }
                    testID="stepper2"
                  >
                    <Text
                      active={true}
                      style={
                        {
                          "color": "white",
                          "fontSize": 12,
                        }
                      }
                      testID="step2"
                    >
                      2
                    </Text>
                  </View>
                  <View
                    style={
                      {
                        "alignItems": "center",
                        "flexBasis": 0,
                        "flexGrow": 1,
                        "flexShrink": 1,
                        "justifyContent": "space-between",
                      }
                    }
                  >
                    <View
                      style={
                        {
                          "color": "black",
                          "height": 100,
                          "paddingLeft": 10,
                          "paddingTop": 60,
                          "width": 100,
                        }
                      }
                    >
                      <Text
                        style={
                          {
                            "color": "black",
                            "fontFamily": "Roboto-Regular",
                            "fontSize": 16,
                            "height": 300,
                            "lineHeight": 20,
                            "marginLeft": -23,
                            "marginTop": 20,
                            "textAlign": "center",
                            "width": "100%",
                          }
                        }
                        testID="stepName2"
                      >
                        migrationScreen.SubsMigrationSucessScreen.step.header2
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
              <View
                style={
                  {
                    "flexBasis": 0,
                    "flexDirection": "row",
                    "flexGrow": 1,
                    "flexShrink": 1,
                    "marginTop": 10,
                  }
                }
              />
            </View>
          </View>
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 28,
                "letterSpacing": 0.5,
                "lineHeight": 40,
                "paddingTop": 30,
                "textAlign": "center",
              }
            }
          >
            migrationScreen.SubsMigrationSucessScreen.subsBenefit.title
          </Text>
          <View
            style={
              {
                "backgroundColor": "rgba(255, 255, 255, 0.92)",
                "paddingBottom": 10,
                "paddingLeft": 54,
                "paddingRight": 54,
                "paddingTop": 10,
              }
            }
            testID="subsciriptionBenefits"
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingTop": 17,
                }
              }
            >
              < />
            </View>
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 16,
                  "fontWeight": "400",
                  "letterSpacing": 0.15,
                  "lineHeight": 28,
                  "textAlign": "center",
                }
              }
            />
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Light",
                  "fontSize": 16,
                  "letterSpacing": 0.15,
                  "lineHeight": 28,
                  "textAlign": "center",
                }
              }
            >
              migrationScreen.SubsMigrationSucessScreen.subsBenefit.content1
            </Text>
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingTop": 17,
                }
              }
            >
              < />
            </View>
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 16,
                  "fontWeight": "400",
                  "letterSpacing": 0.15,
                  "lineHeight": 28,
                  "textAlign": "center",
                }
              }
            />
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Light",
                  "fontSize": 16,
                  "letterSpacing": 0.15,
                  "lineHeight": 28,
                  "textAlign": "center",
                }
              }
            >
              migrationScreen.SubsMigrationSucessScreen.subsBenefit.content2part0
               
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "fontFamily": "Roboto-Bold",
                    "fontSize": 16,
                    "letterSpacing": 0.15,
                    "lineHeight": 28,
                    "textAlign": "center",
                  }
                }
              >
                migrationScreen.SubsMigrationSucessScreen.subsBenefit.content2part1
              </Text>
               
              migrationScreen.SubsMigrationSucessScreen.subsBenefit.content2part2
            </Text>
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingTop": 17,
                }
              }
            >
              < />
            </View>
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 16,
                  "fontWeight": "400",
                  "letterSpacing": 0.15,
                  "lineHeight": 28,
                  "textAlign": "center",
                }
              }
            />
            <Text
              style={
                {
                  "color": "rgb(17, 17, 17)",
                  "fontFamily": "Roboto-Light",
                  "fontSize": 16,
                  "letterSpacing": 0.15,
                  "lineHeight": 28,
                  "textAlign": "center",
                }
              }
            >
              migrationScreen.SubsMigrationSucessScreen.subsBenefit.content3
            </Text>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "backgroundColor": "white",
            "marginBottom": 0,
            "paddingBottom": 30,
            "paddingLeft": 24,
            "paddingRight": 24,
            "paddingTop": 20,
            "width": "100%",
          }
        }
      >
        <View
          accessibilityHint="migrationScreen.SubsMigrationSucessScreen.subsBenefit.button.hint"
          accessibilityLabel="migrationScreen.SubsMigrationSucessScreen.subsBenefit.button.accessibility"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#ffffff",
              "borderBottomLeftRadius": 28,
              "borderBottomRightRadius": 28,
              "borderColor": "#000096",
              "borderStyle": "solid",
              "borderTopLeftRadius": 28,
              "borderTopRightRadius": 28,
              "borderWidth": 1,
              "justifyContent": "center",
              "minHeight": 56,
              "opacity": 1,
              "paddingHorizontal": 29.5,
              "paddingVertical": 15,
            }
          }
          testID="keepMySubscription"
        >
          <View>
            <Text
              disabled={false}
              inverted={false}
              size="xlarge"
              style={
                {
                  "color": "#111111",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 16,
                  "letterSpacing": 0.7,
                  "textAlign": "center",
                }
              }
              type="secondary"
            >
              migrationScreen.SubsMigrationSucessScreen.subsBenefit.button.text
            </Text>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "backgroundColor": "rgb(255, 255, 240)",
            "borderColor": "rgb(255, 238, 92)",
            "borderStyle": "solid",
            "borderWidth": 1,
            "paddingBottom": 20,
            "paddingLeft": 14,
            "paddingRight": 24,
            "paddingTop": 20,
          }
        }
        testID="infoContainer"
      >
        <View
          style={
            {
              "flexDirection": "row",
            }
          }
        >
          <View
            style={
              {
                "paddingBottom": 5,
                "paddingLeft": 5,
                "paddingRight": 5,
                "paddingTop": 5,
              }
            }
          >
            < />
          </View>
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 18,
                "letterSpacing": 0.2,
                "lineHeight": 32,
              }
            }
          >
            migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.information
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "backgroundColor": "rgb(255, 255, 255)",
            "paddingBottom": 20,
            "paddingLeft": 24,
            "paddingRight": 24,
            "paddingTop": 20,
          }
        }
        testID="userView"
      >
        <Text
          style={
            {
              "color": "rgb(17, 17, 17)",
              "fontFamily": "Roboto-Regular",
              "fontSize": 16,
              "letterSpacing": 0.7,
              "lineHeight": 24,
            }
          }
        >
          migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.title1
        </Text>
        <View
          style={
            {
              "flexDirection": "'row'",
            }
          }
        >
          <Text
            style={
              {
                "color": "rgba(17, 17, 17, 0.7)",
                "flexShrink": 1,
                "fontFamily": "Roboto-Regular",
                "fontSize": 12,
                "letterSpacing": 0.6,
                "lineHeight": 18,
                "paddingTop": 10,
              }
            }
          >
            migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.description
             
            <Text
              accessibilityHint="migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.linkPage.hint"
              accessibilityLabel="migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.linkPage.accessibility"
              onPress={[Function]}
              style={
                {
                  "color": "rgb(0, 100, 204)",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 12,
                  "letterSpacing": 0.6,
                  "lineHeight": 18,
                  "paddingTop": 50,
                  "textDecorationLine": "underline",
                }
              }
              testID="onPressBPPulseChargeCard"
            >
              migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.linkPage.label
            </Text>
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "borderColor": "rgb(193, 192, 192)",
            "borderStyle": "solid",
            "borderWidth": 0.6,
          }
        }
      />
      <View
        style={
          {
            "backgroundColor": "rgb(255, 255, 255)",
            "flexDirection": "'row'",
            "paddingBottom": 35,
            "paddingLeft": 33,
            "paddingRight": 30,
            "paddingTop": 18,
          }
        }
        testID="tcView"
      >
        <Text
          style={
            {
              "color": "rgb(17, 17, 17)",
              "fontFamily": "Roboto-Regular",
              "fontSize": 12,
              "letterSpacing": 0.18,
              "lineHeight": 23,
            }
          }
        >
          migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.termsApplyNewPart1
           
          <Text
            onPress={[Function]}
            style={
              {
                "color": "rgb(0, 100, 204)",
                "fontFamily": "Roboto-Regular",
                "fontSize": 12,
                "letterSpacing": 0.6,
                "lineHeight": 18,
                "paddingTop": 50,
                "textDecorationLine": "underline",
              }
            }
          >
            migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.termsApplyLinkLabel1
          </Text>
           
          migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.termsApplyNewPart2
           
          <Text
            onPress={[Function]}
            style={
              {
                "color": "rgb(0, 100, 204)",
                "fontFamily": "Roboto-Regular",
                "fontSize": 12,
                "letterSpacing": 0.6,
                "lineHeight": 18,
                "paddingTop": 50,
                "textDecorationLine": "underline",
              }
            }
          >
            migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.termsApplyLinkLabel2
          </Text>
           
          migrationScreen.SubsMigrationSucessScreen.membershipBenefitDetails.termsApplyNewPart3
           
        </Text>
      </View>
    </View>
  </RCTScrollView>
  <View
    style={
      {
        "backgroundColor": "white",
        "paddingBottom": 24,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 24,
        "shadowColor": "grey",
        "shadowOffset": {
          "height": 50,
          "width": 0,
        },
        "shadowOpacity": 1,
        "shadowRadius": 40,
        "width": "100%",
      }
    }
  >
    <View
      accessibilityHint="migrationScreen.SubsMigrationSucessScreen.subsBenefit.button.hint"
      accessibilityLabel="migrationScreen.SubsMigrationSucessScreen.subsBenefit.button.accessibility"
      accessibilityRole="button"
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": false,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#000096",
          "borderBottomLeftRadius": 28,
          "borderBottomRightRadius": 28,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 28,
          "borderTopRightRadius": 28,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 56,
          "opacity": 1,
          "paddingHorizontal": 29.5,
          "paddingVertical": 15,
        }
      }
      testID="SubsMigrationTestId"
    >
      <View>
        <Text
          disabled={false}
          inverted={false}
          size="xlarge"
          style={
            {
              "color": "#ffffff",
              "fontFamily": "Roboto-Regular",
              "fontSize": 16,
              "letterSpacing": 0.7,
              "textAlign": "center",
            }
          }
          type="primary"
        >
          migrationScreen.SubsMigrationSucessScreen.subsBenefit.button.text
        </Text>
      </View>
    </View>
    <View
      style={
        {
          "alignSelf": "center",
          "backgroundColor": "rgb(255, 255, 255)",
          "flexDirection": "'column'",
          "justifyContent": "center",
          "paddingBottom": 20,
          "paddingLeft": 20,
          "paddingRight": 20,
          "paddingTop": 20,
        }
      }
      testID="footerData"
    >
      <Text
        style={
          {
            "color": "rgba(17, 17, 17, 0.7)",
            "fontFamily": "Roboto-Regular",
            "fontSize": 13,
            "letterSpacing": 0.2,
            "lineHeight": 18,
            "paddingTop": 10,
            "textAlign": "center",
          }
        }
      >
        migrationScreen.SubsMigrationSucessScreen.footerMsg
        <Text
          accessibilityHint="migrationScreen.SubsMigrationSucessScreen.footerLink.hint"
          accessibilityLabel="migrationScreen.SubsMigrationSucessScreen.footerLink.accessibility"
          onPress={[Function]}
          style={
            {
              "color": "rgb(0, 100, 204)",
            }
          }
          testID="onSubsFAQLink"
        >
           
          migrationScreen.SubsMigrationSucessScreen.footerLink.label
        </Text>
      </Text>
    </View>
  </View>
</RCTSafeAreaView>
`;
