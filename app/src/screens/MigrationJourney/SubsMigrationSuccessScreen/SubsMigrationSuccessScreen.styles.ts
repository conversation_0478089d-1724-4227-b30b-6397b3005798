import { Dimensions, Platform } from 'react-native';
import styled from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 24 : 8;

export const PageContainer = styled.SafeAreaView`
  flex: 1;
`;

export const ScreenWrapper = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    flexGrow: 1,
  },
  keyboardShouldPersistTaps: 'handled',
  showsVerticalScrollIndicator: false,
  alwaysBounceVertical: false,
  bounces: false,
}))`
  flex: 1;
  background-color: rgb(255, 255, 255);
`;
export const ImgContainer = styled.View`
  background-color: rgb(175, 223, 252);
`;

export const ContainerWrapper = styled.View`
  background-color: rgb(255, 255, 255);
  display: flex;
  margin-top: 50px;
  padding: 0px;
  border-radius: 38.3px 38.3px 38.3px 0px;
`;

export const StepWrapper = styled.View`
  padding-bottom: 20px;
`;
export const HeaderView = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding-top: ${Platform.OS === 'android' ? 35 : 0}px;
`;

export const Header = styled.Text`
  padding-top: 30px;
  font-family: Roboto-Light;
  font-size: 28px;
  line-height: 40px;
  text-align: center;
  letter-spacing: 0.5px;
  color: rgb(17, 17, 17);
`;
export const InfoHeader1 = styled.Text`
  font-family: Roboto-Bold;
  font-size: 16px;
  line-height: 26px;
  font-weight: bold;
  color: rgb(17, 17, 17);
`;

export const ButtonContainer = styled.View`
  background-color: white;
  width: 100%;
  padding: 20px ${phoneWidth}px;
  margin-bottom: 0px;
  padding-bottom: 30px;
`;

export const Container = styled.View`
  background-color: rgb(255, 255, 240);
  border: 1px solid rgb(255, 238, 92);
  padding: 20px 24px 20px 14px;
`;
export const InfoWrapper = styled.View`
  flex-direction: row;
`;

export const InfoIconContainer = styled.View`
  padding: 5px;
`;

export const InfoContent = styled.Text`
  font-family: Roboto-Light;
  font-size: 18px;
  line-height: 32px;
  letter-spacing: 0.2px;
  flex-shrink: 1;
  color: rgb(17, 17, 17);
`;
export const UserViewWrapper = styled.View`
  padding: 20px 24px 20px 24px;
  background-color: rgb(255, 255, 255);
`;

export const UserViewContentTitle = styled.Text`
  font-family: Roboto-Regular;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.7px;
  color: rgb(17, 17, 17);
`;
export const BoldText = styled.Text`
  font-family: Roboto-Bold;
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.15px;
  text-align: center;
  color: rgb(17, 17, 17);
`;
export const InnerWrapper = styled.View`
  flex-direction: 'row';
`;
export const UserViewContent = styled.Text`
  font-family: Roboto-Regular;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0.6px;
  padding-top: 10px;
  flex-shrink: 1;
  color: rgba(17, 17, 17, 0.7);
`;

export const OrderPulseCard = styled.Text`
  color: rgb(0, 100, 204);
  text-decoration-line: underline;
  padding-top: 50px;
  font-family: Roboto-Regular;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0.6px;
`;

export const Line = styled.View`
  border: 0.6px solid rgb(193, 192, 192);
`;
export const TCWrapper = styled.View`
  flex-direction: 'row';
  padding-left: 33px;
  padding-right: 30px;
  padding-top: 18px;
  padding-bottom: 35px;
  background-color: rgb(255, 255, 255);
`;
export const TermsAndCondition = styled.Text`
  font-family: Roboto-Regular;
  font-size: 12px;
  line-height: 23px;
  letter-spacing: 0.18px;
  color: rgb(17, 17, 17);
`;

export const BottomContainer = styled.View`
  background-color: white;
  width: 100%;
  padding: 20px ${phoneWidth}px;
  margin-bottom: 0;
  padding-bottom: 30px;
  box-shadow: 0px 50px 40px grey;
  align-items: center;
  justify-content: center;
  padding-bottom: 0px;
`;
export const FooterData = styled.View`
  background-color: rgb(255, 255, 255);
  padding: 20px;
  justify-content: center;
  align-self: center;
`;

export const FooterInfo = styled.Text`
  font-family: Roboto-Regular;
  font-size: 13px;
  line-height: 18px;
  letter-spacing: 0.2px;
  padding-top: 10px;
  text-align: center;
  color: rgba(17, 17, 17, 0.7);
`;

export const StatusHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const HeaderTitle = styled.Text`
  font-size: 20px;
  text-align: center;
  color: #111111;
  letter-spacing: 0.15px;
  font-family: 'Roboto-Regular';
  margin-left: auto;
  margin-right: auto;
  line-height: 30px;
  padding-top: 9px;
  padding-bottom: 9px;
`;

export const CloseButtonStyle = styled.View`
  padding-right: 12px;
`;
export const LinkText = styled.Text`
  color: rgb(0, 100, 204);
  text-decoration-line: underline;
  font-family: Roboto-Regular;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0.6px;
`;
