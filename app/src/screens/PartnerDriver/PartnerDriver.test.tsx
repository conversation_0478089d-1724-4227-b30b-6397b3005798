import { render } from '@utils/test-utils';
import React from 'react';

import PartnerDriver from './PartnerDriver';

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
}));

describe('<PartnerDriver />', () => {
  beforeEach(() => {});

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should render Partner Driver', () => {
    render(<PartnerDriver />);
  });
});
