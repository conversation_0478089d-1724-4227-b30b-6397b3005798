import { useAuth } from '@bp/pulse-auth-sdk';
import * as RemovePartnerDriverStatusHooks from '@graphql/hooks/useRemovePartnerDriverStatus';
import { PartnerDriverType } from '@graphql/mutations/removePartnerDriverStatus';
import { useRoute } from '@react-navigation/native';
import { render, waitFor } from '@utils/test-utils';
import React from 'react';

import UberProDisconnectScreen from './UberProDisconnect';

jest.mock('@utils/navigation');
jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

const mockedAuth = jest.mocked(useAuth);
const updateConsentMock = jest.fn();
const removePartnerDriverStatusMutation = jest.fn().mockResolvedValue({
  data: { removePartnerDriverStatus: { status: 200 } },
});

jest
  .spyOn(RemovePartnerDriverStatusHooks, 'useRemovePartnerDriverStatus')
  .mockReturnValue(removePartnerDriverStatusMutation);

mockedAuth.mockReturnValue({
  updateConsent: updateConsentMock,
  authenticated: true,
  user: {
    userId: 'xxx',
  },
} as any);

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useRoute: jest.fn(),
}));

beforeEach(() => {
  (useRoute as jest.Mock).mockReturnValue({
    params: {
      path: 'partner_unlink',
    },
  });
  jest.clearAllMocks();
});

describe('<UberProDisconnectScreen />', () => {
  it('should show loading screen', () => {
    const { getByTestId } = render(<UberProDisconnectScreen />);

    const modal = getByTestId('UberProDisconnectScreen');
    expect(modal).not.toBeNull();
  });

  it('should remove partner driver status', () => {
    render(<UberProDisconnectScreen />);

    expect(removePartnerDriverStatusMutation).toBeCalledWith({
      variables: { userId: 'xxx', partnerType: PartnerDriverType.Uber },
    });
  });

  it('should set ridehailing consent to false', async () => {
    render(<UberProDisconnectScreen />);

    await waitFor(() =>
      expect(updateConsentMock).toBeCalledWith([
        {
          accepted: false,
          consentType: 'EV Ridehailing',
        },
      ]),
    );
  });
});
