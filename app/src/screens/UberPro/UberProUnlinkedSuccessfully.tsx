import { analyticsEvent } from '@analytics';
import {
  UnlinkAccountSuccessFindChargerClick,
  UnlinkAccountSuccessSubscribeClick,
  USER_JOURNEY,
} from '@analytics/events/Uber/UnlinkAccounts';
import { SuccessCheckIcon } from '@assets/images';
import { SubscriptionScreenNames } from '@bp/mfe-subscription';
import { Button, ButtonAction } from '@bp/ui-components/mobile/core';
import { useRoute } from '@react-navigation/native';
import { getUserJourney } from '@utils/getUserJourney';
import { navigate } from '@utils/navigation';
import React from 'react';
import { useTranslation } from 'react-i18next';

import * as S from './UberProUnlinkedSuccessfully.styles';

const UberProUnlinkedSuccessfully = () => {
  const { t } = useTranslation();
  const route = useRoute();

  const handleSetupSubscription = () => {
    const user_journey = getUserJourney(route);
    analyticsEvent(
      UnlinkAccountSuccessSubscribeClick({ user_journey: user_journey }),
    );
    navigate(
      'Subscription',
      {
        screen: SubscriptionScreenNames.JoinGoCardless,
        params: {
          userJourney: USER_JOURNEY.PARTNER_UNLINK,
        },
      },
      true,
    );
  };

  const handleFindCharger = () => {
    const user_journey = getUserJourney(route);
    analyticsEvent(
      UnlinkAccountSuccessFindChargerClick({ user_journey: user_journey }),
    );
    navigate('Tabs', { screen: 'Map' }, true);
  };

  return (
    <>
      <S.SafeAreaView testID="UberProUnlikedSuccessfullyScreen">
        <S.ScrollContainer>
          <S.Heading
            accessibilityLabel={t('uber.unlinkedSuccessfully.heading')}
            testID="UberProUnlikedSuccessfullyHeading">
            {t('uber.unlinkedSuccessfully.heading')}
          </S.Heading>
          <S.ImageContainer testID="UberProUnlikedSuccessfullyImageContainer">
            <SuccessCheckIcon />
          </S.ImageContainer>
          <S.Subheading
            accessibilityLabel={t('uber.unlinkedSuccessfully.subheading')}
            testID="UberProUnlikedSuccessfullySubheading">
            {t('uber.unlinkedSuccessfully.subheading')}
          </S.Subheading>

          <S.ActionButtonContainer testID="UberProUnlikedSuccessfullyActionButtonContainer">
            <S.ActionButtonInfoText>
              {t('uber.unlinkedSuccessfully.buttonInfo')}
            </S.ActionButtonInfoText>
            <S.CommonButtonWrapper>
              <Button
                onPress={handleSetupSubscription}
                accessibilityLabel={t(
                  'uber.unlinkedSuccessfully.setupSubcription',
                )}
                testID="UberProUnlikedSuccessfullyActionButton">
                {t('uber.unlinkedSuccessfully.setupSubcription')}
              </Button>

              <S.ButtonContainer testID="UberProUnlikedSuccessfullyActionButtonContainer">
                <Button
                  onPress={handleFindCharger}
                  type={ButtonAction.SECONDARY}
                  accessibilityLabel={t(
                    'uber.unlinkedSuccessfully.findCharger',
                  )}
                  testID="UberProUnlikedSuccessfullyCancelActionButton">
                  {t('uber.unlinkedSuccessfully.findCharger')}
                </Button>
              </S.ButtonContainer>
            </S.CommonButtonWrapper>
          </S.ActionButtonContainer>
        </S.ScrollContainer>
      </S.SafeAreaView>
    </>
  );
};

export default UberProUnlinkedSuccessfully;
