import { styled } from 'styled-components/native';

export const Container = styled.ScrollView`
  background-color: rgb(255, 255, 255);
`;

export const SafeAreaView = styled.SafeAreaView`
  flex: 1;
`;

export const PulseUberImage = styled.Image`
  width: 100%;
  height: 253px;
`;

export const HeaderContainer = styled.View`
  margin: 16px 24px;
`;

export const Header = styled.Text`
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0.27px;
`;

export const DescriptionContainer = styled.View`
  margin-left: 24px;
  margin-right: 24px;
  margin-bottom: 16px;
`;

export const Description = styled.Text`
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.1px;
  color: rgb(17, 17, 17);
`;

export const BottomTextContainer = styled.View`
  margin-left: 24px;
  margin-right: 24px;
`;

export const BottomText = styled.Text`
  font-size: 13px;
  letter-spacing: 0.2px;
  line-height: 23px;
  color: rgb(17, 17, 17);
`;

export const ButtonSection = styled.View`
  margin-top: 10px;
  justify-content: center;
  gap: 16px;
  margin-left: 24px;
  margin-right: 24px;
`;

export const InformationComponentWrapper = styled.View`
  position: absolute;
  bottom: 0px;
  background-color: rgb(255, 255, 255);
`;

export const Link = styled.Text`
  text-align: center;
  font-size: 13px;
  align-items: flex-start;
  color: #000096;
`;
