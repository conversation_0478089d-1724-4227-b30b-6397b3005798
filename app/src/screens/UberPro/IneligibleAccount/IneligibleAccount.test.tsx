import { useAuth } from '@bp/pulse-auth-sdk';
import * as UnblockPartnerTokenHook from '@graphql/hooks/useUnblockPartnerToken';
import { useRoute } from '@react-navigation/native';
import { fireEvent, render, waitFor } from '@utils/test-utils';
import React from 'react';

import IneligibleAccount from './IneligibleAccount';

jest.mock('@utils/navigation');

const mockedAuth = jest.mocked(useAuth);
const unblockPartnerTokenMutation = jest.fn().mockResolvedValue({
  data: { unblockPartnerToken: { status: 200 } },
});

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useRoute: jest.fn(),
}));

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

jest
  .spyOn(UnblockPartnerTokenHook, 'useUnblockPartnerToken')
  .mockReturnValue(unblockPartnerTokenMutation);

mockedAuth.mockReturnValue({
  user: {
    userId: 'xxx',
  },
} as any);

beforeEach(() => {
  (useRoute as jest.Mock).mockReturnValue({
    params: {
      userJourney: 'partner_unlink',
    },
  });
  jest.clearAllMocks();
});

describe('<IneligibleAccount />', () => {
  it('should correctly render component', () => {
    const { getByTestId } = render(<IneligibleAccount />);
    const descriptionText = getByTestId('ineligible-account-screen-container');
    expect(descriptionText).toBeDefined();
  });

  it('should unblock partner token when click on try again', async () => {
    const { getByTestId } = render(<IneligibleAccount />);

    const tryAgainButton = getByTestId(
      'ineligible-account-screen-retry-button',
    );

    fireEvent.press(tryAgainButton);

    await waitFor(() => {
      expect(getByTestId('loadingIndicator')).toBeTruthy();
    });

    expect(unblockPartnerTokenMutation).toHaveBeenCalledTimes(1);
  });

  it('should open unlink modal when click on unlink button', async () => {
    const { getByTestId } = render(<IneligibleAccount />);

    const unlinkAccountButton = getByTestId(
      'ineligible-account-screen-unlink-button',
    );

    fireEvent.press(unlinkAccountButton);

    await waitFor(() => {
      expect(getByTestId('TouchableWithoutFeedback')).toBeTruthy();
    });
  });
});
