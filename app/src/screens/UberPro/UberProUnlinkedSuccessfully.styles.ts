import styled from 'styled-components/native';

export const SafeAreaView = styled.SafeAreaView`
  flex: 1;
`;

export const ScrollContainer = styled.ScrollView.attrs({
  contentContainerStyle: {
    flexGrow: 1,
  },
  alwaysBounceVertical: false,
})`
  background-color: rgb(255, 255, 255);
`;

export const Heading = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 28px;
  line-height: 40px;
  letter-spacing: 0.5px;
  color: rgb(17, 17, 17);
  margin-top: 24px;
  margin-bottom: 16px;
  text-align: center;
  margin-horizontal: 40px;
  paragraph-spacing: 26px;
`;

export const ImageContainer = styled.View`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 60px 24px 60px;
`;

export const Subheading = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 18px;
  line-height: 32px;
  letter-spacing: 0.2px;
  color: rgb(17, 17, 17);
  text-align: center;
  margin-horizontal: 40px;
  paragraph-spacing: 26px;
`;

export const ActionButtonContainer = styled.View`
  margin-horizontal: 24px;
  margin-top: auto;
  margin-bottom: 16px;
`;

export const ActionButtonInfoText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 18px;
  line-height: 32px;
  letter-spacing: 0.2px;
  color: rgb(17, 17, 17);
  text-align: center;
  margin-horizontal: 24px;
  margin-top: 24px;
  paragraph-spacing: 26px;
`;

export const ButtonContainer = styled.View`
  margin-top: 15px;
`;
export const CommonButtonWrapper = styled.View`
  margin-top: 24px;
`;
