import hero from '@assets/images/png/hero/hero.png';
import { CloseIcon } from '@bp/ui-components/mobile';
import { Button, ButtonAction, Header } from '@bp/ui-components/mobile/core';
import { useConnectivity } from '@providers/ConnectivityProvider';
import { navigate } from '@utils/navigation';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';

import * as S from './SuccessfullyVerified.styles';

const SuccessfullyVerified = () => {
  const { t } = useTranslation();
  const { isInternetReachable } = useConnectivity();
  const ctaButtonText = t('uber.successfullyVerifiedScreen.ctaButton');

  const onPress = () => {
    navigate('Tabs', { screen: 'Map' }, true);
  };

  return (
    <S.Container testID="successfully-verified-screen-container">
      <S.SafeAreaView>
        <Header
          title={t('uber.successfullyVerifiedScreen.title')}
          hasRightButton
          RightComponent={
            <TouchableOpacity onPress={onPress} accessibilityLabel="exit">
              <CloseIcon color="black" />
            </TouchableOpacity>
          }
        />
        <S.PulseUberImage
          source={hero}
          testID="successfully-verified-screen-image"
        />
        <S.HeaderContainer>
          <S.Header testID="successfully-verified-screen-header">
            {t('uber.successfullyVerifiedScreen.header')}
          </S.Header>
        </S.HeaderContainer>
        <S.DescriptionContainer>
          <S.Description testID="successfully-verified-screen-description">
            {t('uber.successfullyVerifiedScreen.description')}
          </S.Description>
        </S.DescriptionContainer>
        <S.ButtonSection>
          <Button
            onPress={onPress}
            accessibilityLabel={ctaButtonText}
            accessibilityHint={ctaButtonText}
            testID="successfully-verified-screen-cta-button"
            disabled={!isInternetReachable}
            type={ButtonAction.PRIMARY}>
            {ctaButtonText}
          </Button>
        </S.ButtonSection>
      </S.SafeAreaView>
    </S.Container>
  );
};

export default SuccessfullyVerified;
