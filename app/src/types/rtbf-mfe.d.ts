// Type declaration to extend RTBFAnalyticsEvent
import React from 'react';

declare module '@bp/rtbf-mfe' {
  export enum RTBFAnalyticsEvent {
    // Original properties exist in the package
    // Adding missing properties
    REASON_SCREEN_BACK_CLICK = 'RTBFAnalyticsEvent.REASON_SCREEN_BACK_CLICK',
    REASON_SCREEN_KEEP_CLICK = 'RTBFAnalyticsEvent.REASON_SCREEN_KEEP_CLICK',
    REASON_SCREEN_CONFIRM_CLICK = 'RTBFAnalyticsEvent.REASON_SCREEN_CONFIRM_CLICK',
    GUIDANCE_SCREEN_OPEN = 'RTBFAnalyticsEvent.G<PERSON>DANCE_SCREEN_OPEN',
    GUIDANCE_SCREEN_KEEP_ACCOUNT_CLICK = 'RTBFAnalyticsEvent.GUIDANCE_SCREEN_KEEP_ACCOUNT_CLICK',
    GUIDANC<PERSON>_SCREEN_CANCEL_SUBS_CLICK = 'RTBFAnalyticsEvent.GUIDANC<PERSON>_SCREEN_CANCEL_SUBS_CLICK',
    GUIDANC<PERSON>_SCREEN_NEXT_CLICK = 'RTBFAnalyticsEvent.GUIDANCE_SCREEN_NEXT_CLICK',
    SELECT_OPTION_SELECT_CLICK = 'RTBFAnalyticsEvent.SELECT_OPTION_SELECT_CLICK',
    SELECT_OPTION_PREVIOUS_CLICK = 'RTBFAnalyticsEvent.SELECT_OPTION_PREVIOUS_CLICK',
    CONFIRMATION_CONFIRM_CLICK = 'RTBFAnalyticsEvent.CONFIRMATION_CONFIRM_CLICK',
    CONFIRMATION_PREVIOUS_CLICK = 'RTBFAnalyticsEvent.CONFIRMATION_PREVIOUS_CLICK',
    REASON_SCREEN_KEEP_ACCOUNT_CLICK = 'RTBFAnalyticsEvent.REASON_SCREEN_KEEP_ACCOUNT_CLICK',
    REASON_SCREEN_CANCEL_SUBS_CLICK = 'RTBFAnalyticsEvent.REASON_SCREEN_CANCEL_SUBS_CLICK',
    REASON_SCREEN_MANAGE_MARKETING_CLICK = 'RTBFAnalyticsEvent.REASON_SCREEN_MANAGE_MARKETING_CLICK',
    REQUEST_SENT_OPEN = 'RTBFAnalyticsEvent.REQUEST_SENT_OPEN',
    REQUEST_FAILED_OPEN = 'RTBFAnalyticsEvent.REQUEST_FAILED_OPEN',
    GET_IN_TOUCH_CLICK = 'RTBFAnalyticsEvent.GET_IN_TOUCH_CLICK',
    REFUND_EMAIL_SENT = 'RTBFAnalyticsEvent.REFUND_EMAIL_SENT',
    DELETE_ACCOUNT_SCREEN_OPEN = 'RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_OPEN',
    DELETE_ACCOUNT_SCREEN_BACK = 'RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_BACK',
    DELETE_ACCOUNT_SCREEN_KEEP_CLICK = 'RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_KEEP_CLICK',
    DELETE_ACCOUNT_SCREEN_DELETE_CLICK = 'RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_DELETE_CLICK',
    DELETE_ACCOUNT_SCREEN_SETTINGS_CLICK = 'RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_SETTINGS_CLICK',
    CONFIRM_DELETION_SCREEN_BACK_CLICK = 'RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_BACK_CLICK',
    CONFIRM_DELETION_SCREEN_KEEP_CLICK = 'RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_KEEP_CLICK',
    CONFIRM_DELETION_CANCEL_SUBS_CLICK = 'RTBFAnalyticsEvent.CONFIRM_DELETION_CANCEL_SUBS_CLICK',
    CONFIRM_DELETION_SCREEN_CONFIRM_CLICK = 'RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_CONFIRM_CLICK',
    REQUEST_SENT_DONE = 'RTBFAnalyticsEvent.REQUEST_SENT_DONE',
    REQUEST_FAILED_DONE = 'RTBFAnalyticsEvent.REQUEST_FAILED_DONE',
    REQUEST_RECEIVED_OPEN = 'RTBFAnalyticsEvent.REQUEST_RECEIVED_OPEN',
  }

  // Also add the type that was missing
  export type RTBFAnalyticsEventType = {
    type: RTBFAnalyticsEvent;
    payload?: any;
  };

  // Add the RTBFProviders and RTBFScreens types that were missing
  export const RTBFProviders: React.FC<IRTBFProviders>;
  export const RTBFScreens: React.FC;
}
