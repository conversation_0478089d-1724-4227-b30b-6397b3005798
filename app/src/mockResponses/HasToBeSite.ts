import {
  AvailabilityState,
  ConnectorType,
  FuelType,
  Provider,
  Service,
  Site,
} from '@bp/map-mfe';

const hasToBeSiteMock: Site = {
  chargepoints: [
    {
      evPrice: [],
      location: {
        lat: 0,
        lon: 0,
      },
      costOfCharge: [],
      hasConnetorsAvailable: true,
      apolloExternalId: 'DE*BPE*E0FE51*01',
      apolloInternalId: 'hasToBe-ac4c53e8-2348-4026-9fb3-7d75222cbfb0',
      schemes: [{ schemeId: 1567, schemeName: 'Uber' }],
      connectors: [
        {
          connectorExternalId: 'DE*BPE*E0FE51*01*01',
          connectorInternalId: 'e8d423f7-064a-4d3d-85ee-92790ca64f09',
          availability: {
            lastUpdate: null,
            state: AvailabilityState.Unknown,
          },
          rating: 200,
          type: ConnectorType.CCS2,
        },
        {
          connectorExternalId: 'DE*BPE*E0FE51*01*02',
          connectorInternalId: 'e8d423f7-064a-4d3d-85ee-92790ca64f10',
          availability: {
            lastUpdate: null,
            state: AvailabilityState.Unknown,
          },
          rating: 350,
          type: ConnectorType.CCS2,
        },
      ],
      free: false,
      private: false,
      provider: Provider.hasToBe,
      providerExternalId: 'DE*BPE*E0FE51*01',
      providerInternalId: 'ac4c53e8-2348-4026-9fb3-7d75222cbfb0',
    },
    {
      evPrice: [],
      location: {
        lat: 0,
        lon: 0,
      },
      costOfCharge: [],
      apolloExternalId: 'DE*BPE*E0FE51*02',
      apolloInternalId: 'hasToBe-44d66b4a-5d7c-436d-8424-e72c3f83e810',
      connectors: [
        {
          connectorExternalId: 'DE*BPE*E0FE51*02*02',
          connectorInternalId: 'bd9ca887-97f5-417f-86e0-e4c2db654590',
          availability: {
            lastUpdate: null,
            state: AvailabilityState.Unknown,
          },
          rating: 350,
          type: ConnectorType.CCS2,
        },
      ],
      free: false,
      private: false,
      schemes: [],
      provider: Provider.hasToBe,
      providerExternalId: 'DE*BPE*E0FE51*02',
      providerInternalId: '44d66b4a-5d7c-436d-8424-e72c3f83e810',
    },
  ],
  fuelPrices: [
    {
      fuel: FuelType.SUPER_E5,
      currency: 'EUROCENT',
      lastUpdated: '2022-05-31 11:21:01',
      price: 216.9,
      unit: 'L',
    },
    {
      fuel: FuelType.SUPER_E10,
      currency: 'EUROCENT',
      lastUpdated: '2022-05-31 11:21:02',
      price: 210.9,
      unit: 'L',
    },
    {
      fuel: FuelType.ULTIMATE_102,
      currency: 'EUROCENT',
      lastUpdated: '2022-05-31 11:21:03',
      price: 236.9,
      unit: 'L',
    },
    {
      fuel: FuelType.DIESEL,
      currency: 'EUROCENT',
      lastUpdated: '2022-05-31 11:21:00',
      price: 205.9,
      unit: 'L',
    },
    {
      fuel: FuelType.ULTIMATE_DIESEL,
      currency: 'EUROCENT',
      lastUpdated: '2022-05-31 11:21:00',
      price: 226.9,
      unit: 'L',
    },
    {
      fuel: FuelType.LPG,
      currency: 'EUROCENT',
      lastUpdated: '2022-05-25 10:25:00',
      price: 111.9,
      unit: 'L',
    },
  ],
  fuels: [
    FuelType.LPG,
    FuelType.SUPER_E5,
    FuelType.DIESEL,
    FuelType.ULTIMATE_102,
    FuelType.ULTIMATE_DIESEL,
    FuelType.SUPER_E10,
  ],
  hasEvCharging: true,
  hasFuel: true,
  isAds: false,
  provider: Provider.aral,
  services: [
    Service.TWENTY_FOUR_HOURS,
    Service.WASH,
    Service.SUPER_WASH,
    Service.PETIT_BISTRO,
  ],
  siteDetails: {
    address: 'Holzmarktstraße 12/14',
    city: 'Berlin',
    country: 'DE',
    hours: {
      mon1: '0000',
      mon2: '2359',
      tue1: '0000',
      tue2: '2359',
      wed1: '0000',
      wed2: '2359',
      thu1: '0000',
      thu2: '2359',
      fri1: '0000',
      fri2: '2359',
      sat1: '0000',
      sat2: '2359',
      sun1: '0000',
      sun2: '2359',
    },
    location: { lat: 52.514151, lon: 13.421487 },
    phone: '03024720748',
    postcode: '10179',
  },
  evAvailability: {
    availableChargepointCount: 0,
    availableConnectorCount: 0,
  },
  siteId: 'aral-27100000',
};

export default hasToBeSiteMock;
