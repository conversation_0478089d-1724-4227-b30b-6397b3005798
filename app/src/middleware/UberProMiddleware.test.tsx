import { useAppSettings } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { SupportedCountries } from '@common/enums';
import { PulseAppConfig } from '@config/config.types';
import { PartnerDriverType } from '@graphql/mutations/removePartnerDriverStatus';
import * as Config from '@providers/ConfigProvider';
import { navigate } from '@utils/navigation';
import { render, waitFor } from '@utils/test-utils';
import React from 'react';
import { Linking } from 'react-native';

import { UberProMiddleware } from './UberProMiddleware';

// Mock the navigate function
jest.mock('@utils/navigation', () => ({
  navigate: jest.fn(),
}));

jest.mock('@analytics', () => ({
  analyticsEvent: jest.fn(),
}));

jest.mock('@bp/profile-mfe');

jest.spyOn(Config, 'useConfig').mockReturnValue({
  fetched: true,
  external_links: {},
} as unknown as PulseAppConfig);

const mockedAppSettings = jest.mocked(useAppSettings);

describe('<UberProMiddleware />', () => {
  describe('Welcome Modal', () => {
    it('should render UberProWelcomeModal when user has Uber Pro partnership and home country is NL', () => {
      mockedAppSettings.mockReturnValue({
        userInfo: {
          partnerType: PartnerDriverType.Uber,
          userCountry: SupportedCountries.NL,
        },
      } as any);

      const { getByTestId } = render(<UberProMiddleware />);

      const modal = getByTestId('UberProWelcomeModal.HeroModal');
      expect(modal).not.toBeNull();
    });

    it('should not render UberProWelcomeModal when user home country is different than NL', () => {
      mockedAppSettings.mockReturnValue({
        userInfo: {
          partnerType: PartnerDriverType.Uber,
          userCountry: SupportedCountries.UK,
        },
      } as any);

      const { queryByTestId } = render(<UberProMiddleware />);

      const modal = queryByTestId('UberProWelcomeModal.HeroModal');
      expect(modal).toBeNull();
    });

    it('should not render UberProWelcomeModal when user is not Uber Pro partner', () => {
      mockedAppSettings.mockReturnValue({
        userInfo: {
          userCountry: SupportedCountries.NL,
        },
      } as any);

      const { queryByTestId } = render(<UberProMiddleware />);

      const modal = queryByTestId('UberProWelcomeModal.HeroModal');
      expect(modal).toBeNull();
    });
  });

  describe('Expired Token', () => {
    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should navigate to UberProLinkExpired screen when access token is expired', async () => {
      mockedAppSettings.mockReturnValue({
        userInfo: {
          partnerType: PartnerDriverType.Uber,
          userCountry: SupportedCountries.UK,
          tokens: [{ tokenType: 'Uber', status: 'INACTIVE' }],
        },
      } as any);

      render(<UberProMiddleware />);

      await waitFor(() => {
        expect(navigate).toHaveBeenCalledWith('UberProLinkExpired');
      });
    });

    it('should refetch the userInfo token information when user authenticates ', async () => {
      const mockRefetchUserInfo = jest.fn();
      (Linking.getInitialURL as jest.Mock).mockResolvedValue(
        'bppulse://bppulse/uberAuth/success',
      );

      (useAuth as jest.Mock).mockImplementation(async () => ({
        authenticated: true,
        consents: [],
      }));

      mockedAppSettings.mockReturnValue({
        userInfo: {
          partnerType: PartnerDriverType.Uber,
          userCountry: SupportedCountries.UK,
          tokens: [{ tokenType: 'Uber', status: 'INACTIVE' }],
        },
        refetchUserInfo: mockRefetchUserInfo,
      } as any);

      render(<UberProMiddleware />);

      await waitFor(() => {
        expect(mockRefetchUserInfo).toHaveBeenCalled();
      });
    });
  });
  describe('Ineligible users', () => {
    it('should navigate to IneligibleAccount screen when access token is invalid', async () => {
      mockedAppSettings.mockReturnValue({
        userInfo: {
          partnerType: PartnerDriverType.Uber,
          userCountry: SupportedCountries.UK,
          tokens: [{ tokenType: 'Uber', status: 'INVALID' }],
        },
      } as any);

      render(<UberProMiddleware />);

      await waitFor(() => {
        expect(navigate).toHaveBeenCalledWith('IneligibleAccount');
      });
    });
  });
});
