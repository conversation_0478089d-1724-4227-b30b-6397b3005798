import { PulseAppConfig } from '@config/config.types';
import * as Config from '@providers/ConfigProvider';
import { waitFor } from '@testing-library/react-native';
import * as Navigation from '@utils/navigation';
import { render } from '@utils/test-utils';
import React from 'react';
import { Linking } from 'react-native';

import { OffersMiddleware } from './OffersMiddleware';

jest.mock('react-native-url-polyfill', () => ({
  URL: class {
    hostname = 'offers';
    searchParams = {
      get: jest.fn().mockReturnValue('123'),
    };
  },
}));

const getInitialURLSpy = jest
  .spyOn(Linking, 'getInitialURL')
  .mockResolvedValue('chargemaster://offers?offerCode=123');

const navigateSpy = jest
  .spyOn(Navigation, 'navigate')
  .mockImplementation(jest.fn());

jest
  .spyOn(Linking, 'addEventListener')
  .mockReturnValue({ remove: jest.fn() } as any);

jest.spyOn(Config, 'useConfig').mockReturnValue({
  profile_mfe: {
    offers: true,
  },
} as unknown as PulseAppConfig);

describe('<OffersMiddleware />', () => {
  it('should handle deeplinking to offers mfe', async () => {
    render(<OffersMiddleware />);
    expect(getInitialURLSpy).toHaveBeenCalled();

    await waitFor(() => {
      expect(navigateSpy).toHaveBeenCalledWith('Offers', {
        screen: 'OffersMfe.AddNewOfferScreen',
        params: {
          offerCode: '123',
        },
      });
    });
  });
});
