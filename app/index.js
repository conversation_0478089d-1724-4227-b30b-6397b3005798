import { AppRegistry, Platform, Text, TextInput } from 'react-native';

if (Platform.OS === 'ios') {
  require('@formatjs/intl-locale/polyfill').default;

  require('@formatjs/intl-numberformat/polyfill').default;
  require('@formatjs/intl-numberformat/locale-data/en').default;
  require('@formatjs/intl-numberformat/locale-data/de').default;
  require('@formatjs/intl-numberformat/locale-data/nl').default;
  require('@formatjs/intl-numberformat/locale-data/es').default;
}

import 'react-native-gesture-handler';

import { name as appName } from './app.json';
import App from './src/App';

if (!__DEV__) {
  console.log = () => null;
  console.info = () => null;
  console.debug = () => null;
  console.warn = () => null;
  console.error = () => null;
}

Text.defaultProps = {
  ...(Text.defaultProps || {}),
  maxFontSizeMultiplier: 2,
};

TextInput.defaultProps = {
  ...(TextInput.defaultProps || {}),
  maxFontSizeMultiplier: 2,
};

AppRegistry.registerComponent(appName, () => App);
