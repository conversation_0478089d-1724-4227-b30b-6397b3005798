import { CodegenConfig } from '@graphql-codegen/cli';

const ANONYMOUS_API_URL = String(process.env.ANONYMOUS_API_URL);
const ANONYMOUS_API_KEY = String(process.env.ANONYMOUS_API_KEY);

const GATEWAY_API_KEY = String(process.env.GATEWAY_API_KEY);
const GATEWAY_PRIVATE_URL = String(process.env.GATEWAY_PRIVATE_URL);

const config = async (): Promise<CodegenConfig> => {
  const response = await fetch(ANONYMOUS_API_URL, {
    headers: {
      'x-api-key': ANONYMOUS_API_KEY,
    },
  });

  const { data } = await response.json();

  return {
    schema: {
      [GATEWAY_PRIVATE_URL]: {
        headers: {
          authorization: data,
          'x-api-key': GATEWAY_API_KEY,
        },
      },
    },
    documents: ['src/graphql/queries/*.ts', 'src/graphql/mutations/*.ts'],
    config: {
      namingConvention: {
        enumValues: 'keep',
      },
    },
    generates: {
      'src/types/graphql/': {
        preset: 'client',
      },
    },
  };
};

export default config();
