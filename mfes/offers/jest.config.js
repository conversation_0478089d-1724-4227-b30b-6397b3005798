const transformIgnorePackages = [
  '@bp/*',
  '@react-native/*',
  '@react-navigation/*',
  'react-native-*',
  'react-native',
  'react-navigation',
];

const transformIgnorePatterns = `node_modules/(?!${transformIgnorePackages.join(
  '|',
)})`;

// all tests which are date dependent will use the GMT time zone
process.env.TZ = 'GMT';

module.exports = {
  preset: 'react-native',
  testEnvironment: 'node',
  fakeTimers: {
    enableGlobally: true,
  },
  transformIgnorePatterns: [transformIgnorePatterns],
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: 'test-results/jest' }],
  ],
  moduleNameMapper: {
    '\\.svg': '<rootDir>/.jest/modules/svg.js',
    '^react$': '<rootDir>/../../node_modules/react',
    '^react-native$': '<rootDir>/../../node_modules/react-native',
  },
  coverageReporters: [
    'json',
    'lcov',
    'text',
    'clover',
    'text-summary',
    'cobertura',
  ],
  collectCoverageFrom: ['<rootDir>/src/**/*.{ts,tsx}'],
  rootDir: '.',
  roots: ['<rootDir>/src'],
  setupFiles: ['./jest.setUp.js'],
  modulePaths: ['<rootDir>/../../node_modules', '<rootDir>/node_modules'],
  testTimeout: 20000,
};
