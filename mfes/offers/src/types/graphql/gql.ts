/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
    "\n  mutation applyOffer(\n    $offerCode: String!\n    $userId: String\n    $updateWalletSubs: Boolean\n  ) {\n    applyOffer(\n      offerCode: $offerCode\n      userId: $userId\n      updateWalletSubs: $updateWalletSubs\n    ) {\n      error\n      offer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        userId\n        status\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n      }\n    }\n  }\n": types.ApplyOfferDocument,
    "\n  query getOffersByUser(\n    $userId: String!\n    $status: [OfferStatus!]\n    $offerType: [OfferType!]\n    $sortBy: UserOffersOrder\n    $pageSize: Int\n    $lastEvaluatedKey: String\n  ) {\n    getOffersByUser(\n      userId: $userId\n      status: $status\n      offerType: $offerType\n      sortBy: $sortBy\n      pageSize: $pageSize\n      lastEvaluatedKey: $lastEvaluatedKey\n    ) {\n      offers {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        creditStatus\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        subsStatus\n        userId\n        status\n        queueStatus\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n        monthsRemaining\n      }\n      lastEvaluatedKey\n      maxOfferQuantity\n      maxOfferQuantityExceeded\n      error\n      trialOffer {\n        trialStatus\n        trialBillingDate\n        planDuration\n        trialRedemptionDate\n      }\n    }\n  }\n": types.GetOffersByUserDocument,
    "\n  query getWalletSubscription($userId: String) {\n    getWalletSubscription(userId: $userId) {\n      id\n      statusReason\n      cancelledOn\n    }\n  }\n": types.GetWalletSubscriptionDocument,
    "\n  query validateOffer($offerCode: String!, $userId: String) {\n    validateOffer(offerCode: $offerCode, userId: $userId) {\n      isValid\n      reason\n      error\n      offer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        creditStatus\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        subsStatus\n        userId\n        status\n        queueStatus\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n        monthsRemaining\n      }\n      contradictoryOffer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        userId\n        status\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n      }\n    }\n  }\n": types.ValidateOfferDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation applyOffer(\n    $offerCode: String!\n    $userId: String\n    $updateWalletSubs: Boolean\n  ) {\n    applyOffer(\n      offerCode: $offerCode\n      userId: $userId\n      updateWalletSubs: $updateWalletSubs\n    ) {\n      error\n      offer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        userId\n        status\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation applyOffer(\n    $offerCode: String!\n    $userId: String\n    $updateWalletSubs: Boolean\n  ) {\n    applyOffer(\n      offerCode: $offerCode\n      userId: $userId\n      updateWalletSubs: $updateWalletSubs\n    ) {\n      error\n      offer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        userId\n        status\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getOffersByUser(\n    $userId: String!\n    $status: [OfferStatus!]\n    $offerType: [OfferType!]\n    $sortBy: UserOffersOrder\n    $pageSize: Int\n    $lastEvaluatedKey: String\n  ) {\n    getOffersByUser(\n      userId: $userId\n      status: $status\n      offerType: $offerType\n      sortBy: $sortBy\n      pageSize: $pageSize\n      lastEvaluatedKey: $lastEvaluatedKey\n    ) {\n      offers {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        creditStatus\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        subsStatus\n        userId\n        status\n        queueStatus\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n        monthsRemaining\n      }\n      lastEvaluatedKey\n      maxOfferQuantity\n      maxOfferQuantityExceeded\n      error\n      trialOffer {\n        trialStatus\n        trialBillingDate\n        planDuration\n        trialRedemptionDate\n      }\n    }\n  }\n"): (typeof documents)["\n  query getOffersByUser(\n    $userId: String!\n    $status: [OfferStatus!]\n    $offerType: [OfferType!]\n    $sortBy: UserOffersOrder\n    $pageSize: Int\n    $lastEvaluatedKey: String\n  ) {\n    getOffersByUser(\n      userId: $userId\n      status: $status\n      offerType: $offerType\n      sortBy: $sortBy\n      pageSize: $pageSize\n      lastEvaluatedKey: $lastEvaluatedKey\n    ) {\n      offers {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        creditStatus\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        subsStatus\n        userId\n        status\n        queueStatus\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n        monthsRemaining\n      }\n      lastEvaluatedKey\n      maxOfferQuantity\n      maxOfferQuantityExceeded\n      error\n      trialOffer {\n        trialStatus\n        trialBillingDate\n        planDuration\n        trialRedemptionDate\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getWalletSubscription($userId: String) {\n    getWalletSubscription(userId: $userId) {\n      id\n      statusReason\n      cancelledOn\n    }\n  }\n"): (typeof documents)["\n  query getWalletSubscription($userId: String) {\n    getWalletSubscription(userId: $userId) {\n      id\n      statusReason\n      cancelledOn\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query validateOffer($offerCode: String!, $userId: String) {\n    validateOffer(offerCode: $offerCode, userId: $userId) {\n      isValid\n      reason\n      error\n      offer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        creditStatus\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        subsStatus\n        userId\n        status\n        queueStatus\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n        monthsRemaining\n      }\n      contradictoryOffer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        userId\n        status\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n      }\n    }\n  }\n"): (typeof documents)["\n  query validateOffer($offerCode: String!, $userId: String) {\n    validateOffer(offerCode: $offerCode, userId: $userId) {\n      isValid\n      reason\n      error\n      offer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        creditStatus\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        subsStatus\n        userId\n        status\n        queueStatus\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n        monthsRemaining\n      }\n      contradictoryOffer {\n        offerCode\n        offerName\n        offerPublicDescription\n        offerNotes\n        offerType\n        offerCountry\n        creditAmount\n        creditBalance\n        currency\n        partnerName\n        subsDiscount\n        subsDuration\n        userId\n        status\n\n        #computed\n        redemptionExpiryDate\n        redemptionDate\n        expiryDate\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;