/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format. */
  DateTimeISO: { input: any; output: any; }
  /** Custom scalar type to handle either string or number */
  StringOrNumber: { input: any; output: any; }
};

export type AdHocLastEvaluatedKey = AdHocLastEvaluatedKeyAvailable | AdHocLastEvaluatedKeyToken;

/** Ad hoc last evaluated key for available secondary index */
export type AdHocLastEvaluatedKeyAvailable = {
  __typename?: 'AdHocLastEvaluatedKeyAvailable';
  available: Scalars['String']['output'];
  token_id: Scalars['String']['output'];
};

/**
 * it can be in the following combinations:
 *     1. token_id,
 *     2. token_id and available
 *     3. Everything is undefined
 */
export type AdHocLastEvaluatedKeyInput = {
  available?: InputMaybe<Scalars['String']['input']>;
  token_id?: InputMaybe<Scalars['String']['input']>;
};

/** Ad hoc last evaluated key for primary index */
export type AdHocLastEvaluatedKeyToken = {
  __typename?: 'AdHocLastEvaluatedKeyToken';
  token_id: Scalars['String']['output'];
};

/** ad hoc token */
export type AdHocToken = {
  __typename?: 'AdHocToken';
  available: Scalars['Boolean']['output'];
  lastUsed: Scalars['DateTimeISO']['output'];
  tokenUid: Scalars['String']['output'];
  usageCount: Scalars['Int']['output'];
};

/** Array for ad hoc tokens */
export type AdHocTokenResponse = {
  __typename?: 'AdHocTokenResponse';
  count: Scalars['Int']['output'];
  lastEvaluatedKey?: Maybe<AdHocLastEvaluatedKey>;
  tokens: Array<AdHocToken>;
};

export type AddRfid = {
  cardNumber: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  country: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type AddRfidResponse = {
  __typename?: 'AddRfidResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type AddRoamingResponse = {
  __typename?: 'AddRoamingResponse';
  dcsContractId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type AddressDetails = {
  addressCity?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLine?: InputMaybe<Scalars['String']['input']>;
  addressPostcode?: InputMaybe<Scalars['String']['input']>;
};

export type AddressInput = {
  addressLine1?: InputMaybe<Scalars['String']['input']>;
  addressLine2?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  postcode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
};

export type AllSubscriptionsInput = {
  endDate: Scalars['String']['input'];
  startDate: Scalars['String']['input'];
};

export type AllUpdatedTagResponse = {
  __typename?: 'AllUpdatedTagResponse';
  country?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
  tagBarredDatetime?: Maybe<Scalars['String']['output']>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCategoryName?: Maybe<Scalars['String']['output']>;
  tagDeletedFlag?: Maybe<Scalars['String']['output']>;
  tagExpiredDatetime?: Maybe<Scalars['String']['output']>;
  tagLastUsedDatetime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagSerialNumber?: Maybe<Scalars['String']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  tagTypeName?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  userStatus?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<Scalars['String']['output']>;
};

export type ApplySubscription = {
  __typename?: 'ApplySubscription';
  subscription_code?: Maybe<Scalars['String']['output']>;
};

export enum AssetType {
  /** @deprecated No longer supported */
  CP = 'CP',
  SITE = 'SITE'
}

export type AuthLookup = {
  __typename?: 'AuthLookup';
  acsTransactionId?: Maybe<Scalars['String']['output']>;
  acsUrl?: Maybe<Scalars['String']['output']>;
  authenticationType?: Maybe<Scalars['String']['output']>;
  cardBrand?: Maybe<Scalars['String']['output']>;
  cavv?: Maybe<Scalars['String']['output']>;
  challengeRequired?: Maybe<Scalars['String']['output']>;
  dsTransactionId?: Maybe<Scalars['String']['output']>;
  eciFlag?: Maybe<Scalars['String']['output']>;
  enrolled?: Maybe<Scalars['String']['output']>;
  errorDescription?: Maybe<Scalars['String']['output']>;
  errorNumber?: Maybe<Scalars['String']['output']>;
  orderId?: Maybe<Scalars['String']['output']>;
  paresStatus?: Maybe<Scalars['String']['output']>;
  payload?: Maybe<Scalars['String']['output']>;
  sdkFlowType?: Maybe<Scalars['String']['output']>;
  signatureVerification?: Maybe<Scalars['String']['output']>;
  threeDSServerTransactionId?: Maybe<Scalars['String']['output']>;
  threeDSVersion?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
};

export type Authenticate = {
  __typename?: 'Authenticate';
  acsTransactionId?: Maybe<Scalars['String']['output']>;
  authenticationType?: Maybe<Scalars['String']['output']>;
  cardBin?: Maybe<Scalars['String']['output']>;
  cardBrand?: Maybe<Scalars['String']['output']>;
  cavv?: Maybe<Scalars['String']['output']>;
  dsTransactionId?: Maybe<Scalars['String']['output']>;
  eciFlag?: Maybe<Scalars['String']['output']>;
  errorDescription?: Maybe<Scalars['String']['output']>;
  errorNumber?: Maybe<Scalars['String']['output']>;
  interactionCounter?: Maybe<Scalars['String']['output']>;
  paresStatus?: Maybe<Scalars['String']['output']>;
  signatureVerification?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  threeDSServerTransactionId?: Maybe<Scalars['String']['output']>;
  threeDSVersion?: Maybe<Scalars['String']['output']>;
};

export enum AvailabilityState {
  Available = 'Available',
  Charging = 'Charging',
  Occupied = 'Occupied',
  Reserved = 'Reserved',
  Unavailable = 'Unavailable',
  Unknown = 'Unknown'
}

export type BillingAmount = {
  __typename?: 'BillingAmount';
  amount?: Maybe<Scalars['Float']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
};

export enum BillingCycle {
  MONTHLY = 'MONTHLY'
}

export type BlockRfid = {
  cardNumber: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  country: Scalars['String']['input'];
  reasonForBlocking: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type BlockRfidResponse = {
  __typename?: 'BlockRfidResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type BlockingFee = {
  __typename?: 'BlockingFee';
  duration: Scalars['Float']['output'];
  price: Scalars['Float']['output'];
  unit: BlockingFeeUnit;
};

export enum BlockingFeeUnit {
  HOUR = 'HOUR',
  MIN = 'MIN'
}

export enum BrandId {
  BP_PULSE = 'BP_PULSE',
  /** @deprecated No longer supported */
  PolarPlus = 'PolarPlus'
}

export type CancelSubscriptionResponse = {
  __typename?: 'CancelSubscriptionResponse';
  cancelledOn?: Maybe<Scalars['String']['output']>;
  endOfCurrentBillingCycle?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  membershipStatus?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
};

export type Card = {
  __typename?: 'Card';
  brand?: Maybe<Scalars['String']['output']>;
  exp_month?: Maybe<Scalars['Int']['output']>;
  exp_year?: Maybe<Scalars['Int']['output']>;
  last4?: Maybe<Scalars['String']['output']>;
};

export type CardDetails = {
  __typename?: 'CardDetails';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
  fundingMethod?: Maybe<Scalars['String']['output']>;
};

export type CardDetailsSubscripton = {
  __typename?: 'CardDetailsSubscripton';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
};

export type ChargeEvent = {
  __typename?: 'ChargeEvent';
  chargeMonitoringPayload?: Maybe<ChargeMonitoringEventPayload>;
  chargePayload?: Maybe<ChargeEventPayload>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type ChargeEventPayload = {
  __typename?: 'ChargeEventPayload';
  apolloInternalId?: Maybe<Scalars['String']['output']>;
  connectorInternalId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
};

export type ChargeMonitoringEventPayload = {
  __typename?: 'ChargeMonitoringEventPayload';
  currentPrice?: Maybe<ChargePropertyAttributes>;
  energyConsumed?: Maybe<ChargePropertyAttributes>;
  power?: Maybe<ChargePropertyAttributes>;
  stateOfCharge?: Maybe<ChargePropertyAttributes>;
  transactionId?: Maybe<Scalars['String']['output']>;
};

export type ChargeMonitoringPayloadData = {
  __typename?: 'ChargeMonitoringPayloadData';
  currentPrice?: Maybe<ChargePropertyAttributes>;
  energyActiveImportInterval?: Maybe<ChargePropertyAttributes>;
  powerActiveImport?: Maybe<ChargePropertyAttributes>;
  soc?: Maybe<ChargePropertyAttributes>;
};

export type ChargePropertyAttributes = {
  __typename?: 'ChargePropertyAttributes';
  eventTime?: Maybe<Scalars['String']['output']>;
  units?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export enum ChargeStatus {
  started = 'started'
}

export type Chargepoint = {
  __typename?: 'Chargepoint';
  apolloExternalId: Scalars['String']['output'];
  apolloInternalId: Scalars['String']['output'];
  availability?: Maybe<ChargepointAvailability>;
  chargepointNumber?: Maybe<Scalars['Int']['output']>;
  connectorFormat: ConnectorFormat;
  connectors: Array<Connector>;
  costOfCharge: Array<CostOfCharge>;
  evPrice?: Maybe<Array<ConnectorPrice>>;
  free: Scalars['Boolean']['output'];
  hasConnectorsAvailable?: Maybe<Scalars['Boolean']['output']>;
  /** @deprecated No longer supported */
  hasConnetorsAvailable?: Maybe<Scalars['Boolean']['output']>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  hours: Hours;
  lastCharge?: Maybe<Scalars['String']['output']>;
  lastLogin?: Maybe<Scalars['String']['output']>;
  lastUpdated: Scalars['String']['output'];
  lastUsed?: Maybe<Scalars['String']['output']>;
  location: GeoPoint;
  overstayFee?: Maybe<Scalars['Float']['output']>;
  owningProvider: Provider;
  private: Scalars['Boolean']['output'];
  provider: Provider;
  providerExternalId: Scalars['String']['output'];
  providerInternalId: Scalars['String']['output'];
  publicName?: Maybe<Scalars['String']['output']>;
  schemes: Array<ChargepointScheme>;
  site?: Maybe<Site>;
  siteId: Scalars['String']['output'];
};

export type ChargepointAvailability = {
  __typename?: 'ChargepointAvailability';
  apolloInternalId: Scalars['String']['output'];
  available: AvailabilityState;
  connectors: Array<ConnectorAvailability>;
  lastUsed?: Maybe<Scalars['String']['output']>;
  provider: Provider;
};

export type ChargepointScheme = {
  __typename?: 'ChargepointScheme';
  schemeId: Scalars['Int']['output'];
  schemeName: Scalars['String']['output'];
};

export enum ChargingSpeed {
  FAST = 'FAST',
  RAPID = 'RAPID',
  SLOW = 'SLOW',
  ULTRA_RAPID = 'ULTRA_RAPID',
  VERY_FAST = 'VERY_FAST'
}

export type Complete = {
  __typename?: 'Complete';
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type Connector = {
  __typename?: 'Connector';
  availability?: Maybe<ConnectorAvailability>;
  chargepoint?: Maybe<Chargepoint>;
  connectorExternalId: Scalars['String']['output'];
  connectorInternalId: Scalars['String']['output'];
  connectorNumber?: Maybe<Scalars['Int']['output']>;
  phase?: Maybe<Scalars['String']['output']>;
  provider: Provider;
  rating: Scalars['Float']['output'];
  type: ConnectorType;
};

export type ConnectorAvailability = {
  __typename?: 'ConnectorAvailability';
  connectorExternalId: Scalars['String']['output'];
  connectorInternalId: Scalars['String']['output'];
  lastUpdate?: Maybe<Scalars['String']['output']>;
  state: AvailabilityState;
};

export enum ConnectorFormat {
  CABLE = 'CABLE',
  SOCKET = 'SOCKET',
  UNKNOWN = 'UNKNOWN'
}

export type ConnectorPrice = {
  __typename?: 'ConnectorPrice';
  blockingFee?: Maybe<BlockingFee>;
  connectorExternalId: Scalars['String']['output'];
  connectorInternalId: Scalars['String']['output'];
  currency?: Maybe<Scalars['String']['output']>;
  grossUnitCost?: Maybe<Scalars['Float']['output']>;
  minimumCost?: Maybe<Scalars['Float']['output']>;
  unit: Scalars['String']['output'];
};

export enum ConnectorType {
  CCS1 = 'CCS1',
  CCS2 = 'CCS2',
  CHADEMO = 'CHADEMO',
  COMMANDO = 'COMMANDO',
  DOMESTIC_E = 'DOMESTIC_E',
  DOMESTIC_G = 'DOMESTIC_G',
  DOMESTIC_J = 'DOMESTIC_J',
  SCHUKO = 'SCHUKO',
  TESLA = 'TESLA',
  TYPE_1 = 'TYPE_1',
  TYPE_2 = 'TYPE_2',
  TYPE_3A = 'TYPE_3A',
  TYPE_3C = 'TYPE_3C',
  UNKNOWN = 'UNKNOWN'
}

export type CostOfCharge = {
  __typename?: 'CostOfCharge';
  minimumCharge: CostOfChargeMinimum;
  tariff: CostOfChargeTariff;
};

export type CostOfChargeMinimum = {
  __typename?: 'CostOfChargeMinimum';
  cost: Scalars['String']['output'];
  type: TariffType;
  unit: Currency;
};

export type CostOfChargeTariff = {
  __typename?: 'CostOfChargeTariff';
  cost: Scalars['String']['output'];
  multiplier: Scalars['String']['output'];
  type: TariffType;
  unit: Currency;
};

export enum Country {
  AT = 'AT',
  BE = 'BE',
  BG = 'BG',
  CH = 'CH',
  CZ = 'CZ',
  DE = 'DE',
  DK = 'DK',
  EE = 'EE',
  ES = 'ES',
  FR = 'FR',
  HR = 'HR',
  HU = 'HU',
  IE = 'IE',
  IT = 'IT',
  LT = 'LT',
  LU = 'LU',
  LV = 'LV',
  NL = 'NL',
  NO = 'NO',
  PL = 'PL',
  RO = 'RO',
  SE = 'SE',
  SI = 'SI',
  SK = 'SK',
  UK = 'UK',
  US = 'US'
}

export type CreateSubsPlanInput = {
  billingAmount: Scalars['Float']['input'];
  currency: Scalars['String']['input'];
  default?: InputMaybe<Scalars['Boolean']['input']>;
  description: Scalars['String']['input'];
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName: Scalars['String']['input'];
  planType?: InputMaybe<PlanType>;
  scheme_id?: InputMaybe<Scalars['Int']['input']>;
};

export type CreateTariffInput = {
  country: TariffCountry;
  currency: TariffCurrency;
  elements: Array<TariffElementInput>;
  tariffType: EmspTariffType;
  validFrom: Scalars['String']['input'];
};

export type CreateWalletSubscriptionResponse = {
  __typename?: 'CreateWalletSubscriptionResponse';
  error?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export enum CrossAcceptanceBrand {
  AN = 'AN',
  AP = 'AP',
  AR = 'AR',
  AS = 'AS',
  BP = 'BP',
  CK = 'CK',
  DH = 'DH',
  ES = 'ES',
  OM = 'OM',
  OT = 'OT',
  TO = 'TO',
  WE = 'WE',
  WP = 'WP'
}

export enum Currency {
  EUR = 'EUR',
  GBP = 'GBP',
  USD = 'USD'
}

export type Customer = {
  __typename?: 'Customer';
  data?: Maybe<Scalars['String']['output']>;
};

export type DataResult = {
  __typename?: 'DataResult';
  error?: Maybe<Scalars['String']['output']>;
  items: Array<Site>;
  searchAfter?: Maybe<Scalars['String']['output']>;
  total: Scalars['Int']['output'];
};

export type DeleteAccountRequestInput = {
  appCountry: Scalars['String']['input'];
  balance: Scalars['Float']['input'];
  deletionOption?: InputMaybe<DeletionType>;
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  isDeleteConfirmed: Scalars['Boolean']['input'];
  lastName: Scalars['String']['input'];
  reason: Scalars['String']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
  userType: Scalars['String']['input'];
};

export type DeleteAccountResponse = {
  __typename?: 'DeleteAccountResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type DeletePartnerTokenResponse = {
  __typename?: 'DeletePartnerTokenResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type DeleteRfidRecordResponse = {
  __typename?: 'DeleteRFIDRecordResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type DeleteRfidParamsObject = {
  cardNumber: Scalars['String']['input'];
};

export type DeleteUserChargeEventsResponse = {
  __typename?: 'DeleteUserChargeEventsResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type DeleteUserRespnse = {
  __typename?: 'DeleteUserRespnse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  user?: Maybe<User>;
};

export enum DeletionType {
  ACCOUNTS_AND_DATA = 'ACCOUNTS_AND_DATA',
  ALL_ACCOUNTS = 'ALL_ACCOUNTS',
  PULSE_DATA = 'PULSE_DATA'
}

export type DeprecatedPaymentRfidResponse = {
  __typename?: 'DeprecatedPaymentRFIDResponse';
  data?: Maybe<DeprecatedPaymentRfidResponseData>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type DeprecatedPaymentRfidResponseData = {
  __typename?: 'DeprecatedPaymentRFIDResponseData';
  event_details?: Maybe<Scalars['String']['output']>;
  event_time?: Maybe<Scalars['String']['output']>;
  salesforce_ID?: Maybe<Scalars['String']['output']>;
};

export type DropInSession = {
  __typename?: 'DropInSession';
  dropInType?: Maybe<Scalars['String']['output']>;
  merchant?: Maybe<Scalars['String']['output']>;
  redirectUrl?: Maybe<Scalars['String']['output']>;
  session?: Maybe<Session>;
  sessionType?: Maybe<Scalars['String']['output']>;
};

export type EditOfferInput = {
  expiryDate?: InputMaybe<Scalars['String']['input']>;
  offerCode: Scalars['String']['input'];
  offerName?: InputMaybe<Scalars['String']['input']>;
  offerNotes?: InputMaybe<Scalars['String']['input']>;
  offerPublicDescription?: InputMaybe<Scalars['String']['input']>;
  partnerContribution?: InputMaybe<Scalars['Float']['input']>;
};

export enum EmspTariffType {
  GUEST = 'GUEST',
  PAYG_WALLET = 'PAYG_WALLET',
  SUBS_WALLET = 'SUBS_WALLET',
  UBER = 'UBER'
}

export type Entitlements = {
  __typename?: 'Entitlements';
  chargepointsAvailable?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  originEntitlementID?: Maybe<Scalars['String']['output']>;
  partnerSchemes?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  paymentMethods?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rfidDefaultProviders?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rfidEnabled?: Maybe<Scalars['Boolean']['output']>;
  subsEnabled?: Maybe<Scalars['Boolean']['output']>;
};

export type EvAvailability = {
  __typename?: 'EvAvailability';
  availableChargepointCount?: Maybe<Scalars['Int']['output']>;
  availableConnectorCount?: Maybe<Scalars['Int']['output']>;
};

export type EvDetails = {
  __typename?: 'EvDetails';
  chargepointCount: Scalars['Int']['output'];
  connectorCount: Scalars['Int']['output'];
  hasRoyalMailScheme: Scalars['Boolean']['output'];
  hasUberScheme: Scalars['Boolean']['output'];
  maximumSpeed?: Maybe<Scalars['Float']['output']>;
  schemeIds: Array<Scalars['Int']['output']>;
  schemes: Array<ChargepointScheme>;
};

export type EvPrice = {
  __typename?: 'EvPrice';
  chargepointId: Scalars['String']['output'];
  connectors: Array<ConnectorPrice>;
  error?: Maybe<Scalars['String']['output']>;
  lastUpdated?: Maybe<Scalars['String']['output']>;
};

export type EventResponse = {
  __typename?: 'EventResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type ExistingRfidRecordResponse = {
  __typename?: 'ExistingRFIDRecordResponse';
  data?: Maybe<Array<Maybe<ExistingRfidRecordResponseData>>>;
};

export type ExistingRfidRecordResponseData = {
  __typename?: 'ExistingRFIDRecordResponseData';
  additional_address_1?: Maybe<Scalars['String']['output']>;
  additional_address_2?: Maybe<Scalars['String']['output']>;
  address_city?: Maybe<Scalars['String']['output']>;
  address_country?: Maybe<Scalars['String']['output']>;
  address_line?: Maybe<Scalars['String']['output']>;
  address_postcode?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  date_added?: Maybe<Scalars['String']['output']>;
  first_name?: Maybe<Scalars['String']['output']>;
  last_name?: Maybe<Scalars['String']['output']>;
  partner_type?: Maybe<Scalars['String']['output']>;
  rfid_card_number?: Maybe<Scalars['String']['output']>;
  rfid_status?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type FailedOffer = {
  __typename?: 'FailedOffer';
  error: Scalars['String']['output'];
  offerCode: Scalars['String']['output'];
};

export type Favourite = {
  __typename?: 'Favourite';
  asset_type?: Maybe<Scalars['String']['output']>;
  brand_id?: Maybe<Scalars['String']['output']>;
  favourite_label?: Maybe<Scalars['String']['output']>;
  serial?: Maybe<Scalars['String']['output']>;
};

export type FinalPayment = {
  __typename?: 'FinalPayment';
  amount?: Maybe<Scalars['Float']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
};

export type FuelPrice = {
  __typename?: 'FuelPrice';
  currency: Scalars['String']['output'];
  fuel: FuelType;
  lastUpdated?: Maybe<Scalars['String']['output']>;
  price: Scalars['Float']['output'];
  unit: Scalars['String']['output'];
  validFrom?: Maybe<Scalars['String']['output']>;
};

export enum FuelType {
  AD_BLUE = 'AD_BLUE',
  DIESEL = 'DIESEL',
  ERDGAS = 'ERDGAS',
  LKW_DIESEL = 'LKW_DIESEL',
  LPG = 'LPG',
  SUPER_E5 = 'SUPER_E5',
  SUPER_E10 = 'SUPER_E10',
  SUPER_PLUS = 'SUPER_PLUS',
  ULTIMATE_102 = 'ULTIMATE_102',
  ULTIMATE_DIESEL = 'ULTIMATE_DIESEL'
}

export type GenerateOffersInput = {
  creditAmount?: InputMaybe<Scalars['Float']['input']>;
  creditDuration?: InputMaybe<Scalars['Int']['input']>;
  expiryDate?: InputMaybe<Scalars['String']['input']>;
  marketingCampaignName: Scalars['String']['input'];
  offerCountry: OfferCountry;
  offerDescription: Scalars['String']['input'];
  offerPublicName: Scalars['String']['input'];
  offerType: OfferType;
  partnerCode: Scalars['String']['input'];
  partnerContribution?: InputMaybe<Scalars['Float']['input']>;
  partnerName: Scalars['String']['input'];
  quantity: Scalars['Int']['input'];
  redemptionExpiryDate?: InputMaybe<Scalars['String']['input']>;
  subsDiscount?: InputMaybe<Scalars['Float']['input']>;
  subsDuration?: InputMaybe<Scalars['Int']['input']>;
};

export type GenerateReceiptResponse = {
  __typename?: 'GenerateReceiptResponse';
  message?: Maybe<Scalars['String']['output']>;
  receiptData?: Maybe<ReceiptData>;
  status?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type GenericBpcmResponse = {
  __typename?: 'GenericBPCMResponse';
  payload?: Maybe<GenericBpcmResponsePayload>;
  status?: Maybe<Scalars['String']['output']>;
};

export type GenericBpcmResponsePayload = {
  __typename?: 'GenericBPCMResponsePayload';
  code?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['Boolean']['output']>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export type GeoPoint = {
  __typename?: 'GeoPoint';
  lat: Scalars['Float']['output'];
  lon: Scalars['Float']['output'];
};

export type GeoPointInput = {
  lat: Scalars['Float']['input'];
  lon: Scalars['Float']['input'];
};

export type GetAllSubsTransactionsInput = {
  /**
   * Pass in ISO type date.
   * 1. ISO string: 2024-08-01T00:00:00.000Z
   *
   * End date can be the same day as start date.
   * End date cannot be before start date.
   * End date and start date have to be in the same month.
   */
  endDate: Scalars['String']['input'];
  lastEvaluatedKey?: InputMaybe<LastEvaluatedKeySubscriptionTransaction>;
  /**
   * Limit if you want to limit the number of results returned.
   * Defaults to no limit.
   */
  limit?: InputMaybe<Scalars['Int']['input']>;
  /**
   * If not provided, all payment statuses will be returned.
   * If provided as null all status will be returned.
   */
  paymentStatus?: InputMaybe<PaymentStatusEnum>;
  /**
   * Pass in ISO type date.
   * 1. ISO string: 2024-08-01T00:00:00.000Z
   *
   * Start and end date have to be in the same month.
   */
  startDate: Scalars['String']['input'];
};

export type GetAllSubsTransactionsResponse = {
  __typename?: 'GetAllSubsTransactionsResponse';
  error?: Maybe<Scalars['String']['output']>;
  /** Last evaluated key to be used in the next query if exists. */
  lastEvaluatedKey?: Maybe<LastEvaluatedKeySubscriptionTransactionUnion>;
  results?: Maybe<Array<Maybe<SubscriptionRecord>>>;
  status?: Maybe<Scalars['String']['output']>;
  totalResultsCount?: Maybe<Scalars['Int']['output']>;
};

export type GetLatestRequestIdentifierResponse = {
  __typename?: 'GetLatestRequestIdentifierResponse';
  latestRequestIdentifier?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GetMarketingPreferenceResponse = {
  __typename?: 'GetMarketingPreferenceResponse';
  generalMarketing?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  personalMarketing?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GetReceiptDataResponse = {
  __typename?: 'GetReceiptDataResponse';
  message?: Maybe<Scalars['String']['output']>;
  receiptData?: Maybe<ReceiptData>;
  status?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type GetReceiptDetailResponse = {
  __typename?: 'GetReceiptDetailResponse';
  message?: Maybe<Scalars['String']['output']>;
  receiptDetail?: Maybe<Array<Maybe<ReceiptDetail>>>;
  status?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type GetRfidParamsObjectCard = {
  cardNumber: Scalars['String']['input'];
  exclusiveStartKey?: InputMaybe<Scalars['String']['input']>;
};

export type GetRfidParamsObjectStatus = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  exclusiveStartKey?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  status: Scalars['String']['input'];
};

export type GetSubscriptionRecordsResponse = {
  __typename?: 'GetSubscriptionRecordsResponse';
  lastKey?: Maybe<LastEvaluatedKeyType>;
  results?: Maybe<Array<Maybe<SubscriptionRecord>>>;
};

export type GoCardless = {
  __typename?: 'GoCardless';
  mandateId?: Maybe<Scalars['String']['output']>;
  mandateStatus?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type GoPasswordlessResponse = {
  __typename?: 'GoPasswordlessResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GuestEntitlement = {
  __typename?: 'GuestEntitlement';
  chargepointsAvailable?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  paymentMethods?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rfidEnabled?: Maybe<Scalars['Boolean']['output']>;
  subsEnabled?: Maybe<Scalars['Boolean']['output']>;
};

export type History_BlockingFee = {
  __typename?: 'History_BlockingFee';
  duration?: Maybe<Scalars['Float']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
};

export type History_BlockingFeeInput = {
  duration?: InputMaybe<Scalars['Float']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
};

export type History_ChargeDetails = {
  __typename?: 'History_ChargeDetails';
  authId?: Maybe<Scalars['String']['output']>;
  authType?: Maybe<Scalars['String']['output']>;
  chargeAdditionalFees?: Maybe<Scalars['String']['output']>;
  chargeAdditionalFeesGross?: Maybe<Scalars['String']['output']>;
  chargeBaseFee?: Maybe<Scalars['String']['output']>;
  chargeBaseFeeGross?: Maybe<Scalars['String']['output']>;
  chargeGross?: Maybe<Scalars['String']['output']>;
  chargeNet?: Maybe<Scalars['String']['output']>;
  chargeTax?: Maybe<Scalars['String']['output']>;
  chargeTaxPercentage?: Maybe<Scalars['String']['output']>;
  co2Saving?: Maybe<Scalars['Float']['output']>;
  creditCardPan?: Maybe<Scalars['String']['output']>;
  creditCardType?: Maybe<Scalars['String']['output']>;
  creditCardUuid?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  discount?: Maybe<Scalars['Float']['output']>;
  energyConsumed?: Maybe<History_ChargeMetaData>;
  grossAmountLocalCurrency?: Maybe<Scalars['String']['output']>;
  isStandardInvoice?: Maybe<Scalars['Boolean']['output']>;
  meterEnd?: Maybe<Scalars['String']['output']>;
  meterStart?: Maybe<Scalars['String']['output']>;
  minCost?: Maybe<Scalars['String']['output']>;
  offerCodes?: Maybe<Array<History_OfferCode>>;
  overstay?: Maybe<History_OverstayCost>;
  payback?: Maybe<Scalars['String']['output']>;
  power?: Maybe<History_ChargeMetaData>;
  providerChargeIds?: Maybe<Array<Maybe<History_ProviderChargeIds>>>;
  rateName?: Maybe<Scalars['String']['output']>;
  stateOfCharge?: Maybe<History_ChargeMetaData>;
  status?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  totalGross?: Maybe<Scalars['String']['output']>;
  unitCost?: Maybe<Scalars['String']['output']>;
  unitOfMeasure?: Maybe<Scalars['String']['output']>;
};

export type History_ChargeDetailsInput = {
  authId?: InputMaybe<Scalars['String']['input']>;
  authType?: InputMaybe<Scalars['String']['input']>;
  chargeAdditionalFees?: InputMaybe<Scalars['Float']['input']>;
  chargeBaseFee?: InputMaybe<Scalars['Float']['input']>;
  chargeGross?: InputMaybe<Scalars['Float']['input']>;
  chargeNet?: InputMaybe<Scalars['Float']['input']>;
  chargeTax?: InputMaybe<Scalars['Float']['input']>;
  chargeTaxPercentage?: InputMaybe<Scalars['Float']['input']>;
  co2Saving?: InputMaybe<Scalars['String']['input']>;
  creditCardPan?: InputMaybe<Scalars['String']['input']>;
  creditCardType?: InputMaybe<Scalars['String']['input']>;
  creditCardUuid?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  energyConsumed?: InputMaybe<History_ChargeMetaDataInput>;
  grossAmountLocalCurrency?: InputMaybe<Scalars['Float']['input']>;
  meterEnd?: InputMaybe<Scalars['String']['input']>;
  meterStart?: InputMaybe<Scalars['String']['input']>;
  minCost?: InputMaybe<Scalars['Float']['input']>;
  overstay?: InputMaybe<History_OverstayCostInput>;
  payback?: InputMaybe<Scalars['String']['input']>;
  power?: InputMaybe<History_ChargeMetaDataInput>;
  providerChargeIds?: InputMaybe<Array<InputMaybe<History_ChargeIdsInput>>>;
  rateName?: InputMaybe<Scalars['String']['input']>;
  stateOfCharge?: InputMaybe<History_ChargeMetaDataInput>;
  status?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
  totalGross?: InputMaybe<Scalars['Float']['input']>;
  unitCost?: InputMaybe<Scalars['Float']['input']>;
  unitOfMeasure?: InputMaybe<Scalars['String']['input']>;
};

export type History_ChargeDuration = {
  __typename?: 'History_ChargeDuration';
  days?: Maybe<Scalars['Int']['output']>;
  formatted?: Maybe<Scalars['String']['output']>;
  hours?: Maybe<Scalars['Int']['output']>;
  minutes?: Maybe<Scalars['Int']['output']>;
  seconds?: Maybe<Scalars['Int']['output']>;
};

export type History_ChargeIdsInput = {
  type?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};

export type History_ChargeMetaData = {
  __typename?: 'History_ChargeMetaData';
  units?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['Float']['output']>;
};

export type History_ChargeMetaDataInput = {
  units?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['Float']['input']>;
};

export type History_ChargePoint = {
  __typename?: 'History_ChargePoint';
  address?: Maybe<Scalars['String']['output']>;
  apolloExternalId?: Maybe<Scalars['String']['output']>;
  apolloInternalId?: Maybe<Scalars['String']['output']>;
  blockingFee?: Maybe<History_BlockingFee>;
  chargepointId?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  connector?: Maybe<History_ConnectorHistory>;
  country?: Maybe<Scalars['String']['output']>;
  cpo?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  displayAddress?: Maybe<Scalars['String']['output']>;
  grossUnitCost?: Maybe<Scalars['Float']['output']>;
  postcode?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  providerExternalId?: Maybe<Scalars['String']['output']>;
  publicName?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type History_ChargepointInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  apolloExternalId?: InputMaybe<Scalars['String']['input']>;
  apolloInternalId?: InputMaybe<Scalars['String']['input']>;
  blockingFee?: InputMaybe<History_BlockingFeeInput>;
  city?: InputMaybe<Scalars['String']['input']>;
  connector?: InputMaybe<History_ConnectorInput>;
  country?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  displayAddress?: InputMaybe<Scalars['String']['input']>;
  grossUnitCost?: InputMaybe<Scalars['Float']['input']>;
  postcode?: InputMaybe<Scalars['String']['input']>;
  provider?: InputMaybe<Scalars['String']['input']>;
  providerExternalId?: InputMaybe<Scalars['String']['input']>;
  publicName?: InputMaybe<Scalars['String']['input']>;
  unit?: InputMaybe<Scalars['String']['input']>;
};

export type History_ConnectorHistory = {
  __typename?: 'History_ConnectorHistory';
  connectorExternalId?: Maybe<Scalars['String']['output']>;
  connectorInternalId?: Maybe<Scalars['String']['output']>;
  phase?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type History_ConnectorInput = {
  connectorExternalId?: InputMaybe<Scalars['String']['input']>;
  connectorInternalId?: InputMaybe<Scalars['String']['input']>;
  phase?: InputMaybe<Scalars['String']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type History_CreateHistoryResponse = {
  __typename?: 'History_CreateHistoryResponse';
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type History_HistoryRecord = {
  __typename?: 'History_HistoryRecord';
  cdrType?: Maybe<Scalars['String']['output']>;
  cdr_filename?: Maybe<Scalars['String']['output']>;
  chargeDetails?: Maybe<History_ChargeDetails>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  chargepoint?: Maybe<History_ChargePoint>;
  duration?: Maybe<History_ChargeDuration>;
  eventTime?: Maybe<Scalars['String']['output']>;
  homeCountry?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  paymentStatus?: Maybe<Scalars['String']['output']>;
  receiptId?: Maybe<Scalars['String']['output']>;
  referenceChargeSessionId?: Maybe<Scalars['String']['output']>;
  sessionId?: Maybe<Scalars['String']['output']>;
  timestamp?: Maybe<History_TimeStamp>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<Scalars['String']['output']>;
};

export type History_HistoryRecordInput = {
  chargeDetails?: InputMaybe<History_ChargeDetailsInput>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  chargepoint?: InputMaybe<History_ChargepointInput>;
  dateEnd?: InputMaybe<Scalars['String']['input']>;
  dateStart?: InputMaybe<Scalars['String']['input']>;
  eventTime?: InputMaybe<Scalars['String']['input']>;
  payback?: InputMaybe<Scalars['String']['input']>;
  sessionId?: InputMaybe<Scalars['String']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  transactionNumber?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type History_HistoryRecordsResponse = {
  __typename?: 'History_HistoryRecordsResponse';
  count?: Maybe<Scalars['Int']['output']>;
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  lastKey?: Maybe<History_LastEvaluatedKey>;
  page?: Maybe<Scalars['Int']['output']>;
  results?: Maybe<Array<Maybe<History_HistoryRecord>>>;
};

export type History_LastEvaluatedKey = {
  __typename?: 'History_LastEvaluatedKey';
  charge_session_id?: Maybe<Scalars['String']['output']>;
  date_end?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type History_LastEvaluatedKeyInput = {
  charge_session_id?: InputMaybe<Scalars['String']['input']>;
  date_end?: InputMaybe<Scalars['String']['input']>;
  date_start?: InputMaybe<Scalars['String']['input']>;
  user_id?: InputMaybe<Scalars['String']['input']>;
};

export type History_OfferCode = {
  __typename?: 'History_OfferCode';
  offerCode: Scalars['String']['output'];
  offerValue: Scalars['Float']['output'];
};

export type History_OfferCodeInput = {
  offerCode: Scalars['String']['input'];
  offerValue: Scalars['Float']['input'];
};

export type History_OverstayCost = {
  __typename?: 'History_OverstayCost';
  duration?: Maybe<Scalars['String']['output']>;
  fineGross?: Maybe<Scalars['String']['output']>;
  fineNet?: Maybe<Scalars['String']['output']>;
  fineTax?: Maybe<Scalars['String']['output']>;
  fineTaxPercentage?: Maybe<Scalars['String']['output']>;
  fineUnitCost?: Maybe<Scalars['String']['output']>;
};

export type History_OverstayCostInput = {
  duration?: InputMaybe<Scalars['String']['input']>;
  fineGross?: InputMaybe<Scalars['Float']['input']>;
  fineNet?: InputMaybe<Scalars['Float']['input']>;
  fineTax?: InputMaybe<Scalars['Float']['input']>;
  fineTaxPercentage?: InputMaybe<Scalars['Float']['input']>;
  fineUnitCost?: InputMaybe<Scalars['Float']['input']>;
};

export type History_ProviderChargeIds = {
  __typename?: 'History_ProviderChargeIds';
  type?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type History_TimeStamp = {
  __typename?: 'History_TimeStamp';
  start?: Maybe<Scalars['String']['output']>;
  stop?: Maybe<Scalars['String']['output']>;
};

export type History_UpdateHistoryResponse = {
  __typename?: 'History_UpdateHistoryResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Hours = {
  __typename?: 'Hours';
  fri1?: Maybe<Scalars['String']['output']>;
  fri2?: Maybe<Scalars['String']['output']>;
  mon1?: Maybe<Scalars['String']['output']>;
  mon2?: Maybe<Scalars['String']['output']>;
  sat1?: Maybe<Scalars['String']['output']>;
  sat2?: Maybe<Scalars['String']['output']>;
  sun1?: Maybe<Scalars['String']['output']>;
  sun2?: Maybe<Scalars['String']['output']>;
  thu1?: Maybe<Scalars['String']['output']>;
  thu2?: Maybe<Scalars['String']['output']>;
  tue1?: Maybe<Scalars['String']['output']>;
  tue2?: Maybe<Scalars['String']['output']>;
  wed1?: Maybe<Scalars['String']['output']>;
  wed2?: Maybe<Scalars['String']['output']>;
};

export type InsertItem = {
  additional_address_1?: InputMaybe<Scalars['String']['input']>;
  additional_address_2?: InputMaybe<Scalars['String']['input']>;
  address_city?: InputMaybe<Scalars['String']['input']>;
  address_country?: InputMaybe<Scalars['String']['input']>;
  address_line?: InputMaybe<Scalars['String']['input']>;
  address_postcode?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  date_added?: InputMaybe<Scalars['String']['input']>;
  first_name?: InputMaybe<Scalars['String']['input']>;
  last_name?: InputMaybe<Scalars['String']['input']>;
  rfid_card_number: Scalars['String']['input'];
  rfid_status: Scalars['String']['input'];
  user_id?: InputMaybe<Scalars['String']['input']>;
};

export type InsertRfidRecordResponse = {
  __typename?: 'InsertRFIDRecordResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export enum IntersectionType {
  EXCLUDE = 'EXCLUDE',
  INCLUDE = 'INCLUDE',
  ONLY = 'ONLY'
}

export type Invoice = {
  __typename?: 'Invoice';
  invoice_currency?: Maybe<Scalars['String']['output']>;
  invoice_due?: Maybe<Scalars['String']['output']>;
  invoice_number?: Maybe<Scalars['Int']['output']>;
  invoice_period_end?: Maybe<Scalars['String']['output']>;
  invoice_period_start?: Maybe<Scalars['String']['output']>;
  invoice_total?: Maybe<Scalars['Float']['output']>;
  less_credits_applied?: Maybe<Scalars['Float']['output']>;
  net_total_detailed_charges?: Maybe<Scalars['Float']['output']>;
  net_total_new_card_fee?: Maybe<Scalars['Float']['output']>;
  net_total_overstay_fees?: Maybe<Scalars['Float']['output']>;
  net_total_subscriptions?: Maybe<Scalars['Float']['output']>;
  total_gross?: Maybe<Scalars['Float']['output']>;
  total_net?: Maybe<Scalars['Float']['output']>;
  total_vat?: Maybe<Scalars['Float']['output']>;
  transactions?: Maybe<Array<Maybe<TransactionDataItem>>>;
  vat_percentage?: Maybe<Scalars['Float']['output']>;
};

export type JourneyDetails = {
  __typename?: 'JourneyDetails';
  dropInSession?: Maybe<DropInSession>;
  journeyId?: Maybe<Scalars['String']['output']>;
};

export type LastEvaluatedKeyDayIndexType = {
  __typename?: 'LastEvaluatedKeyDayIndexType';
  transaction_day?: Maybe<Scalars['String']['output']>;
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type LastEvaluatedKeyInput = {
  transaction_id?: InputMaybe<Scalars['String']['input']>;
};

export type LastEvaluatedKeyMonthIndexType = {
  __typename?: 'LastEvaluatedKeyMonthIndexType';
  transaction_id?: Maybe<Scalars['String']['output']>;
  transaction_month?: Maybe<Scalars['String']['output']>;
};

export type LastEvaluatedKeySubscriptionTransaction = {
  transaction_day?: InputMaybe<Scalars['String']['input']>;
  /**
   * Pass only combination of transaction_id and transaction_month
   * or transaction_id and transaction_day.
   *
   * Otherwise will throw an error.
   */
  transaction_id?: InputMaybe<Scalars['String']['input']>;
  transaction_month?: InputMaybe<Scalars['String']['input']>;
};

export type LastEvaluatedKeySubscriptionTransactionUnion = LastEvaluatedKeyDayIndexType | LastEvaluatedKeyMonthIndexType;

export type LastEvaluatedKeyType = {
  __typename?: 'LastEvaluatedKeyType';
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type Marker = MarkerCluster | MarkerPoint;

export type MarkerCluster = {
  __typename?: 'MarkerCluster';
  count: Scalars['Float']['output'];
  id: Scalars['Float']['output'];
  location: GeoPoint;
  siteIds: Array<Scalars['String']['output']>;
  siteLocations: Array<GeoPoint>;
};

export type MarkerPoint = {
  __typename?: 'MarkerPoint';
  location: GeoPoint;
  site: MarkerPointSiteInfo;
};

export type MarkerPointSiteInfo = {
  __typename?: 'MarkerPointSiteInfo';
  brandName?: Maybe<CrossAcceptanceBrand>;
  country: Scalars['String']['output'];
  cpo?: Maybe<Scalars['String']['output']>;
  evAvailability: EvAvailability;
  evDetails: EvDetails;
  fuelPrices?: Maybe<Array<FuelPrice>>;
  hasEvCharging: Scalars['Boolean']['output'];
  hasFuel: Scalars['Boolean']['output'];
  isAds: Scalars['Boolean']['output'];
  location: GeoPoint;
  provider: Provider;
  siteId: Scalars['String']['output'];
};

export enum MarkerType {
  MarkerCluster = 'MarkerCluster',
  MarkerPoint = 'MarkerPoint'
}

export type MarketingConsents = {
  generalMarketing?: InputMaybe<Scalars['Boolean']['input']>;
  personalMarketing?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Membership = {
  __typename?: 'Membership';
  membershipBillingCycleDate?: Maybe<Scalars['String']['output']>;
  membershipEndDate?: Maybe<Scalars['String']['output']>;
  membershipExternalId?: Maybe<Scalars['String']['output']>;
  membershipId?: Maybe<Scalars['String']['output']>;
  membershipRequestCancelDate?: Maybe<Scalars['String']['output']>;
  membershipStartDate?: Maybe<Scalars['String']['output']>;
  membershipStatus?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<Scalars['String']['output']>;
};

export type MembershipInternalResponse = {
  __typename?: 'MembershipInternalResponse';
  membership?: Maybe<Membership>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  addFavourite?: Maybe<MutationAddFavouriteResponse>;
  addNewToken: UserToken;
  addPaymentMethodWallet?: Maybe<Scalars['String']['output']>;
  addRFID?: Maybe<AddRfidResponse>;
  addRoaming?: Maybe<AddRoamingResponse>;
  applyOffer: MutationApplyOfferResult;
  authLookup3DS?: Maybe<AuthLookup>;
  authenticate3DS?: Maybe<Authenticate>;
  authoriseWalletPayment?: Maybe<Scalars['Boolean']['output']>;
  blockRFID?: Maybe<BlockRfidResponse>;
  cancelSubscription?: Maybe<GenericBpcmResponse>;
  cancelWalletSubscription?: Maybe<CancelSubscriptionResponse>;
  capturePayment?: Maybe<Scalars['Boolean']['output']>;
  clearChargeSession?: Maybe<EventResponse>;
  createHistoryRecord?: Maybe<History_CreateHistoryResponse>;
  createMembershipInternal?: Maybe<MembershipInternalResponse>;
  createOrder?: Maybe<OrderDetails>;
  createPaymentRecordInternal?: Maybe<Scalars['Boolean']['output']>;
  createSubsHistoryRecord?: Maybe<CreateSubsHistoryRecordResponse>;
  createSubsPlanInternal?: Maybe<PlanResponse>;
  createTariff: MutationCreateTariffResult;
  createWalletSubsPlan?: Maybe<SubsPlansResponse>;
  createWalletSubscription?: Maybe<CreateWalletSubscriptionResponse>;
  deleteAccountRequest?: Maybe<DeleteAccountResponse>;
  deletePartnerToken?: Maybe<DeletePartnerTokenResponse>;
  deletePaymentMethodWallet?: Maybe<Scalars['String']['output']>;
  deleteRfidData?: Maybe<DeleteRfidRecordResponse>;
  deleteSUBSDefaultPaymentMethodWallet?: Maybe<DeleteSubsDefPaymentMethodResponse>;
  deleteUserChargeEvents?: Maybe<DeleteUserChargeEventsResponse>;
  deleteUserInternal?: Maybe<DeleteUserRespnse>;
  editOffers: MutationEditOffersResult;
  expireOffers: MutationExpireOffersResult;
  generateOffers: MutationGenerateOffersResult;
  generateReceipt?: Maybe<GenerateReceiptResponse>;
  getToken3DS?: Maybe<Scalars['String']['output']>;
  goPasswordlessUserStageOne?: Maybe<GoPasswordlessResponse>;
  goPasswordlessUserStageTwo?: Maybe<GoPasswordlessResponse>;
  insertRfidData?: Maybe<InsertRfidRecordResponse>;
  makeSimplePayment?: Maybe<SimplePayment>;
  notifyRegistration?: Maybe<RegistrationResponse>;
  offerCreditStatusToUsed: MutationOfferCreditStatusToUsedResult;
  offerSubsStatusToUsed: MutationOfferSubsStatusToUsedResult;
  onboardAccount?: Maybe<OnboardAccountResponse>;
  onboardCharging?: Maybe<OnboardChargingResponse>;
  onboardPartner?: Maybe<OnboardPartnerResponse>;
  onboardRoaming?: Maybe<OnboardRoamingResponse>;
  onboardUberInternal?: Maybe<OnboardUberInternalResponse>;
  preAuth?: Maybe<Scalars['Boolean']['output']>;
  refundOrder?: Maybe<RefundOrderResponse>;
  releaseAdHocToken: ReleaseAdHocToken;
  removeFavourite?: Maybe<MutationRemoveFavouriteResponse>;
  removePartnerDriverStatus?: Maybe<RemovePartnerDriverStatusResponse>;
  replaceRFID?: Maybe<RfidResponse>;
  requestRFID?: Maybe<PaymentRfidResponse>;
  retryWalletSubsPayment?: Maybe<SubsPaymentResponse>;
  saveReceipt?: Maybe<SaveReceiptResponse>;
  saveTransactionIdInternal?: Maybe<SaveTransactionIdInternalResponse>;
  setDefaultPaymentMethodWallet?: Maybe<Scalars['String']['output']>;
  startCharge?: Maybe<EventResponse>;
  startJourney?: Maybe<JourneyDetails>;
  startSession: StartSessionResponse;
  stopCharge?: Maybe<EventResponse>;
  stopSession: StopSessionResponse;
  storeOperationId?: Maybe<StoreOperationIdResponse>;
  storePaymentToken?: Maybe<StorePaymentTokenResponse>;
  unblockPartnerToken?: Maybe<UnblockPartnerTokenResponse>;
  unblockRFID?: Maybe<UnblockRfidResponse>;
  updateHistoryRecord?: Maybe<History_UpdateHistoryResponse>;
  updateMarketingPreference?: Maybe<UpdateMarketingPreferenceResponse>;
  updateMembershipInternal?: Maybe<MembershipInternalResponse>;
  updateOfferCreditBalance: MutationUpdateOfferCreditBalanceResult;
  updateRFIDInternal?: Maybe<UpdateRfidInternalResponse>;
  updateRfidData?: Maybe<UpdateRfidRecordResponse>;
  updateSubsPlanDpaas?: Maybe<UpdatedSubsPlanResponse>;
  updateSubsPlanInternal?: Maybe<UpdatedSubsPlanResponse>;
  updateSubscriptionPreference?: Maybe<GenericBpcmResponse>;
  updateTagInternal?: Maybe<UpdateInternalResponse>;
  updateToken: UpdateToken;
  updateUserInternal?: Maybe<UpdateInternalResponse>;
  updateUserSchemeInternal?: Maybe<UpdateUserSchemeInternalResponse>;
  updateWalletSubscription?: Maybe<UpdateSubscriptionDetails>;
  upsertPartnerToken?: Maybe<UpsertPartnerTokenResponse>;
  upsertWalletSubscription?: Maybe<UpsertWalletSubscriptionResponse>;
  voidOrder?: Maybe<Scalars['Boolean']['output']>;
};


export type MutationAddFavouriteArgs = {
  assetType: AssetType;
  brandId: BrandId;
  favouriteLabel?: InputMaybe<Scalars['String']['input']>;
  serial: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationAddNewTokenArgs = {
  data: UserTokenInput;
};


export type MutationAddPaymentMethodWalletArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
  default?: InputMaybe<Scalars['Boolean']['input']>;
  subsDefaultFlag?: InputMaybe<Scalars['Boolean']['input']>;
  threeDS?: InputMaybe<ThreeDs>;
  token: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationAddRfidArgs = {
  payload?: InputMaybe<AddRfid>;
};


export type MutationAddRoamingArgs = {
  country: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationApplyOfferArgs = {
  offerCode: Scalars['String']['input'];
  updateWalletSubs?: InputMaybe<Scalars['Boolean']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationAuthLookup3DsArgs = {
  amount: Scalars['Float']['input'];
  country: Scalars['String']['input'];
  orderId: Scalars['String']['input'];
  paymentMethod: Scalars['String']['input'];
  paymentMethodType: Scalars['String']['input'];
  referenceId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationAuthenticate3DsArgs = {
  country: Scalars['String']['input'];
  paymentMethod: Scalars['String']['input'];
  paymentMethodType: Scalars['String']['input'];
  transactionId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationAuthoriseWalletPaymentArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  appCountry?: InputMaybe<Scalars['String']['input']>;
  appType?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  threeDS?: InputMaybe<ThreeDs>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationBlockRfidArgs = {
  payload?: InputMaybe<BlockRfid>;
};


export type MutationCancelSubscriptionArgs = {
  userId: Scalars['String']['input'];
};


export type MutationCancelWalletSubscriptionArgs = {
  userId: Scalars['String']['input'];
};


export type MutationCapturePaymentArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  appCountry?: InputMaybe<Scalars['String']['input']>;
  appType?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationClearChargeSessionArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateHistoryRecordArgs = {
  cdrType?: InputMaybe<Scalars['String']['input']>;
  cdr_filename?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  historyRecord: History_HistoryRecordInput;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  paymentStatus: PaymentStatus;
  referenceChargeSessionId?: InputMaybe<Scalars['String']['input']>;
  sessionType?: InputMaybe<SessionType>;
  userId?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateMembershipInternalArgs = {
  billingCycleDate?: InputMaybe<Scalars['String']['input']>;
  membershipExternalId?: InputMaybe<Scalars['String']['input']>;
  membershipUpgrade: Scalars['Boolean']['input'];
  partner?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationCreateOrderArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreatePaymentRecordInternalArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  chargeSessionId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  finalPaymentStatus?: InputMaybe<Scalars['String']['input']>;
  paymentId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationCreateSubsHistoryRecordArgs = {
  paymentStatus: Scalars['String']['input'];
  subsHistoryRecord: SubsHistoryRecord;
  transactionId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationCreateSubsPlanInternalArgs = {
  payload: CreateSubsPlanInput;
};


export type MutationCreateTariffArgs = {
  tariff: CreateTariffInput;
};


export type MutationCreateWalletSubsPlanArgs = {
  payload?: InputMaybe<SubsPlan>;
};


export type MutationCreateWalletSubscriptionArgs = {
  offerCode?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteAccountRequestArgs = {
  payload?: InputMaybe<DeleteAccountRequestInput>;
};


export type MutationDeletePartnerTokenArgs = {
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationDeletePaymentMethodWalletArgs = {
  paymentMethodId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationDeleteRfidDataArgs = {
  payload?: InputMaybe<DeleteRfidParamsObject>;
};


export type MutationDeleteSubsDefaultPaymentMethodWalletArgs = {
  paymentMethodId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationDeleteUserChargeEventsArgs = {
  privateId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationDeleteUserInternalArgs = {
  softDelete?: InputMaybe<Scalars['Boolean']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationEditOffersArgs = {
  offers: Array<EditOfferInput>;
};


export type MutationGenerateOffersArgs = {
  options: GenerateOffersInput;
};


export type MutationGenerateReceiptArgs = {
  address?: InputMaybe<AddressInput>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  isChargeReceipt?: InputMaybe<Scalars['Boolean']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  subsUserCountry?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type MutationGetToken3DsArgs = {
  amount: Scalars['Float']['input'];
  country: Scalars['String']['input'];
  orderId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationGoPasswordlessUserStageOneArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  phoneCountryCode: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  userAgent?: InputMaybe<Scalars['String']['input']>;
};


export type MutationGoPasswordlessUserStageTwoArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  code: Scalars['String']['input'];
};


export type MutationInsertRfidDataArgs = {
  payload?: InputMaybe<InsertItem>;
};


export type MutationMakeSimplePaymentArgs = {
  amount: Scalars['Float']['input'];
  appCountry: Scalars['String']['input'];
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  correlationId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  operationId?: InputMaybe<Scalars['String']['input']>;
  outstanding?: InputMaybe<Scalars['Boolean']['input']>;
  paymentId: Scalars['String']['input'];
  paymentMethodId: Scalars['String']['input'];
  threeDS?: InputMaybe<ThreeDs>;
  userId: Scalars['String']['input'];
};


export type MutationNotifyRegistrationArgs = {
  appCountry: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationOfferCreditStatusToUsedArgs = {
  offerCode: Scalars['String']['input'];
};


export type MutationOfferSubsStatusToUsedArgs = {
  override?: InputMaybe<Scalars['Boolean']['input']>;
  subsPlanId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationOnboardAccountArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  homeCountry?: InputMaybe<SupportedCountry>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationOnboardChargingArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationOnboardPartnerArgs = {
  membershipId?: InputMaybe<Scalars['String']['input']>;
  partnerType: SupportedPartner;
  userId: Scalars['String']['input'];
};


export type MutationOnboardRoamingArgs = {
  userId: Scalars['String']['input'];
};


export type MutationOnboardUberInternalArgs = {
  userId: Scalars['String']['input'];
};


export type MutationPreAuthArgs = {
  token?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRefundOrderArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationReleaseAdHocTokenArgs = {
  tokenUid: Scalars['String']['input'];
};


export type MutationRemoveFavouriteArgs = {
  assetType: AssetType;
  brandId: BrandId;
  serial: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationRemovePartnerDriverStatusArgs = {
  partnerType: SupportedPartner;
  userId: Scalars['String']['input'];
};


export type MutationReplaceRfidArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRequestRfidArgs = {
  payload?: InputMaybe<RequestRfidRequest>;
};


export type MutationRetryWalletSubsPaymentArgs = {
  membershipId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSaveReceiptArgs = {
  cdrType?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  data?: InputMaybe<Scalars['String']['input']>;
  isStandardInvoice?: InputMaybe<Scalars['Boolean']['input']>;
  language?: InputMaybe<Scalars['String']['input']>;
  subsUserCountry?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSaveTransactionIdInternalArgs = {
  authId?: InputMaybe<Scalars['String']['input']>;
  chargeStatus?: InputMaybe<ChargeStatus>;
  connectorId?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  transactionNumber?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSetDefaultPaymentMethodWalletArgs = {
  isSubsDefault?: InputMaybe<Scalars['Boolean']['input']>;
  paymentMethodId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationStartChargeArgs = {
  event?: InputMaybe<NewChargeEvent>;
};


export type MutationStartJourneyArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  appType?: InputMaybe<Scalars['String']['input']>;
  paymentMethodType?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationStartSessionArgs = {
  data: StartSessionInputArgs;
};


export type MutationStopChargeArgs = {
  event?: InputMaybe<NewChargeEvent>;
};


export type MutationStopSessionArgs = {
  data: StopSessionInputArgs;
};


export type MutationStoreOperationIdArgs = {
  operationId: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
};


export type MutationStorePaymentTokenArgs = {
  isDefault?: InputMaybe<Scalars['Boolean']['input']>;
  isSubsDefault?: InputMaybe<Scalars['Boolean']['input']>;
  token: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationUnblockPartnerTokenArgs = {
  token: Scalars['String']['input'];
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationUnblockRfidArgs = {
  payload?: InputMaybe<UnblockRfid>;
};


export type MutationUpdateHistoryRecordArgs = {
  chargeSessionId: Scalars['String']['input'];
  discount?: InputMaybe<Scalars['Float']['input']>;
  offerCodes?: InputMaybe<Array<History_OfferCodeInput>>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  paymentStatus?: InputMaybe<Scalars['String']['input']>;
  referenceChargeSessionId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateMarketingPreferenceArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  consents: MarketingConsents;
  userAgent: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationUpdateMembershipInternalArgs = {
  billingCycleDate?: InputMaybe<Scalars['String']['input']>;
  membershipCancelRequested: Scalars['Boolean']['input'];
  membershipId: Scalars['String']['input'];
  membershipStatus?: InputMaybe<Scalars['String']['input']>;
  subsFlag?: InputMaybe<Scalars['Boolean']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationUpdateOfferCreditBalanceArgs = {
  offerCode: Scalars['String']['input'];
  usedCredit: Scalars['Float']['input'];
};


export type MutationUpdateRfidInternalArgs = {
  payload?: InputMaybe<UpdateRfidInternalPayload>;
};


export type MutationUpdateRfidDataArgs = {
  payload?: InputMaybe<UpdateRfidParamsObject>;
};


export type MutationUpdateSubsPlanDpaasArgs = {
  payload?: InputMaybe<UpdateSubsPlanPayload>;
};


export type MutationUpdateSubsPlanInternalArgs = {
  payload?: InputMaybe<UpdateSubsPlanPayload>;
};


export type MutationUpdateSubscriptionPreferenceArgs = {
  addressDetails?: InputMaybe<AddressDetails>;
  cardPreference: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationUpdateTagInternalArgs = {
  payload: UpdateTagInternalInput;
};


export type MutationUpdateTokenArgs = {
  data: UpdateTokenInput;
};


export type MutationUpdateUserInternalArgs = {
  payload: UpdateUserInternalInput;
};


export type MutationUpdateUserSchemeInternalArgs = {
  schemeName: SupportedUserScheme;
  userId: Scalars['String']['input'];
};


export type MutationUpdateWalletSubscriptionArgs = {
  offerSubsPlanId?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationUpsertPartnerTokenArgs = {
  expiryDate?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<TokenStatus>;
  token: Scalars['String']['input'];
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationUpsertWalletSubscriptionArgs = {
  offerCode?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationVoidOrderArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
};

export type MutationAddFavouriteResponse = {
  __typename?: 'MutationAddFavouriteResponse';
  status?: Maybe<Scalars['Int']['output']>;
};

export type MutationApplyOfferResult = {
  __typename?: 'MutationApplyOfferResult';
  error?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Offer>;
};

export type MutationCreateTariffResult = {
  __typename?: 'MutationCreateTariffResult';
  error?: Maybe<Scalars['String']['output']>;
  tariff?: Maybe<Tariff>;
};

export type MutationEditOffersResult = {
  __typename?: 'MutationEditOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  failed?: Maybe<Array<FailedOffer>>;
  success?: Maybe<Array<Offer>>;
};

export type MutationExpireOffersResult = {
  __typename?: 'MutationExpireOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  expired?: Maybe<Array<Scalars['String']['output']>>;
  failed?: Maybe<Array<Scalars['String']['output']>>;
};

export type MutationGenerateOffersResult = {
  __typename?: 'MutationGenerateOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  offers?: Maybe<Array<Offer>>;
};

export type MutationOfferCreditStatusToUsedResult = {
  __typename?: 'MutationOfferCreditStatusToUsedResult';
  error?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Offer>;
};

export type MutationOfferSubsStatusToUsedResult = {
  __typename?: 'MutationOfferSubsStatusToUsedResult';
  error?: Maybe<Scalars['String']['output']>;
  offers?: Maybe<Array<Offer>>;
};

export type MutationRemoveFavouriteResponse = {
  __typename?: 'MutationRemoveFavouriteResponse';
  status?: Maybe<Scalars['Int']['output']>;
};

export type MutationUpdateOfferCreditBalanceResult = {
  __typename?: 'MutationUpdateOfferCreditBalanceResult';
  error?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Offer>;
};

export type NewChargeEvent = {
  apolloInternalId?: InputMaybe<Scalars['String']['input']>;
  connectorInternalId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type Offer = {
  __typename?: 'Offer';
  createdDate: Scalars['String']['output'];
  creditAmount?: Maybe<Scalars['Float']['output']>;
  creditBalance?: Maybe<Scalars['Float']['output']>;
  creditDuration?: Maybe<Scalars['Int']['output']>;
  creditStatus?: Maybe<OfferStatus>;
  currency?: Maybe<OfferCurrency>;
  expiryDate?: Maybe<Scalars['String']['output']>;
  monthsRemaining?: Maybe<Scalars['Int']['output']>;
  offerCode: Scalars['String']['output'];
  offerCountry?: Maybe<OfferCountry>;
  offerName: Scalars['String']['output'];
  offerNotes?: Maybe<Scalars['String']['output']>;
  offerPublicDescription: Scalars['String']['output'];
  offerType: OfferType;
  overrideDate?: Maybe<Scalars['String']['output']>;
  partnerContribution?: Maybe<Scalars['Float']['output']>;
  partnerName?: Maybe<Scalars['String']['output']>;
  queueStatus?: Maybe<OfferQueueStatus>;
  redemptionDate?: Maybe<Scalars['String']['output']>;
  redemptionExpiryDate?: Maybe<Scalars['String']['output']>;
  status: OfferStatus;
  subsDiscount?: Maybe<Scalars['Float']['output']>;
  subsDuration?: Maybe<Scalars['Int']['output']>;
  subsPlanId?: Maybe<Scalars['String']['output']>;
  subsStatus?: Maybe<OfferSubsStatus>;
  updatedDate: Scalars['String']['output'];
  userId?: Maybe<Scalars['String']['output']>;
};

export enum OfferCountry {
  UK = 'UK'
}

export enum OfferCurrency {
  GBP = 'GBP'
}

export enum OfferInvalidReason {
  COUNTRY_MISMATCH = 'COUNTRY_MISMATCH',
  EXCEEDED_OFFER_LIMIT = 'EXCEEDED_OFFER_LIMIT',
  EXPIRED_OFFER = 'EXPIRED_OFFER',
  OFFER_NOT_FOUND = 'OFFER_NOT_FOUND',
  PARTNER_MISMATCH = 'PARTNER_MISMATCH',
  PARTNER_RESTRICTION = 'PARTNER_RESTRICTION',
  REDEEMED_OFFER = 'REDEEMED_OFFER'
}

export enum OfferQueueStatus {
  PENDING_DELETION = 'PENDING_DELETION',
  QUEUED = 'QUEUED'
}

export enum OfferStatus {
  AVAILABLE = 'AVAILABLE',
  EXPIRED = 'EXPIRED',
  EXPIRED_REDEEM = 'EXPIRED_REDEEM',
  REDEEMED = 'REDEEMED',
  UNAVAILABLE = 'UNAVAILABLE',
  USED = 'USED'
}

export enum OfferSubsStatus {
  AVAILABLE = 'AVAILABLE',
  EXPIRED_REDEEM = 'EXPIRED_REDEEM',
  REDEEMED = 'REDEEMED',
  USED = 'USED'
}

export enum OfferType {
  COMBO = 'COMBO',
  CREDIT = 'CREDIT',
  SUBS = 'SUBS'
}

export type OnboardAccountResponse = {
  __typename?: 'OnboardAccountResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardChargingResponse = {
  __typename?: 'OnboardChargingResponse';
  message: Scalars['String']['output'];
  providers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardPartnerResponse = {
  __typename?: 'OnboardPartnerResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardRoamingResponse = {
  __typename?: 'OnboardRoamingResponse';
  message: Scalars['String']['output'];
  providers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardUberInternalResponse = {
  __typename?: 'OnboardUberInternalResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardingStatus = {
  __typename?: 'OnboardingStatus';
  account?: Maybe<Scalars['Boolean']['output']>;
  charging?: Maybe<Scalars['Boolean']['output']>;
  country?: Maybe<SupportedCountry>;
  partners?: Maybe<Array<Maybe<Partner>>>;
  providers?: Maybe<Array<Maybe<ProviderWithDateCreated>>>;
  roaming?: Maybe<Scalars['Boolean']['output']>;
};

export type OperatorCount = {
  __typename?: 'OperatorCount';
  count: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type OrderDetails = {
  __typename?: 'OrderDetails';
  correlationId?: Maybe<Scalars['String']['output']>;
  merchant?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Partner = {
  __typename?: 'Partner';
  partner?: Maybe<SupportedPartner>;
};

export type PartnerTariffs = {
  __typename?: 'PartnerTariffs';
  externalMembershipId?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<SupportedPartner>;
  schemeName?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  userAuthenticationId?: Maybe<Scalars['String']['output']>;
};

export type Payment = {
  __typename?: 'Payment';
  url?: Maybe<Scalars['String']['output']>;
};

/** Deprecated - PaymentAddressDetails */
export type PaymentAddressDetails = {
  address_city?: InputMaybe<Scalars['String']['input']>;
  address_country?: InputMaybe<Scalars['String']['input']>;
  address_line?: InputMaybe<Scalars['String']['input']>;
  address_postcode?: InputMaybe<Scalars['String']['input']>;
};

/** Updated - used to be PaymentAddressDetails */
export type PaymentAddressDetailsUpdated = {
  addressCity?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLine?: InputMaybe<Scalars['String']['input']>;
  addressPostcode?: InputMaybe<Scalars['String']['input']>;
};

export type PaymentDetails = {
  __typename?: 'PaymentDetails';
  cardDetails?: Maybe<CardDetails>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  finalPayment?: Maybe<FinalPayment>;
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  orderStarted?: Maybe<Scalars['Float']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  preAuthAmount?: Maybe<Scalars['Float']['output']>;
  preAuthRoundedAmount?: Maybe<Scalars['Float']['output']>;
  preAuthStatus?: Maybe<Scalars['Boolean']['output']>;
  refundedDate?: Maybe<Scalars['Float']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export type PaymentIntent = {
  __typename?: 'PaymentIntent';
  data?: Maybe<Scalars['String']['output']>;
};

export type PaymentMethod = {
  __typename?: 'PaymentMethod';
  /** @deprecated Returned as empty string for security */
  address1?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  address2?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  bin?: Maybe<Scalars['String']['output']>;
  cardType?: Maybe<Scalars['String']['output']>;
  cardholderName?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  city?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  country?: Maybe<Scalars['String']['output']>;
  default?: Maybe<Scalars['Boolean']['output']>;
  deleteDate?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  email?: Maybe<Scalars['String']['output']>;
  fingerprint?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  firstName?: Maybe<Scalars['String']['output']>;
  isExpired?: Maybe<Scalars['Boolean']['output']>;
  lastFour?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  lastName?: Maybe<Scalars['String']['output']>;
  month?: Maybe<Scalars['Int']['output']>;
  /** @deprecated Returned as empty string for security */
  nickname?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  paymentMethodToken?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  phoneNumber?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  postalCode?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  state?: Maybe<Scalars['String']['output']>;
  subsDefault?: Maybe<Scalars['Boolean']['output']>;
  threeDSStatus?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type PaymentRfidResponse = {
  __typename?: 'PaymentRFIDResponse';
  data?: Maybe<PaymentRfidResponseData>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type PaymentRfidResponseData = {
  __typename?: 'PaymentRFIDResponseData';
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  salesforceID?: Maybe<Scalars['String']['output']>;
};

export enum PaymentStatus {
  Captured = 'Captured',
  Contactless = 'Contactless',
  Outstanding = 'Outstanding',
  Preauthorised = 'Preauthorised',
  Processing = 'Processing'
}

export enum PaymentStatusEnum {
  ALL = 'ALL',
  CAPTURED = 'CAPTURED',
  FAILED = 'FAILED'
}

export type PaymentTokenResponse = {
  __typename?: 'PaymentTokenResponse';
  isDefault?: Maybe<Scalars['Boolean']['output']>;
  isSubsDefault?: Maybe<Scalars['Boolean']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export type Plan = {
  __typename?: 'Plan';
  billingAmount?: Maybe<BillingAmount>;
  externalPlanId?: Maybe<Scalars['String']['output']>;
  planDefault?: Maybe<Scalars['Boolean']['output']>;
  planDuration?: Maybe<Scalars['Int']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
  planid?: Maybe<Scalars['String']['output']>;
};

export type PlanResponse = {
  __typename?: 'PlanResponse';
  externalPlanId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type PlanResults = {
  __typename?: 'PlanResults';
  plans?: Maybe<Array<Maybe<Plan>>>;
};

export enum PlanType {
  BASE = 'BASE',
  OFFER = 'OFFER'
}

export enum PowerType {
  EV = 'EV',
  fuel = 'fuel'
}

export type PriceComponent = {
  __typename?: 'PriceComponent';
  price: Scalars['Float']['output'];
  stepSize: Scalars['Int']['output'];
  type: PriceComponentType;
  vat?: Maybe<Scalars['Float']['output']>;
};

export enum PriceComponentType {
  ENERGY = 'ENERGY',
  PARKING_TIME = 'PARKING_TIME'
}

export type PriceDetailsSubscription = {
  __typename?: 'PriceDetailsSubscription';
  currency?: Maybe<Scalars['String']['output']>;
  defaultFee?: Maybe<Scalars['String']['output']>;
  priceBase?: Maybe<Scalars['String']['output']>;
  priceDiscount?: Maybe<Scalars['String']['output']>;
  priceGross?: Maybe<Scalars['String']['output']>;
  priceNet?: Maybe<Scalars['String']['output']>;
  priceTaxRate?: Maybe<Scalars['String']['output']>;
  priceVAT?: Maybe<Scalars['String']['output']>;
};

export enum Provider {
  BPCM = 'BPCM',
  DCS = 'DCS',
  EVC = 'EVC',
  USOCPI = 'USOCPI',
  aral = 'aral',
  fleet = 'fleet',
  hasToBe = 'hasToBe',
  semarchy = 'semarchy'
}

export type ProviderWithDateCreated = {
  __typename?: 'ProviderWithDateCreated';
  created?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  chargepoints: Array<Chargepoint>;
  completePayment?: Maybe<Complete>;
  connectors: Array<Connector>;
  createPaymentIntent?: Maybe<PaymentIntent>;
  data: DataResult;
  favourites: QueryFavouritesResponse;
  fetchRfidDataByCardNumber?: Maybe<ExistingRfidRecordResponse>;
  fetchRfidDataByStatus?: Maybe<ExistingRfidRecordResponse>;
  getAdHocTokens: AdHocTokenResponse;
  getAllActiveTagsByProvider?: Maybe<Array<Maybe<TagRecord>>>;
  getAllOffers: QueryGetAllOffersResult;
  getAllPartnerTariffsInternal?: Maybe<Array<Maybe<PartnerTariffs>>>;
  /**
   * Returns all transactions for a given month or day.
   * Results are paginated.
   */
  getAllSubsTransactions?: Maybe<GetAllSubsTransactionsResponse>;
  getAllUpdatedTagData?: Maybe<Array<Maybe<AllUpdatedTagResponse>>>;
  getAppliedVouchers?: Maybe<Array<Maybe<Voucher>>>;
  getCustomer?: Maybe<Customer>;
  getEvPrices: Array<EvPrice>;
  /** @deprecated Use favourites query instead. */
  getFavouriteList?: Maybe<QueryFavouritesResponse>;
  getFuelPrices: Array<SiteFuelPrice>;
  getGuestEntitlement?: Maybe<GuestEntitlement>;
  getHistoryRecord?: Maybe<History_HistoryRecord>;
  getHistoryRecords?: Maybe<History_HistoryRecordsResponse>;
  getHistoryRecordsByIds?: Maybe<Array<Maybe<History_HistoryRecord>>>;
  getInvoices?: Maybe<Array<Maybe<Invoice>>>;
  getLatestHistoryRecord?: Maybe<History_HistoryRecord>;
  getLatestRequestIdentifier?: Maybe<GetLatestRequestIdentifierResponse>;
  getMarketingPreference?: Maybe<GetMarketingPreferenceResponse>;
  getOffersByUser: QueryGetOffersByUserResult;
  getPaymentMethodsWallet?: Maybe<Array<Maybe<PaymentMethod>>>;
  getPaymentRecord?: Maybe<PaymentDetails>;
  getPaymentToken?: Maybe<PaymentTokenResponse>;
  getPaymentUrl?: Maybe<Payment>;
  getPendingDeletionPaymentMethods?: Maybe<Array<Maybe<GetPendingDeletionResponse>>>;
  getPendingSubsMemberships?: Maybe<Array<Maybe<Membership>>>;
  getReceiptData?: Maybe<GetReceiptDataResponse>;
  getReceiptDetail?: Maybe<GetReceiptDetailResponse>;
  getReceiptURL?: Maybe<ReceiptUrl>;
  getSavedCards?: Maybe<SavedCards>;
  getSubsOfferPlan?: Maybe<Plan>;
  getSubsPlan?: Maybe<PlanResults>;
  getSubsWalletHistoryRecord?: Maybe<SubscriptionRecord>;
  getSubsWalletHistoryRecords?: Maybe<GetSubscriptionRecordsResponse>;
  getSubscriptionCredit?: Maybe<SubscriptionCredit>;
  getSubscriptionPreference?: Maybe<SubscriptionPreference>;
  getTransactions?: Maybe<Array<Maybe<Transaction>>>;
  getUserChargeEvent?: Maybe<ChargeEvent>;
  getUserIdFromAuthIdInternal?: Maybe<UserInternal>;
  getUserPaymentDetails?: Maybe<UserPaymentDetails>;
  getVoucherCode?: Maybe<VoucherCode>;
  getVoucherStatus?: Maybe<VoucherInfo>;
  getWalletSubscription?: Maybe<SubscriptionDetails>;
  markers: QueryMarkersResponse;
  onboardingStatus?: Maybe<OnboardingStatus>;
  operators: Array<OperatorCount>;
  /** @deprecated Deprecated - used in UK app */
  requestRFID?: Maybe<DeprecatedPaymentRfidResponse>;
  root?: Maybe<Scalars['String']['output']>;
  search: Array<SearchResultItem>;
  searchInvoices?: Maybe<Array<Maybe<History_HistoryRecord>>>;
  setupIntent?: Maybe<SetupIntent>;
  sites?: Maybe<Array<Site>>;
  tagInfo?: Maybe<Tags>;
  tariffs: QueryTariffsResult;
  test?: Maybe<TestObject>;
  userInfo?: Maybe<UserInfo>;
  validateOffer: QueryValidateOfferResult;
};


export type QueryChargepointsArgs = {
  chargepointIds: Array<Scalars['String']['input']>;
};


export type QueryCompletePaymentArgs = {
  flowId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryConnectorsArgs = {
  connectorIds: Array<Scalars['String']['input']>;
};


export type QueryCreatePaymentIntentArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  sfId?: InputMaybe<Scalars['String']['input']>;
  stripeId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDataArgs = {
  countries?: InputMaybe<Array<Country>>;
  powerTypes?: InputMaybe<Array<PowerType>>;
  providers?: InputMaybe<Array<Provider>>;
  searchAfter?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryFavouritesArgs = {
  assetType: AssetType;
  brandId: BrandId;
  userId: Scalars['String']['input'];
};


export type QueryFetchRfidDataByCardNumberArgs = {
  payload?: InputMaybe<GetRfidParamsObjectCard>;
};


export type QueryFetchRfidDataByStatusArgs = {
  payload?: InputMaybe<GetRfidParamsObjectStatus>;
};


export type QueryGetAdHocTokensArgs = {
  available?: InputMaybe<Scalars['Boolean']['input']>;
  lastEvaluatedKey?: InputMaybe<AdHocLastEvaluatedKeyInput>;
  limit?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetAllActiveTagsByProviderArgs = {
  provider?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllOffersArgs = {
  endDate: Scalars['String']['input'];
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  startDate: Scalars['String']['input'];
};


export type QueryGetAllPartnerTariffsInternalArgs = {
  partners?: InputMaybe<Array<InputMaybe<SupportedPartner>>>;
};


export type QueryGetAllSubsTransactionsArgs = {
  input?: InputMaybe<GetAllSubsTransactionsInput>;
};


export type QueryGetAllUpdatedTagDataArgs = {
  country: Scalars['String']['input'];
  tagLastUsedFrom: Scalars['String']['input'];
  tagLastUsedTo: Scalars['String']['input'];
};


export type QueryGetAppliedVouchersArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetCustomerArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  sfId?: InputMaybe<Scalars['String']['input']>;
  stripeId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetEvPricesArgs = {
  chargepointIds: Array<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetFavouriteListArgs = {
  assetType: AssetType;
  brandId: BrandId;
  userId: Scalars['String']['input'];
};


export type QueryGetFuelPricesArgs = {
  siteIds: Array<Scalars['String']['input']>;
};


export type QueryGetGuestEntitlementArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetHistoryRecordArgs = {
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  sessionId?: InputMaybe<Scalars['String']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetHistoryRecordsArgs = {
  appCountry: Scalars['String']['input'];
  endDate?: InputMaybe<Scalars['String']['input']>;
  lastKey?: InputMaybe<History_LastEvaluatedKeyInput>;
  page?: InputMaybe<Scalars['Int']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type QueryGetHistoryRecordsByIdsArgs = {
  chargeSessionIds: Array<Scalars['String']['input']>;
};


export type QueryGetInvoicesArgs = {
  from?: InputMaybe<Scalars['String']['input']>;
  to?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetLatestHistoryRecordArgs = {
  appCountry: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type QueryGetMarketingPreferenceArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  userAgent: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type QueryGetOffersByUserArgs = {
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
  offerType?: InputMaybe<Array<OfferType>>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<UserOffersOrder>;
  status?: InputMaybe<Array<OfferStatus>>;
  userId: Scalars['String']['input'];
};


export type QueryGetPaymentMethodsWalletArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetPaymentRecordArgs = {
  paymentId: Scalars['String']['input'];
};


export type QueryGetPaymentTokenArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetPaymentUrlArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPendingDeletionPaymentMethodsArgs = {
  endTime: Scalars['String']['input'];
  startTime: Scalars['String']['input'];
};


export type QueryGetPendingSubsMembershipsArgs = {
  country?: InputMaybe<SupportedCountry>;
  onDate: Array<InputMaybe<Scalars['String']['input']>>;
};


export type QueryGetReceiptDataArgs = {
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  from?: InputMaybe<Scalars['String']['input']>;
  isChargeReceipt?: InputMaybe<Scalars['Boolean']['input']>;
  subsUserCountry?: InputMaybe<Scalars['String']['input']>;
  to?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
  userType: Scalars['String']['input'];
};


export type QueryGetReceiptDetailArgs = {
  chargeSessionIds: Array<Scalars['String']['input']>;
  isChargeReceipt?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetReceiptUrlArgs = {
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  isChargeReceipt?: InputMaybe<Scalars['Boolean']['input']>;
  isGuestInvoice?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetSavedCardsArgs = {
  sfId?: InputMaybe<Scalars['String']['input']>;
  stripeId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSubsOfferPlanArgs = {
  country: Scalars['String']['input'];
  subsFilter?: InputMaybe<SubsFilterInput>;
};


export type QueryGetSubsPlanArgs = {
  externalPlanIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  userId: Scalars['String']['input'];
};


export type QueryGetSubsWalletHistoryRecordArgs = {
  subsTransactionID: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type QueryGetSubsWalletHistoryRecordsArgs = {
  lastKey?: InputMaybe<LastEvaluatedKeyInput>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  userId: Scalars['String']['input'];
};


export type QueryGetSubscriptionCreditArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetSubscriptionPreferenceArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetTransactionsArgs = {
  endDate: Scalars['String']['input'];
  paymentStatus?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  startDate?: InputMaybe<Scalars['String']['input']>;
  transactionType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserChargeEventArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserIdFromAuthIdInternalArgs = {
  authId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserPaymentDetailsArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetVoucherCodeArgs = {
  voucherType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetVoucherStatusArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
  voucherId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetWalletSubscriptionArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryMarkersArgs = {
  adsFilter?: InputMaybe<IntersectionType>;
  chargingSpeeds?: InputMaybe<Array<ChargingSpeed>>;
  connectorTypes?: InputMaybe<Array<ConnectorType>>;
  countries?: InputMaybe<Array<Country>>;
  cpo?: InputMaybe<Array<Scalars['String']['input']>>;
  entitledSchemes?: InputMaybe<Array<Scalars['Int']['input']>>;
  fuels?: InputMaybe<Array<FuelType>>;
  geoHashes: Array<Scalars['String']['input']>;
  isOpen24Hours?: InputMaybe<Scalars['Boolean']['input']>;
  maximumPrice?: InputMaybe<Scalars['Float']['input']>;
  minimumSpeed?: InputMaybe<Scalars['Int']['input']>;
  powerTypes?: InputMaybe<Array<PowerType>>;
  providers?: InputMaybe<Array<Provider>>;
  services?: InputMaybe<Array<Service>>;
  siteIds?: InputMaybe<Array<Scalars['String']['input']>>;
  uberFilter?: InputMaybe<IntersectionType>;
  zoom: Scalars['Float']['input'];
};


export type QueryOnboardingStatusArgs = {
  userId: Scalars['String']['input'];
};


export type QueryOperatorsArgs = {
  countries?: InputMaybe<Array<Country>>;
};


export type QueryRequestRfidArgs = {
  addressDetails?: InputMaybe<PaymentAddressDetails>;
  cardPreference?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySearchArgs = {
  countries?: InputMaybe<Array<Country>>;
  input: Scalars['String']['input'];
  location?: InputMaybe<GeoPointInput>;
  providers?: InputMaybe<Array<Provider>>;
};


export type QuerySearchInvoicesArgs = {
  cardDigits?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  endDate: Scalars['String']['input'];
  grossAmount: Scalars['String']['input'];
};


export type QuerySetupIntentArgs = {
  sfId?: InputMaybe<Scalars['String']['input']>;
  stripeId?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySitesArgs = {
  adsFilter?: InputMaybe<IntersectionType>;
  chargingSpeeds?: InputMaybe<Array<ChargingSpeed>>;
  connectorTypes?: InputMaybe<Array<ConnectorType>>;
  countries?: InputMaybe<Array<Country>>;
  cpo?: InputMaybe<Array<Scalars['String']['input']>>;
  entitledSchemes?: InputMaybe<Array<Scalars['Int']['input']>>;
  from?: InputMaybe<Scalars['Int']['input']>;
  fuels?: InputMaybe<Array<FuelType>>;
  geoHashes?: InputMaybe<Array<Scalars['String']['input']>>;
  isOpen24Hours?: InputMaybe<Scalars['Boolean']['input']>;
  maximumPrice?: InputMaybe<Scalars['Float']['input']>;
  minimumSpeed?: InputMaybe<Scalars['Int']['input']>;
  powerTypes?: InputMaybe<Array<PowerType>>;
  providers?: InputMaybe<Array<Provider>>;
  relativeGeoPoint?: InputMaybe<GeoPointInput>;
  services?: InputMaybe<Array<Service>>;
  siteIds?: InputMaybe<Array<Scalars['String']['input']>>;
  size?: InputMaybe<Scalars['Int']['input']>;
  uberFilter?: InputMaybe<IntersectionType>;
};


export type QueryTagInfoArgs = {
  country: Scalars['String']['input'];
  tagCardNumber: Scalars['String']['input'];
  tagSerialNumber?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTariffsArgs = {
  country: TariffCountry;
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  tariffType: EmspTariffType;
};


export type QueryUserInfoArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type QueryValidateOfferArgs = {
  offerCode: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFavouritesResponse = {
  __typename?: 'QueryFavouritesResponse';
  favourites: Array<Favourite>;
  userId: Scalars['String']['output'];
};

export type QueryGetAllOffersResult = {
  __typename?: 'QueryGetAllOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  lastEvaluatedKey?: Maybe<Scalars['String']['output']>;
  offers?: Maybe<Array<Offer>>;
};

export type QueryGetOffersByUserResult = {
  __typename?: 'QueryGetOffersByUserResult';
  error?: Maybe<Scalars['String']['output']>;
  lastEvaluatedKey?: Maybe<Scalars['String']['output']>;
  maxOfferQuantity?: Maybe<Scalars['Int']['output']>;
  maxOfferQuantityExceeded?: Maybe<Scalars['Boolean']['output']>;
  offers?: Maybe<Array<Offer>>;
  trialOffer?: Maybe<TrialOffer>;
};

export type QueryMarkersResponse = {
  __typename?: 'QueryMarkersResponse';
  args: QueryMarkersResponseArgs;
  markers: Array<Marker>;
};

export type QueryMarkersResponseArgs = {
  __typename?: 'QueryMarkersResponseArgs';
  adsFilter?: Maybe<IntersectionType>;
  chargingSpeeds?: Maybe<Array<ChargingSpeed>>;
  connectorTypes?: Maybe<Array<ConnectorType>>;
  countries?: Maybe<Array<Country>>;
  cpo?: Maybe<Array<Scalars['String']['output']>>;
  entitledSchemes?: Maybe<Array<Scalars['Int']['output']>>;
  fuels?: Maybe<Array<FuelType>>;
  geoHashes: Array<Scalars['String']['output']>;
  isOpen24Hours?: Maybe<Scalars['Boolean']['output']>;
  maximumPrice?: Maybe<Scalars['Float']['output']>;
  minimumSpeed?: Maybe<Scalars['Int']['output']>;
  powerTypes?: Maybe<Array<PowerType>>;
  providers?: Maybe<Array<Provider>>;
  /** @deprecated No longer supported */
  royalMailFilter?: Maybe<IntersectionType>;
  services?: Maybe<Array<Service>>;
  siteIds?: Maybe<Array<Scalars['String']['output']>>;
  uberFilter?: Maybe<IntersectionType>;
  zoom: Scalars['Float']['output'];
};

export type QueryTariffsResult = {
  __typename?: 'QueryTariffsResult';
  error?: Maybe<Scalars['String']['output']>;
  lastEvaluatedKey?: Maybe<Scalars['String']['output']>;
  tariffs?: Maybe<Array<Tariff>>;
};

export type QueryValidateOfferResult = {
  __typename?: 'QueryValidateOfferResult';
  contradictoryOffer?: Maybe<Offer>;
  error?: Maybe<Scalars['String']['output']>;
  isValid?: Maybe<Scalars['Boolean']['output']>;
  offer?: Maybe<Offer>;
  reason?: Maybe<OfferInvalidReason>;
};

export type RfidResponse = {
  __typename?: 'RFIDResponse';
  data?: Maybe<RfidResponseData>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type RfidResponseData = {
  __typename?: 'RFIDResponseData';
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  salesforceID?: Maybe<Scalars['String']['output']>;
};

export type ReceiptData = {
  __typename?: 'ReceiptData';
  address?: Maybe<Scalars['String']['output']>;
  blockingFee?: Maybe<Receipt_BlockingFee>;
  cardChargeAmount?: Maybe<Scalars['String']['output']>;
  cardDetails?: Maybe<Receipt_CardDetails>;
  cdrType?: Maybe<Scalars['String']['output']>;
  chargeAdditionalFees?: Maybe<Scalars['String']['output']>;
  chargeBaseFee?: Maybe<Scalars['String']['output']>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  chargeStarted?: Maybe<Scalars['Boolean']['output']>;
  chargepoint?: Maybe<Receipt_Chargepoint>;
  connector?: Maybe<Receipt_Connector>;
  cost?: Maybe<Receipt_Cost>;
  countryTaxCode?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  dateEnd?: Maybe<Scalars['String']['output']>;
  dateStart?: Maybe<Scalars['String']['output']>;
  discount?: Maybe<Scalars['Float']['output']>;
  energyConsumption?: Maybe<Scalars['Float']['output']>;
  finalPayment?: Maybe<Receipt_FinalPayment>;
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  fullAddress?: Maybe<Scalars['String']['output']>;
  grossAmount?: Maybe<Scalars['String']['output']>;
  grossUnitCost?: Maybe<Scalars['Float']['output']>;
  invoices?: Maybe<Array<Maybe<Invoice>>>;
  isStandardInvoice?: Maybe<Scalars['Boolean']['output']>;
  orderStarted?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  paymentType?: Maybe<Scalars['String']['output']>;
  preAuthAmount?: Maybe<Scalars['Int']['output']>;
  preAuthStatus?: Maybe<Scalars['Boolean']['output']>;
  prefix?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  publicName?: Maybe<Scalars['String']['output']>;
  receiptId?: Maybe<Scalars['String']['output']>;
  referenceChargeSessionId?: Maybe<Scalars['String']['output']>;
  refundedDate?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export type ReceiptDetail = {
  __typename?: 'ReceiptDetail';
  acquirer?: Maybe<Scalars['String']['output']>;
  charge_session_id?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  receipt_id?: Maybe<Scalars['String']['output']>;
  receipt_number?: Maybe<Scalars['String']['output']>;
  s3_file_key?: Maybe<Scalars['String']['output']>;
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type ReceiptUrl = {
  __typename?: 'ReceiptURL';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type Receipt_BlockingFee = {
  __typename?: 'Receipt_BlockingFee';
  duration?: Maybe<Scalars['Float']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
};

export type Receipt_CardDetails = {
  __typename?: 'Receipt_CardDetails';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
  cardUuid?: Maybe<Scalars['String']['output']>;
  fundingMethod?: Maybe<Scalars['String']['output']>;
};

export type Receipt_Chargepoint = {
  __typename?: 'Receipt_Chargepoint';
  apolloExternalId?: Maybe<Scalars['String']['output']>;
  providerExternalId?: Maybe<Scalars['String']['output']>;
};

export type Receipt_Connector = {
  __typename?: 'Receipt_Connector';
  connectorExternalId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type Receipt_Cost = {
  __typename?: 'Receipt_Cost';
  currency?: Maybe<Scalars['String']['output']>;
  tax?: Maybe<Scalars['String']['output']>;
  taxDeducted?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['String']['output']>;
};

export type Receipt_FinalPayment = {
  __typename?: 'Receipt_FinalPayment';
  amount?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  roundedAmount?: Maybe<Scalars['String']['output']>;
};

export type RefundOrderResponse = {
  __typename?: 'RefundOrderResponse';
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type RegistrationResponse = {
  __typename?: 'RegistrationResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

/** Release ad hoc token response */
export type ReleaseAdHocToken = {
  __typename?: 'ReleaseAdHocToken';
  statusCode: Scalars['Int']['output'];
  statusMessage: Scalars['String']['output'];
};

export type RemovePartnerDriverStatusResponse = {
  __typename?: 'RemovePartnerDriverStatusResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type RequestRfidRequest = {
  address: PaymentAddressDetailsUpdated;
  cardPreference?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type RevenuePlanInput = {
  provider?: InputMaybe<Scalars['String']['input']>;
  revenuePlanName?: InputMaybe<Scalars['String']['input']>;
};

export type RevenuePlans = {
  __typename?: 'RevenuePlans';
  provider?: Maybe<Scalars['String']['output']>;
  revenuePlanDescription?: Maybe<Scalars['String']['output']>;
  revenuePlanName?: Maybe<Scalars['String']['output']>;
};

export type SaveReceiptResponse = {
  __typename?: 'SaveReceiptResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type SavedCard = {
  __typename?: 'SavedCard';
  card?: Maybe<Card>;
  id?: Maybe<Scalars['String']['output']>;
};

export type SavedCards = {
  __typename?: 'SavedCards';
  data?: Maybe<Array<Maybe<SavedCard>>>;
};

export type Schemes = {
  __typename?: 'Schemes';
  schemeId?: Maybe<Scalars['Float']['output']>;
  schemeName?: Maybe<Scalars['String']['output']>;
};

export type SearchResult = Chargepoint | Connector;

export type SearchResultItem = {
  __typename?: 'SearchResultItem';
  distance?: Maybe<Scalars['Float']['output']>;
  result: SearchResult;
};

export enum SearchResultType {
  Chargepoint = 'Chargepoint',
  Connector = 'Connector'
}

export enum Service {
  ADS = 'ADS',
  AD_BLUE = 'AD_BLUE',
  ARAL_FUEL_CARD = 'ARAL_FUEL_CARD',
  ARAL_STORE = 'ARAL_STORE',
  ARAL_ULTIMATE = 'ARAL_ULTIMATE',
  ATM_MACHINE = 'ATM_MACHINE',
  BP_ME = 'BP_ME',
  CABRIO_CARE = 'CABRIO_CARE',
  CAR_COURT = 'CAR_COURT',
  CAR_RENTAL = 'CAR_RENTAL',
  CAR_WASH_PLANT = 'CAR_WASH_PLANT',
  EV = 'EV',
  LPG = 'LPG',
  NATURAL_GAS = 'NATURAL_GAS',
  NEAR_MOTORWAY = 'NEAR_MOTORWAY',
  PAYBACK = 'PAYBACK',
  PAYBACK_FUEL_AND_GO = 'PAYBACK_FUEL_AND_GO',
  PETIT_BISTRO = 'PETIT_BISTRO',
  RECUP = 'RECUP',
  RESTAURANT = 'RESTAURANT',
  REWE_TO_GO = 'REWE_TO_GO',
  SUPER_BOX = 'SUPER_BOX',
  SUPER_WASH = 'SUPER_WASH',
  SUV_WASH = 'SUV_WASH',
  TOLL_STATION = 'TOLL_STATION',
  TOO_GOOD_TO_GO = 'TOO_GOOD_TO_GO',
  TRUCK_DIESEL = 'TRUCK_DIESEL',
  TRUCK_WASH = 'TRUCK_WASH',
  TWENTY_FOUR_HOURS = 'TWENTY_FOUR_HOURS',
  VDA = 'VDA',
  WASH = 'WASH'
}

export type Session = {
  __typename?: 'Session';
  id?: Maybe<Scalars['String']['output']>;
  updateStatus?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['String']['output']>;
};

export enum SessionTokenType {
  AD_HOC_USER = 'AD_HOC_USER'
}

export enum SessionType {
  Contactless = 'Contactless',
  Mobile = 'Mobile',
  RFID = 'RFID',
  Webshop = 'Webshop'
}

export type SetupIntent = {
  __typename?: 'SetupIntent';
  data?: Maybe<Scalars['String']['output']>;
};

export type SimplePayment = {
  __typename?: 'SimplePayment';
  /**
   * An optional argument used to identify the chargeSession payment ID,
   * which can be different to the payment ID sent to dPaaS
   */
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  correlationId?: Maybe<Scalars['String']['output']>;
  /** This is the payment ID sent to dPaaS to create payment orders on their platform */
  paymentId?: Maybe<Scalars['String']['output']>;
  rrn?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Site = {
  __typename?: 'Site';
  brandName?: Maybe<CrossAcceptanceBrand>;
  chargepoints?: Maybe<Array<Chargepoint>>;
  cpo?: Maybe<Scalars['String']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  distanceToRelativeGeoPoint?: Maybe<Scalars['Float']['output']>;
  evAvailability?: Maybe<EvAvailability>;
  fuelPrices?: Maybe<Array<FuelPrice>>;
  fuels: Array<FuelType>;
  hasEvCharging: Scalars['Boolean']['output'];
  hasFuel: Scalars['Boolean']['output'];
  hidden?: Maybe<Scalars['Boolean']['output']>;
  isAds: Scalars['Boolean']['output'];
  isOpen24Hours?: Maybe<Scalars['Boolean']['output']>;
  lastUpdated: Scalars['String']['output'];
  provider: Provider;
  sapId?: Maybe<Scalars['String']['output']>;
  services: Array<Service>;
  siteDetails: SiteDetails;
  siteId: Scalars['String']['output'];
};

export type SiteDetails = {
  __typename?: 'SiteDetails';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  country: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  fax?: Maybe<Scalars['String']['output']>;
  geohash: Scalars['String']['output'];
  hotline?: Maybe<Scalars['String']['output']>;
  hours: Hours;
  location: GeoPoint;
  phone?: Maybe<Scalars['String']['output']>;
  postcode?: Maybe<Scalars['String']['output']>;
  siteName?: Maybe<Scalars['String']['output']>;
  siteProviderIds: Array<SiteProviderId>;
};

export type SiteFuelPrice = {
  __typename?: 'SiteFuelPrice';
  error?: Maybe<Scalars['String']['output']>;
  fuels: Array<FuelPrice>;
  siteId: Scalars['String']['output'];
};

export type SiteProviderId = {
  __typename?: 'SiteProviderId';
  id: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type StartSessionInputArgs = {
  evseId: Scalars['String']['input'];
  locationId: Scalars['StringOrNumber']['input'];
  ocpiIdentifier: Scalars['String']['input'];
  token: StartSessionToken;
};

export type StartSessionResponse = {
  __typename?: 'StartSessionResponse';
  authId: Scalars['String']['output'];
  commandId: Scalars['String']['output'];
  result: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

/** start session token */
export type StartSessionToken = {
  authId?: InputMaybe<Scalars['String']['input']>;
  lastUpdated?: InputMaybe<Scalars['DateTimeISO']['input']>;
  type?: InputMaybe<SessionTokenType>;
  uid?: InputMaybe<Scalars['String']['input']>;
};

export type StopSessionInputArgs = {
  ocpiIdentifier: Scalars['String']['input'];
  sessionId: Scalars['String']['input'];
  token: StopSessionToken;
};

export type StopSessionResponse = {
  __typename?: 'StopSessionResponse';
  commandId: Scalars['String']['output'];
  result: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

/** stop session token */
export type StopSessionToken = {
  uid: Scalars['String']['input'];
};

export type StoreOperationIdResponse = {
  __typename?: 'StoreOperationIdResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type StorePaymentTokenResponse = {
  __typename?: 'StorePaymentTokenResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type SubsCardDetails = {
  card_number?: InputMaybe<Scalars['String']['input']>;
  card_scheme?: InputMaybe<Scalars['String']['input']>;
};

export type SubsFilterInput = {
  subsDiscount: Scalars['Float']['input'];
  subsDuration: Scalars['Int']['input'];
};

export type SubsHistoryRecord = {
  cardDetails?: InputMaybe<SubsCardDetails>;
  country?: InputMaybe<Scalars['String']['input']>;
  nextBillingCycleDate?: InputMaybe<Scalars['String']['input']>;
  partnerType?: InputMaybe<Scalars['String']['input']>;
  priceDetails?: InputMaybe<SubsPriceDetails>;
  subscriptionExternalId?: InputMaybe<Scalars['String']['input']>;
  transactionDate?: InputMaybe<Scalars['String']['input']>;
};

export type SubsPaymentResponse = {
  __typename?: 'SubsPaymentResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  cancelledOn?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type SubsPlan = {
  billingAmount: Scalars['Float']['input'];
  currency: Scalars['String']['input'];
  default?: InputMaybe<Scalars['Boolean']['input']>;
  description: Scalars['String']['input'];
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName: Scalars['String']['input'];
  planType?: InputMaybe<PlanType>;
  scheme_id?: InputMaybe<Scalars['Int']['input']>;
};

export type SubsPlansResponse = {
  __typename?: 'SubsPlansResponse';
  error?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
};

export type SubsPriceDetails = {
  currency?: InputMaybe<Scalars['String']['input']>;
  price_VAT?: InputMaybe<Scalars['String']['input']>;
  price_base?: InputMaybe<Scalars['String']['input']>;
  price_discount?: InputMaybe<Scalars['String']['input']>;
  price_full_gross?: InputMaybe<Scalars['String']['input']>;
  price_gross?: InputMaybe<Scalars['String']['input']>;
  price_net?: InputMaybe<Scalars['String']['input']>;
  price_tax?: InputMaybe<Scalars['String']['input']>;
};

export type SubscriptionCredit = {
  __typename?: 'SubscriptionCredit';
  payload?: Maybe<SubscriptionCreditPayload>;
  status?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionCreditData = {
  __typename?: 'SubscriptionCreditData';
  currency?: Maybe<Scalars['String']['output']>;
  invoiceCredit?: Maybe<Scalars['Float']['output']>;
  tagPlanCredit?: Maybe<Scalars['Float']['output']>;
  tagPlanCreditTotal?: Maybe<Scalars['Float']['output']>;
};

export type SubscriptionCreditPayload = {
  __typename?: 'SubscriptionCreditPayload';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<SubscriptionCreditData>;
  error?: Maybe<Scalars['Boolean']['output']>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionData = {
  __typename?: 'SubscriptionData';
  /** @deprecated No longer supported */
  addressCity?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported */
  addressCountry?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported */
  addressLine?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported */
  addressPostcode?: Maybe<Scalars['String']['output']>;
  cardPreference?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  toBeCancelled?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionDetails = {
  __typename?: 'SubscriptionDetails';
  accountId?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  billingItems?: Maybe<Array<Maybe<Subscription_BillingItems>>>;
  cancelledOn?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  deactivationDate?: Maybe<Scalars['String']['output']>;
  discountExpiresAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  merchantId?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionPlan = {
  __typename?: 'SubscriptionPlan';
  externalPlanId?: Maybe<Scalars['String']['output']>;
  subsPlanCost?: Maybe<Scalars['String']['output']>;
  subsPlanCreatedDate?: Maybe<Scalars['String']['output']>;
  subsPlanCurrency?: Maybe<Scalars['String']['output']>;
  subsPlanDefault?: Maybe<Scalars['Boolean']['output']>;
  subsPlanDeletionDate?: Maybe<Scalars['String']['output']>;
  subsPlanDescription?: Maybe<Scalars['String']['output']>;
  subsPlanDuration?: Maybe<Scalars['Int']['output']>;
  subsPlanName?: Maybe<Scalars['String']['output']>;
  subsPlanUpdatedDate?: Maybe<Scalars['String']['output']>;
  subscriptionPlanId?: Maybe<Scalars['Int']['output']>;
};

export type SubscriptionPreference = {
  __typename?: 'SubscriptionPreference';
  payload?: Maybe<SubscriptionPreferencePayload>;
  status?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionPreferencePayload = {
  __typename?: 'SubscriptionPreferencePayload';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<SubscriptionData>;
  error?: Maybe<Scalars['Boolean']['output']>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionRecord = {
  __typename?: 'SubscriptionRecord';
  cardDetails?: Maybe<CardDetailsSubscripton>;
  country?: Maybe<Scalars['String']['output']>;
  nextBillingCycleDate?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  paymentStatus?: Maybe<PaymentStatusEnum>;
  priceDetails?: Maybe<PriceDetailsSubscription>;
  subsAccountId?: Maybe<Scalars['String']['output']>;
  transactionDate?: Maybe<Scalars['String']['output']>;
  transactionDay?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionMonth?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Subscription_BillingItems = {
  __typename?: 'Subscription_BillingItems';
  amount?: Maybe<Scalars['String']['output']>;
  billingCycleCount?: Maybe<Scalars['Int']['output']>;
  billingItemId?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
};

export enum SupportedCountry {
  AU = 'AU',
  DE = 'DE',
  ES = 'ES',
  NL = 'NL',
  NZ = 'NZ',
  UK = 'UK',
  UK_Beta = 'UK_Beta',
  US = 'US'
}

export enum SupportedPartner {
  ADAC = 'ADAC',
  Uber = 'Uber',
  Uber_EVC = 'Uber_EVC'
}

export enum SupportedUserScheme {
  ADAC_DE_SCHEME = 'ADAC_DE_SCHEME',
  DEFAULT_DE_EUROPEAN_SCHEME = 'DEFAULT_DE_EUROPEAN_SCHEME',
  DEFAULT_ES_EUROPEAN_SCHEME = 'DEFAULT_ES_EUROPEAN_SCHEME',
  DEFAULT_ES_SCHEME = 'DEFAULT_ES_SCHEME',
  DEFAULT_NL_EUROPEAN_SCHEME = 'DEFAULT_NL_EUROPEAN_SCHEME',
  DE_FREE_SUBSCRIPTION = 'DE_FREE_SUBSCRIPTION',
  NL_FREE_SUBSCRIPTION = 'NL_FREE_SUBSCRIPTION',
  UBER_PRO_NL_BLUE_TIER = 'UBER_PRO_NL_BLUE_TIER',
  UBER_PRO_NL_DIAMOND_TIER = 'UBER_PRO_NL_DIAMOND_TIER',
  UBER_PRO_NL_GOLD_TIER = 'UBER_PRO_NL_GOLD_TIER',
  UBER_PRO_NL_PLATINUM_TIER = 'UBER_PRO_NL_PLATINUM_TIER',
  UK_DEFAULT_SCHEME = 'UK_DEFAULT_SCHEME',
  UK_SUBSCRIPTION_SCHEME = 'UK_SUBSCRIPTION_SCHEME'
}

export type TagRecord = {
  __typename?: 'TagRecord';
  engineerTagFlag?: Maybe<Scalars['String']['output']>;
  tagBarredDateTime?: Maybe<Scalars['String']['output']>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCategoryId?: Maybe<Scalars['Float']['output']>;
  tagCreatedDateTime?: Maybe<Scalars['String']['output']>;
  tagDeletedDateTime?: Maybe<Scalars['String']['output']>;
  tagDeletedFlag?: Maybe<Scalars['String']['output']>;
  tagExpiresDateTime?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  tagLastUsedDateTime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagSerialNumber?: Maybe<Scalars['String']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  tagTypeId?: Maybe<Scalars['Float']['output']>;
  tagVerifiedFlag?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Tags = {
  __typename?: 'Tags';
  country?: Maybe<Scalars['String']['output']>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCategoryName?: Maybe<Scalars['String']['output']>;
  tagCreatedDateTime?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  tagLastUsedDateTime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagPartner?: Maybe<Scalars['Boolean']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  tagTypeName?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Tariff = {
  __typename?: 'Tariff';
  country: TariffCountry;
  countryType: Scalars['String']['output'];
  currency: TariffCurrency;
  elements: Array<TariffElement>;
  lastUpdated: Scalars['String']['output'];
  tariffId: Scalars['String']['output'];
  tariffType: EmspTariffType;
  validFrom: Scalars['String']['output'];
  validUntil?: Maybe<Scalars['String']['output']>;
  versionTimestamp: Scalars['String']['output'];
};

export enum TariffCountry {
  UK = 'UK'
}

export enum TariffCurrency {
  GBP = 'GBP'
}

export type TariffElement = {
  __typename?: 'TariffElement';
  priceComponents: Array<PriceComponent>;
  restrictions: TariffRestriction;
};

export type TariffElementInput = {
  price: Scalars['Float']['input'];
  restrictions?: InputMaybe<TariffRestrictionInput>;
  stepSize: Scalars['Int']['input'];
  type: PriceComponentType;
  vat?: InputMaybe<Scalars['Float']['input']>;
};

export type TariffRestriction = {
  __typename?: 'TariffRestriction';
  maxDuration?: Maybe<Scalars['Int']['output']>;
  maxPower?: Maybe<Scalars['Int']['output']>;
  minDuration?: Maybe<Scalars['Int']['output']>;
  minPower?: Maybe<Scalars['Int']['output']>;
};

export type TariffRestrictionInput = {
  maxDuration?: InputMaybe<Scalars['Int']['input']>;
  maxPower?: InputMaybe<Scalars['Int']['input']>;
  minDuration?: InputMaybe<Scalars['Int']['input']>;
  minPower?: InputMaybe<Scalars['Int']['input']>;
};

export enum TariffType {
  GUEST = 'GUEST',
  PAYG = 'PAYG',
  SUBSCRIBER = 'SUBSCRIBER'
}

export type TestObject = {
  __typename?: 'TestObject';
  test: Scalars['String']['output'];
};

export type ThreeDs = {
  acsTransactionId?: InputMaybe<Scalars['String']['input']>;
  cavv?: InputMaybe<Scalars['String']['input']>;
  dsTransactionId?: InputMaybe<Scalars['String']['input']>;
  eciFlag?: InputMaybe<Scalars['String']['input']>;
  enrolled?: InputMaybe<Scalars['String']['input']>;
  paresStatus?: InputMaybe<Scalars['String']['input']>;
  statusReason?: InputMaybe<Scalars['String']['input']>;
  threeDSServerTransactionId?: InputMaybe<Scalars['String']['input']>;
  threeDSVersion?: InputMaybe<Scalars['String']['input']>;
};

export type Token = {
  __typename?: 'Token';
  expiryDate?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tokenType?: Maybe<Scalars['String']['output']>;
};

export enum TokenStatus {
  INVALID = 'INVALID',
  REAUTH = 'REAUTH',
  VALID = 'VALID'
}

export type Transaction = {
  __typename?: 'Transaction';
  card_number?: Maybe<Scalars['String']['output']>;
  card_scheme?: Maybe<Scalars['String']['output']>;
  charge_session_id?: Maybe<Scalars['String']['output']>;
  correlation_id?: Maybe<Scalars['String']['output']>;
  final_payment?: Maybe<FinalPayment>;
  final_payment_status?: Maybe<Scalars['String']['output']>;
  funding_method?: Maybe<Scalars['String']['output']>;
  journey_id?: Maybe<Scalars['String']['output']>;
  merchant_id?: Maybe<Scalars['String']['output']>;
  operationId?: Maybe<Scalars['String']['output']>;
  order_started?: Maybe<Scalars['String']['output']>;
  order_status?: Maybe<Scalars['String']['output']>;
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_method_type?: Maybe<Scalars['String']['output']>;
  preauth?: Maybe<Scalars['Boolean']['output']>;
  reference_charge_session_id?: Maybe<Scalars['String']['output']>;
  refunded_date?: Maybe<Scalars['Float']['output']>;
  retrieval_reference_number?: Maybe<Scalars['String']['output']>;
  session_id?: Maybe<Scalars['String']['output']>;
  transaction_id?: Maybe<Scalars['String']['output']>;
  transaction_number?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type TransactionDataItem = {
  __typename?: 'TransactionDataItem';
  CP_address?: Maybe<Scalars['String']['output']>;
  CP_serial?: Maybe<Scalars['String']['output']>;
  charge_energy_used?: Maybe<Scalars['Float']['output']>;
  charge_id?: Maybe<Scalars['String']['output']>;
  charge_time_end?: Maybe<Scalars['String']['output']>;
  charge_time_start?: Maybe<Scalars['String']['output']>;
  connector_type?: Maybe<Scalars['String']['output']>;
  overstayTotal?: Maybe<Scalars['Float']['output']>;
  transactionTotal?: Maybe<Scalars['Float']['output']>;
  transaction_item_unit_cost?: Maybe<Scalars['Float']['output']>;
};

export type TrialOffer = {
  __typename?: 'TrialOffer';
  planDuration?: Maybe<Scalars['Int']['output']>;
  trialBillingDate?: Maybe<Scalars['String']['output']>;
  trialRedemptionDate?: Maybe<Scalars['String']['output']>;
  trialStatus: OfferStatus;
};

export type UberTier = {
  __typename?: 'UberTier';
  display_name?: Maybe<Scalars['String']['output']>;
  tier?: Maybe<Scalars['String']['output']>;
};

export type UnblockPartnerTokenResponse = {
  __typename?: 'UnblockPartnerTokenResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type UnblockRfid = {
  cardNumber: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  country: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type UnblockRfidResponse = {
  __typename?: 'UnblockRfidResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateInternalResponse = {
  __typename?: 'UpdateInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateMarketingPreferenceResponse = {
  __typename?: 'UpdateMarketingPreferenceResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateRfidInternalPayload = {
  country?: InputMaybe<Scalars['String']['input']>;
  revenuePlan?: InputMaybe<Array<InputMaybe<RevenuePlanInput>>>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateRfidInternalResponse = {
  __typename?: 'UpdateRFIDInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateRfidRecordResponse = {
  __typename?: 'UpdateRFIDRecordResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateRevenuePlanInternalInput = {
  chargeTypeId?: InputMaybe<Scalars['String']['input']>;
  revenuePlanDeletedDate?: InputMaybe<Scalars['String']['input']>;
  revenuePlanDescription?: InputMaybe<Scalars['String']['input']>;
  revenuePlanEffectiveDate?: InputMaybe<Scalars['String']['input']>;
  revenuePlanEnergyCharge?: InputMaybe<Scalars['Float']['input']>;
  revenuePlanFreeMonths?: InputMaybe<Scalars['Float']['input']>;
  revenuePlanId: Scalars['String']['input'];
  revenuePlanName?: InputMaybe<Scalars['String']['input']>;
  revenuePlanTimeCharge?: InputMaybe<Scalars['Float']['input']>;
  revenuePlanUpdatedDate?: InputMaybe<Scalars['String']['input']>;
  revenueRateCost?: InputMaybe<Scalars['Float']['input']>;
  schemeId: Scalars['String']['input'];
};

export type UpdateRevenuePlanInternalResponse = {
  __typename?: 'UpdateRevenuePlanInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateRfidParamsObject = {
  cardNumber: Scalars['String']['input'];
  requestIdentifier?: InputMaybe<Scalars['String']['input']>;
  status: Scalars['String']['input'];
};

export type UpdateSubsPlanInternalResponse = {
  __typename?: 'UpdateSubsPlanInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateSubsPlanPayload = {
  billingAmount: Scalars['Float']['input'];
  currency: Scalars['String']['input'];
  default: Scalars['Boolean']['input'];
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName: Scalars['String']['input'];
  subscriptionPlanId: Scalars['String']['input'];
};

export type UpdateSubscriptionDetails = {
  __typename?: 'UpdateSubscriptionDetails';
  accountId?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  billingItems?: Maybe<Array<Maybe<UpdateSubscription_BillingItems>>>;
  createdAt?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  deactivationDate?: Maybe<Scalars['String']['output']>;
  discountExpiresAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  merchantId?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type UpdateSubscription_BillingItems = {
  __typename?: 'UpdateSubscription_BillingItems';
  billingItemId?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
};

export type UpdateTagInternalInput = {
  country: Scalars['String']['input'];
  salesforceId: Scalars['String']['input'];
  tagBarredDatetime?: InputMaybe<Scalars['String']['input']>;
  tagCardNumber: Scalars['String']['input'];
  tagCategoryName?: InputMaybe<Scalars['String']['input']>;
  tagDeletedFlag?: InputMaybe<Scalars['String']['input']>;
  tagExpiresDatetime?: InputMaybe<Scalars['String']['input']>;
  tagLastUsedDatetime?: InputMaybe<Scalars['String']['input']>;
  tagNotes?: InputMaybe<Scalars['String']['input']>;
  tagPartner?: InputMaybe<Scalars['Boolean']['input']>;
  tagSerialNumber?: InputMaybe<Scalars['String']['input']>;
  tagStatus?: InputMaybe<Scalars['String']['input']>;
  tagTypeName?: InputMaybe<Scalars['String']['input']>;
};

/** Update token response */
export type UpdateToken = {
  __typename?: 'UpdateToken';
  statusCode: Scalars['Int']['output'];
  statusMessage: Scalars['String']['output'];
  timestamp: Scalars['DateTimeISO']['output'];
};

export type UpdateTokenInput = {
  ocpiIdentifier: Scalars['String']['input'];
  uid: Scalars['String']['input'];
  valid: Scalars['Boolean']['input'];
};

export type UpdateUserInternalInput = {
  country: Scalars['String']['input'];
  userBalance?: InputMaybe<Scalars['Float']['input']>;
  userBalanceCurrency?: InputMaybe<Scalars['String']['input']>;
  userCancelledDateTime?: InputMaybe<Scalars['String']['input']>;
  userCancelledReason?: InputMaybe<Scalars['String']['input']>;
  userDeletedDateTime?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
  userNotes?: InputMaybe<Scalars['String']['input']>;
  userStatus?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserSchemeInternalResponse = {
  __typename?: 'UpdateUserSchemeInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdatedSubsPlanResponse = {
  __typename?: 'UpdatedSubsPlanResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  billingAmount?: Maybe<BillingAmount>;
  billingCycle?: Maybe<BillingCycle>;
  billingCycleCount?: Maybe<Scalars['Int']['output']>;
  billingFrequency?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
  planType?: Maybe<PlanType>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpsertPartnerTokenResponse = {
  __typename?: 'UpsertPartnerTokenResponse';
  message?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<UberTier>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpsertWalletSubscriptionResponse = {
  __typename?: 'UpsertWalletSubscriptionResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  billingItems?: Maybe<Array<Maybe<Subscription_BillingItems>>>;
  createdAt?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  deactivationDate?: Maybe<Scalars['String']['output']>;
  discountExpiresAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  merchantId?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  subscriptionId?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type User = {
  __typename?: 'User';
  addressId?: Maybe<Scalars['String']['output']>;
  authenticationId?: Maybe<Scalars['String']['output']>;
  balance?: Maybe<Scalars['String']['output']>;
  balanceCurrency?: Maybe<Scalars['String']['output']>;
  cancelledDatetime?: Maybe<Scalars['String']['output']>;
  cancelledReason?: Maybe<Scalars['String']['output']>;
  cardPreference?: Maybe<Scalars['String']['output']>;
  createdDatetime?: Maybe<Scalars['String']['output']>;
  deletedDatetime?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  language?: Maybe<Scalars['String']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  originTypeId?: Maybe<Scalars['Int']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  roamingEnabled?: Maybe<Scalars['Boolean']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  typeId?: Maybe<Scalars['String']['output']>;
};

export type UserInfo = {
  __typename?: 'UserInfo';
  balance?: Maybe<Scalars['Float']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  entitlements?: Maybe<Entitlements>;
  gocardless?: Maybe<GoCardless>;
  membership?: Maybe<Array<Maybe<Membership>>>;
  message?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  /** @deprecated replaced by revenuePlans */
  revenuePlanNames?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  revenuePlans?: Maybe<Array<Maybe<RevenuePlans>>>;
  roamingEnabled?: Maybe<Scalars['Boolean']['output']>;
  schemes?: Maybe<Array<Maybe<Schemes>>>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionPlan?: Maybe<SubscriptionPlan>;
  tagIds?: Maybe<Array<Maybe<Tags>>>;
  tokens?: Maybe<Array<Maybe<Token>>>;
  type?: Maybe<Scalars['String']['output']>;
  userCancelledDateTime?: Maybe<Scalars['String']['output']>;
  userStatus?: Maybe<Scalars['String']['output']>;
};

export type UserInternal = {
  __typename?: 'UserInternal';
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export enum UserOffersOrder {
  EXPIRY_DATE = 'EXPIRY_DATE',
  UPDATE_DATE = 'UPDATE_DATE'
}

export type UserPaymentDetails = {
  __typename?: 'UserPaymentDetails';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  chargeStarted?: Maybe<Scalars['Boolean']['output']>;
  finalPayment?: Maybe<FinalPayment>;
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  fundingMethod?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  /** @deprecated Use paymentId instead */
  orderId?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  preAuthAmount?: Maybe<Scalars['Float']['output']>;
  preAuthStatus?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
  DELETED = 'DELETED'
}

/** user token */
export type UserToken = {
  __typename?: 'UserToken';
  statusCode: Scalars['Int']['output'];
  statusMessage: Scalars['String']['output'];
  timestamp: Scalars['DateTimeISO']['output'];
};

/** User token */
export type UserTokenInput = {
  authId: Scalars['String']['input'];
  ocpiIdentifier: Scalars['String']['input'];
  type: UserTokenType;
  uid: Scalars['String']['input'];
};

export enum UserTokenType {
  OTHER = 'OTHER',
  RFID = 'RFID'
}

export type Voucher = {
  __typename?: 'Voucher';
  activeUntil?: Maybe<Scalars['String']['output']>;
  clientPays?: Maybe<Scalars['String']['output']>;
  creditAmount?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Scalars['String']['output']>;
  offerCode?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
};

export type VoucherCode = {
  __typename?: 'VoucherCode';
  code?: Maybe<Scalars['String']['output']>;
};

export type VoucherInfo = {
  __typename?: 'VoucherInfo';
  duration?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  userPays?: Maybe<Scalars['String']['output']>;
  voucherDesc?: Maybe<Scalars['String']['output']>;
  voucherStatus?: Maybe<Scalars['String']['output']>;
};

export type CreateSubsHistoryRecordResponse = {
  __typename?: 'createSubsHistoryRecordResponse';
  error?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionExternalId?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
};

export type DeleteSubsDefPaymentMethodResponse = {
  __typename?: 'deleteSUBSDefPaymentMethodResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type GetPendingDeletionResponse = {
  __typename?: 'getPendingDeletionResponse';
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type SaveTransactionIdInternalResponse = {
  __typename?: 'saveTransactionIdInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type ApplyOfferMutationVariables = Exact<{
  offerCode: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['String']['input']>;
  updateWalletSubs?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type ApplyOfferMutation = { __typename?: 'Mutation', applyOffer: { __typename?: 'MutationApplyOfferResult', error?: string | null, offer?: { __typename?: 'Offer', offerCode: string, offerName: string, offerPublicDescription: string, offerNotes?: string | null, offerType: OfferType, offerCountry?: OfferCountry | null, creditAmount?: number | null, creditBalance?: number | null, currency?: OfferCurrency | null, partnerName?: string | null, subsDiscount?: number | null, subsDuration?: number | null, userId?: string | null, status: OfferStatus, redemptionExpiryDate?: string | null, redemptionDate?: string | null, expiryDate?: string | null } | null } };

export type GetOffersByUserQueryVariables = Exact<{
  userId: Scalars['String']['input'];
  status?: InputMaybe<Array<OfferStatus> | OfferStatus>;
  offerType?: InputMaybe<Array<OfferType> | OfferType>;
  sortBy?: InputMaybe<UserOffersOrder>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetOffersByUserQuery = { __typename?: 'Query', getOffersByUser: { __typename?: 'QueryGetOffersByUserResult', lastEvaluatedKey?: string | null, maxOfferQuantity?: number | null, maxOfferQuantityExceeded?: boolean | null, error?: string | null, offers?: Array<{ __typename?: 'Offer', offerCode: string, offerName: string, offerPublicDescription: string, offerNotes?: string | null, offerType: OfferType, offerCountry?: OfferCountry | null, creditAmount?: number | null, creditBalance?: number | null, creditStatus?: OfferStatus | null, currency?: OfferCurrency | null, partnerName?: string | null, subsDiscount?: number | null, subsDuration?: number | null, subsStatus?: OfferSubsStatus | null, userId?: string | null, status: OfferStatus, queueStatus?: OfferQueueStatus | null, redemptionExpiryDate?: string | null, redemptionDate?: string | null, expiryDate?: string | null, monthsRemaining?: number | null }> | null, trialOffer?: { __typename?: 'TrialOffer', trialStatus: OfferStatus, trialBillingDate?: string | null, planDuration?: number | null, trialRedemptionDate?: string | null } | null } };

export type GetWalletSubscriptionQueryVariables = Exact<{
  userId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetWalletSubscriptionQuery = { __typename?: 'Query', getWalletSubscription?: { __typename?: 'SubscriptionDetails', id?: string | null, statusReason?: string | null, cancelledOn?: string | null } | null };

export type ValidateOfferQueryVariables = Exact<{
  offerCode: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['String']['input']>;
}>;


export type ValidateOfferQuery = { __typename?: 'Query', validateOffer: { __typename?: 'QueryValidateOfferResult', isValid?: boolean | null, reason?: OfferInvalidReason | null, error?: string | null, offer?: { __typename?: 'Offer', offerCode: string, offerName: string, offerPublicDescription: string, offerNotes?: string | null, offerType: OfferType, offerCountry?: OfferCountry | null, creditAmount?: number | null, creditBalance?: number | null, creditStatus?: OfferStatus | null, currency?: OfferCurrency | null, partnerName?: string | null, subsDiscount?: number | null, subsDuration?: number | null, subsStatus?: OfferSubsStatus | null, userId?: string | null, status: OfferStatus, queueStatus?: OfferQueueStatus | null, redemptionExpiryDate?: string | null, redemptionDate?: string | null, expiryDate?: string | null, monthsRemaining?: number | null } | null, contradictoryOffer?: { __typename?: 'Offer', offerCode: string, offerName: string, offerPublicDescription: string, offerNotes?: string | null, offerType: OfferType, offerCountry?: OfferCountry | null, creditAmount?: number | null, creditBalance?: number | null, currency?: OfferCurrency | null, partnerName?: string | null, subsDiscount?: number | null, subsDuration?: number | null, userId?: string | null, status: OfferStatus, redemptionExpiryDate?: string | null, redemptionDate?: string | null, expiryDate?: string | null } | null } };


export const ApplyOfferDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"applyOffer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"offerCode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"updateWalletSubs"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"applyOffer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"offerCode"},"value":{"kind":"Variable","name":{"kind":"Name","value":"offerCode"}}},{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"updateWalletSubs"},"value":{"kind":"Variable","name":{"kind":"Name","value":"updateWalletSubs"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"offer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"offerCode"}},{"kind":"Field","name":{"kind":"Name","value":"offerName"}},{"kind":"Field","name":{"kind":"Name","value":"offerPublicDescription"}},{"kind":"Field","name":{"kind":"Name","value":"offerNotes"}},{"kind":"Field","name":{"kind":"Name","value":"offerType"}},{"kind":"Field","name":{"kind":"Name","value":"offerCountry"}},{"kind":"Field","name":{"kind":"Name","value":"creditAmount"}},{"kind":"Field","name":{"kind":"Name","value":"creditBalance"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}},{"kind":"Field","name":{"kind":"Name","value":"partnerName"}},{"kind":"Field","name":{"kind":"Name","value":"subsDiscount"}},{"kind":"Field","name":{"kind":"Name","value":"subsDuration"}},{"kind":"Field","name":{"kind":"Name","value":"userId"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionExpiryDate"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionDate"}},{"kind":"Field","name":{"kind":"Name","value":"expiryDate"}}]}}]}}]}}]} as unknown as DocumentNode<ApplyOfferMutation, ApplyOfferMutationVariables>;
export const GetOffersByUserDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getOffersByUser"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"status"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"OfferStatus"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"offerType"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"OfferType"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sortBy"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"UserOffersOrder"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"lastEvaluatedKey"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getOffersByUser"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"status"}}},{"kind":"Argument","name":{"kind":"Name","value":"offerType"},"value":{"kind":"Variable","name":{"kind":"Name","value":"offerType"}}},{"kind":"Argument","name":{"kind":"Name","value":"sortBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sortBy"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"lastEvaluatedKey"},"value":{"kind":"Variable","name":{"kind":"Name","value":"lastEvaluatedKey"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"offers"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"offerCode"}},{"kind":"Field","name":{"kind":"Name","value":"offerName"}},{"kind":"Field","name":{"kind":"Name","value":"offerPublicDescription"}},{"kind":"Field","name":{"kind":"Name","value":"offerNotes"}},{"kind":"Field","name":{"kind":"Name","value":"offerType"}},{"kind":"Field","name":{"kind":"Name","value":"offerCountry"}},{"kind":"Field","name":{"kind":"Name","value":"creditAmount"}},{"kind":"Field","name":{"kind":"Name","value":"creditBalance"}},{"kind":"Field","name":{"kind":"Name","value":"creditStatus"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}},{"kind":"Field","name":{"kind":"Name","value":"partnerName"}},{"kind":"Field","name":{"kind":"Name","value":"subsDiscount"}},{"kind":"Field","name":{"kind":"Name","value":"subsDuration"}},{"kind":"Field","name":{"kind":"Name","value":"subsStatus"}},{"kind":"Field","name":{"kind":"Name","value":"userId"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"queueStatus"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionExpiryDate"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionDate"}},{"kind":"Field","name":{"kind":"Name","value":"expiryDate"}},{"kind":"Field","name":{"kind":"Name","value":"monthsRemaining"}}]}},{"kind":"Field","name":{"kind":"Name","value":"lastEvaluatedKey"}},{"kind":"Field","name":{"kind":"Name","value":"maxOfferQuantity"}},{"kind":"Field","name":{"kind":"Name","value":"maxOfferQuantityExceeded"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"trialOffer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"trialStatus"}},{"kind":"Field","name":{"kind":"Name","value":"trialBillingDate"}},{"kind":"Field","name":{"kind":"Name","value":"planDuration"}},{"kind":"Field","name":{"kind":"Name","value":"trialRedemptionDate"}}]}}]}}]}}]} as unknown as DocumentNode<GetOffersByUserQuery, GetOffersByUserQueryVariables>;
export const GetWalletSubscriptionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getWalletSubscription"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getWalletSubscription"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"statusReason"}},{"kind":"Field","name":{"kind":"Name","value":"cancelledOn"}}]}}]}}]} as unknown as DocumentNode<GetWalletSubscriptionQuery, GetWalletSubscriptionQueryVariables>;
export const ValidateOfferDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"validateOffer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"offerCode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"validateOffer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"offerCode"},"value":{"kind":"Variable","name":{"kind":"Name","value":"offerCode"}}},{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isValid"}},{"kind":"Field","name":{"kind":"Name","value":"reason"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"offer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"offerCode"}},{"kind":"Field","name":{"kind":"Name","value":"offerName"}},{"kind":"Field","name":{"kind":"Name","value":"offerPublicDescription"}},{"kind":"Field","name":{"kind":"Name","value":"offerNotes"}},{"kind":"Field","name":{"kind":"Name","value":"offerType"}},{"kind":"Field","name":{"kind":"Name","value":"offerCountry"}},{"kind":"Field","name":{"kind":"Name","value":"creditAmount"}},{"kind":"Field","name":{"kind":"Name","value":"creditBalance"}},{"kind":"Field","name":{"kind":"Name","value":"creditStatus"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}},{"kind":"Field","name":{"kind":"Name","value":"partnerName"}},{"kind":"Field","name":{"kind":"Name","value":"subsDiscount"}},{"kind":"Field","name":{"kind":"Name","value":"subsDuration"}},{"kind":"Field","name":{"kind":"Name","value":"subsStatus"}},{"kind":"Field","name":{"kind":"Name","value":"userId"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"queueStatus"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionExpiryDate"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionDate"}},{"kind":"Field","name":{"kind":"Name","value":"expiryDate"}},{"kind":"Field","name":{"kind":"Name","value":"monthsRemaining"}}]}},{"kind":"Field","name":{"kind":"Name","value":"contradictoryOffer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"offerCode"}},{"kind":"Field","name":{"kind":"Name","value":"offerName"}},{"kind":"Field","name":{"kind":"Name","value":"offerPublicDescription"}},{"kind":"Field","name":{"kind":"Name","value":"offerNotes"}},{"kind":"Field","name":{"kind":"Name","value":"offerType"}},{"kind":"Field","name":{"kind":"Name","value":"offerCountry"}},{"kind":"Field","name":{"kind":"Name","value":"creditAmount"}},{"kind":"Field","name":{"kind":"Name","value":"creditBalance"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}},{"kind":"Field","name":{"kind":"Name","value":"partnerName"}},{"kind":"Field","name":{"kind":"Name","value":"subsDiscount"}},{"kind":"Field","name":{"kind":"Name","value":"subsDuration"}},{"kind":"Field","name":{"kind":"Name","value":"userId"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionExpiryDate"}},{"kind":"Field","name":{"kind":"Name","value":"redemptionDate"}},{"kind":"Field","name":{"kind":"Name","value":"expiryDate"}}]}}]}}]}}]} as unknown as DocumentNode<ValidateOfferQuery, ValidateOfferQueryVariables>;