import { GetOffersByUserQueryResult } from '../graphql/queries/GET_OFFERS_BY_USER_QUERY';
import { ValidateOfferQueryResult } from '../graphql/queries/VALIDATE_OFFER_QUERY';

export type UserOffer = NonNullable<
  GetOffersByUserQueryResult['getOffersByUser']['offers']
>[0];

export type ValidateOffer = NonNullable<
  NonNullable<ValidateOfferQueryResult['validateOffer']>['offer']
>;

export type Offer = UserOffer | ValidateOffer;
