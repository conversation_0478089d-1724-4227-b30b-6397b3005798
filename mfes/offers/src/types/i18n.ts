import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';
/**
 * The language to display the application in.
 * This should be an ISO-639 standard locale identifier.
 *
 * This not only controls the language, but the locale formatting
 * including number, currency, etc.
 *
 * The value `zz` is also used for pseudolocalisation - a process used
 * to help identify where un-translated strings are present in the application.
 *
 * @see https://formatjs.io/docs/core-concepts/basic-internationalization-principles/#locales-language-and-region
 * @see https://en.wikipedia.org/wiki/Pseudolocalization
 */

export enum OffersLocale {
  ZZ = 'zz',
  TEST = 'test',
}

export type OffersLocaleType = OffersLocale | SupportedLocales;

export const OffersLocales = { ...OffersLocale, ...SupportedLocale };
