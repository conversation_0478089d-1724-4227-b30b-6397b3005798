import { ITheme as BPCoreTheme } from '@bp/ui-components/mobile/core/themes/default';

export type OffersMfeTheme = {
  bpCore?: BPCoreTheme;
  offersMfe: {
    color: {
      primary: string;
      secondary: string;
      black: string;
      white: string;
    };
    text: {
      fontFamily: {
        regular: string;
        medium: string;
        light: string;
        bold: string;
      };
    };
  };
};

export const theme: OffersMfeTheme = {
  offersMfe: {
    color: {
      primary: '#CC0000',
      secondary: '#FAE5E5',
      black: '#111111',
      white: '#FFFFFF',
    },
    text: {
      fontFamily: {
        regular: 'Roboto',
        medium: 'Roboto-Medium',
        light: 'Roboto-Light',
        bold: 'Roboto-Bold',
      },
    },
  },
};

export default theme;
