import { Logger } from '@bp/mfe-helper-apollo';
import clc from 'cli-color';

// @ts-ignore - expect package.json to exist
import packageJson from '../../package.json';

const packageInfo = () => clc.white(`offers (${packageJson.version})`);

export const logger: Logger = {
  debug: (...args) => console.debug(packageInfo(), ...args),
  error: (...args) => console.error(packageInfo(), ...args),
  info: (...args) => console.info(packageInfo(), ...args),
  log: (...args) => console.log(packageInfo(), ...args),
  trace: (...args) => console.trace(packageInfo(), ...args),
  warn: (...args) => console.warn(packageInfo(), ...args),
};
