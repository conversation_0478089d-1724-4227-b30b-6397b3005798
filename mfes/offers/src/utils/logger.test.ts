import { logger } from './logger';

jest.mock(
  '../../package.json',
  () => ({
    name: 'offers',
    version: '0.0.0',
  }),
  { virtual: true },
);

describe('src/utils/logger', () => {
  beforeAll(() => {
    process.env.NO_COLOR = '1';
  });

  afterAll(() => {
    delete process.env.NO_COLOR;
  });

  describe('logger', () => {
    it('should log an error message', () => {
      jest.spyOn(global.console, 'debug').mockImplementation();
      jest.spyOn(global.console, 'error').mockImplementation();
      jest.spyOn(global.console, 'info').mockImplementation();
      jest.spyOn(global.console, 'log').mockImplementation();
      jest.spyOn(global.console, 'trace').mockImplementation();
      jest.spyOn(global.console, 'warn').mockImplementation();

      const EXPECTED_PACKAGE_INFO = 'offers (0.0.0)';

      logger.warn('warn test');
      expect(console.warn).toBeCalledWith(EXPECTED_PACKAGE_INFO, 'warn test');

      logger.error('error test');
      expect(console.error).toBeCalledWith(EXPECTED_PACKAGE_INFO, 'error test');

      logger.log('log test');
      expect(console.log).toBeCalledWith(EXPECTED_PACKAGE_INFO, 'log test');

      logger.debug('debug test');
      expect(console.debug).toBeCalledWith(EXPECTED_PACKAGE_INFO, 'debug test');
    });
  });
});
