import { theme as bpCore } from '@bp/ui-components/mobile/core/themes/default';
import { NavigationContainer } from '@react-navigation/native';
import { render, RenderOptions } from '@testing-library/react-native';
import React, { ReactElement } from 'react';
import { ThemeProvider } from 'styled-components/native';

import { SettingsContextProviderProps } from '../state/Settings/Settings';
import { SettingsSeed } from '../state/Settings/Settings.seed';
import { TranslationsContextProvider } from '../state/Translations/Translations';
import { Theme } from '../themes/Theme';
import { theme } from '../themes/Theme.default';
import { OffersLocales } from '../types/i18n';

const AllTheProviders = ({ children }: SettingsContextProviderProps) => {
  return (
    <ThemeProvider theme={{ ...bpCore, ...theme }}>
      <Theme>
        <NavigationContainer>
          <SettingsSeed locale={OffersLocales.TEST}>
            <TranslationsContextProvider>
              {children}
            </TranslationsContextProvider>
          </SettingsSeed>
        </NavigationContainer>
      </Theme>
    </ThemeProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react-native';
export { customRender as render };
