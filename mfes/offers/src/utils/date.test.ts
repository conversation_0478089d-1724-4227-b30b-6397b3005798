import { compareDateString } from './date';

describe('utils/date', () => {
  describe('compareDateString()', () => {
    it('should return negative value if date A is less than date B', () => {
      const result = compareDateString(
        '2024-01-01T00:00:00.000Z',
        '2024-02-01T00:00:00.000Z',
      );
      expect(result).toBeLessThan(0);
    });

    it('should return positive value if date A is greater than date B', () => {
      const result = compareDateString(
        '2024-02-01T00:00:00.000Z',
        '2024-01-01T00:00:00.000Z',
      );
      expect(result).toBeGreaterThan(0);
    });

    it('should return zero if date A is equal to date B', () => {
      const result = compareDateString(
        '2024-02-01T00:00:00.000Z',
        '2024-02-01T00:00:00.000Z',
      );
      expect(result).toBe(0);
    });

    it('should consider invalid dates greater than valid dates', () => {
      const result1 = compareDateString(
        '2024-02-01T00:00:00.000Z',
        'not-a-valid-date',
      );
      expect(result1).toBeLessThan(0);

      const result2 = compareDateString('2024-02-01T00:00:00.000Z', undefined);
      expect(result2).toBeLessThan(0);
    });
  });
});
