import { OfferType } from '../types/graphql/graphql';
import { Offer } from '../types/Offer';
import { compareDateString } from './date';

export function sortActiveOffers(offers: Array<Offer>): Array<Offer> {
  const comboOrSubs = offers
    .filter((offer) => offer.offerType !== OfferType.CREDIT)
    // The queued offers are ensured to be in order when sorted by redemption date.
    .sort((a, b) =>
      compareDateString(
        a.redemptionDate ?? undefined,
        b.redemptionDate ?? undefined,
      ),
    );

  const credit = offers.filter((offer) => offer.offerType === OfferType.CREDIT);

  return [...comboOrSubs, ...credit];
}
