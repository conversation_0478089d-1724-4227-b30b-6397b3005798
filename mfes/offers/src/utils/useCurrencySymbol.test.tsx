import React from 'react';
import { Text } from 'react-native';

import { OfferCurrency } from '../types/graphql/graphql';
import { render, screen } from './testing';
import { useCurrencySymbol } from './useCurrencySymbol';

const TestComponent = (props: { currency: OfferCurrency }) => {
  const symbol = useCurrencySymbol(props.currency);
  return <Text>{symbol}</Text>;
};

describe('utils/useCurrencySymbol', () => {
  it('#GBP', () => {
    render(<TestComponent currency={OfferCurrency.GBP} />);

    expect(screen.getByText('£')).toBeTruthy();
  });
});
