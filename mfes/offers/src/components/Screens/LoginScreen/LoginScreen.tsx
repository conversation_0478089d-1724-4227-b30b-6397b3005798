import React, { useCallback, useEffect } from 'react';

import {
  OffersAnalyticsEventOffersCreateAccountClick,
  OffersAnalyticsEventOffersLoginClick,
  OffersAnalyticsEventOffersLoginCreateAccountOpen,
} from '../../../analytics/events';
import { useSettings } from '../../../state/Settings/Settings.context';
import { useTranslations } from '../../../state/Translations/Translations.context';
import { LoginButton } from '../../Atoms/LoginButton/LoginButton';
import { RegisterButton } from '../../Atoms/RegisterButton/RegisterButton';
import { OffersScreenProps } from '../../Navigation/OffersNavigator/types';
import { OffersScreenName } from '../OffersScreenName';
import * as S from './LoginScreen.styles';

export type LoginScreenProps = OffersScreenProps<OffersScreenName.LOGIN_SCREEN>;

export const LoginScreen = () => {
  const t = useTranslations();
  const { onRequestLogin, onRequestRegister, onAnalyticsEvent } = useSettings();

  const handleRequestLogin = useCallback(() => {
    onAnalyticsEvent(OffersAnalyticsEventOffersLoginClick());

    return onRequestLogin();
  }, [onAnalyticsEvent, onRequestLogin]);

  const handleRequestRegister = useCallback(() => {
    onAnalyticsEvent(OffersAnalyticsEventOffersCreateAccountClick());

    return onRequestRegister();
  }, [onAnalyticsEvent, onRequestRegister]);

  useEffect(() => {
    onAnalyticsEvent(OffersAnalyticsEventOffersLoginCreateAccountOpen());
  }, [onAnalyticsEvent]);

  return (
    <S.Container testID="LoginScreen">
      <S.Heading>{t.GUEST_SCREEN_HEADING}</S.Heading>

      <S.Description>{t.GUEST_SCREEN_DESCRIPTION}</S.Description>

      <S.Button>
        <LoginButton onPress={handleRequestLogin} />
      </S.Button>

      <RegisterButton onPress={handleRequestRegister} />
    </S.Container>
  );
};
