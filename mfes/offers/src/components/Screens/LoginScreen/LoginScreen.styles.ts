import { styled } from 'styled-components/native';

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.offersMfe.color.white};
  padding-horizontal: 24px;
`;

export const Heading = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 26px;
  color: ${({ theme }) => theme.offersMfe.color.black};
  line-height: 30px;
  letter-spacing: 0.15px;
  margin-top: 32px;
`;

export const Description = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 16px;
  color: ${({ theme }) => theme.offersMfe.color.black};
  line-height: 28px;
  letter-spacing: 0.1px;
  margin-top: 8px;
  margin-bottom: 24px;
`;

export const Button = styled.View`
  margin-bottom: 16px;
`;
