import { ApolloError } from '@apollo/client';
import { Button } from '@bp/ui-components/mobile/core';
import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect } from 'react';

import {
  OffersAnalyticsEventOffersScreenActiveOpen,
  OffersAnalyticsEventOffersScreenNewOfferClick,
} from '../../../analytics/events';
import { useGetOffersByUserQuery } from '../../../graphql/hooks/useGetOffersByUserQuery';
import {
  ActiveOffersActionSetIsFetching,
  ActiveOffersActionSetMaxOfferQuantity,
  ActiveOffersActionSetOffers,
  ActiveOffersActionSetTrialOffer,
} from '../../../state/ActiveOffers/ActiveOffers.actions';
import { useActiveOffers } from '../../../state/ActiveOffers/ActiveOffers.context';
import { activeOffersStateInitial } from '../../../state/ActiveOffers/ActiveOffers.state';
import { RetryErrorActionSetError } from '../../../state/RetryError/RetryError.actions';
import { useRetryError } from '../../../state/RetryError/RetryError.context';
import { useSettings } from '../../../state/Settings/Settings.context';
import { useTranslations } from '../../../state/Translations/Translations.context';
import { ViewedOffersActionMarkAsViewed } from '../../../state/ViewedOffers/ViewedOffers.actions';
import { useViewedOffers } from '../../../state/ViewedOffers/ViewedOffers.context';
import { OfferStatus, UserOffersOrder } from '../../../types/graphql/graphql';
import { logger } from '../../../utils/logger';
import { useIntroSubsOffer } from '../../../utils/useIntroSubsOffer';
import { LimitExceededBanner } from '../../Atoms/LimitExceededBanner/LimitExceededBanner';
import { LoadingSpinner } from '../../Atoms/LoadingSpinner/LoadingSpinner';
import { UserOffersFooter } from '../../Molecules/UserOffersFooter/UserOffersFooter';
import { OffersScreenProps } from '../../Navigation/OffersNavigator/types';
import { ActiveOffersList } from '../../Organisms/ActiveOffersList/ActiveOffersList';
import { OffersScreenName } from '../OffersScreenName';
import * as S from './UserOffersScreen.styles';

export type UserOffersScreenProps =
  OffersScreenProps<OffersScreenName.USER_OFFERS_SCREEN>;

export const UserOffersScreen = ({ route }: UserOffersScreenProps) => {
  const t = useTranslations();
  const navigation = useNavigation();
  const getOffersByUserQuery = useGetOffersByUserQuery();
  const { onAnalyticsEvent } = useSettings();
  const { retryErrorDispatch } = useRetryError();
  const { viewedOffersDispatch } = useViewedOffers();
  const { activeOffersState, activeOffersDispatch } = useActiveOffers();

  const { reload } = route.params ?? {};

  const {
    isFetching,
    offers,
    trialOffer,
    maxOfferQuantity,
    maxOfferQuantityExceeded,
  } = activeOffersState;

  const introSubsOffer = useIntroSubsOffer(trialOffer);

  const loadOffers = useCallback(async () => {
    try {
      activeOffersDispatch(
        ActiveOffersActionSetIsFetching({ isFetching: true }),
      );

      const result = await getOffersByUserQuery({
        variables: {
          status: [OfferStatus.REDEEMED],
          sortBy: UserOffersOrder.EXPIRY_DATE,
        },
      });

      activeOffersDispatch(
        ActiveOffersActionSetOffers({
          offers: result.offers ?? [],
        }),
      );

      activeOffersDispatch(
        ActiveOffersActionSetMaxOfferQuantity({
          maxOfferQuantity: result.maxOfferQuantity ?? undefined,
          maxOfferQuantityExceeded: !!result.maxOfferQuantityExceeded,
        }),
      );

      activeOffersDispatch(
        ActiveOffersActionSetTrialOffer({
          trialOffer: result.trialOffer ?? undefined,
        }),
      );

      if (result.offers?.length) {
        onAnalyticsEvent(
          OffersAnalyticsEventOffersScreenActiveOpen({
            offer_count: result.offers.length,
          }),
        );
      }
    } catch (e) {
      const error = e as ApolloError;

      logger.error(`Failed to fetch user active offers: ${error.message}`);
      retryErrorDispatch(
        RetryErrorActionSetError({ error, onRequestRetry: loadOffers }),
      );

      activeOffersDispatch(
        ActiveOffersActionSetIsFetching({ isFetching: false }),
      );

      throw e;
    }
  }, [
    activeOffersDispatch,
    getOffersByUserQuery,
    onAnalyticsEvent,
    retryErrorDispatch,
  ]);

  const handleAddOfferCode = useCallback(() => {
    onAnalyticsEvent(OffersAnalyticsEventOffersScreenNewOfferClick());
    navigation.navigate(OffersScreenName.ADD_NEW_OFFER_SCREEN);
  }, [navigation, onAnalyticsEvent]);

  useEffect(() => {
    return navigation.addListener('blur', () => {
      if (!offers.length) {
        return;
      }

      const offerCodes = offers.map((offer) => offer.offerCode);
      if (introSubsOffer?.status === OfferStatus.REDEEMED) {
        offerCodes.push(introSubsOffer.offerCode);
      }

      viewedOffersDispatch(ViewedOffersActionMarkAsViewed({ offerCodes }));
    });
  }, [navigation, offers, viewedOffersDispatch, introSubsOffer]);

  useEffect(() => {
    if (activeOffersState === activeOffersStateInitial || reload) {
      navigation.setParams({ reload: false });
      loadOffers();
    }
  }, [activeOffersState, loadOffers, navigation, reload]);

  if (isFetching) {
    return <LoadingSpinner />;
  }

  return (
    <S.Container testID="UserOffersScreen">
      {maxOfferQuantityExceeded && typeof maxOfferQuantity === 'number' && (
        <LimitExceededBanner limit={maxOfferQuantity} />
      )}

      <ActiveOffersList
        offers={offers}
        trialOffer={trialOffer}
        ListFooterComponent={UserOffersFooter}
      />

      {!maxOfferQuantityExceeded && (
        <S.BottomButtonContainer>
          <Button
            testID="UserOffersScreen.AddOfferCodeButton"
            onPress={handleAddOfferCode}
          >
            {t.ADD_OFFER_CODE}
          </Button>
        </S.BottomButtonContainer>
      )}
    </S.Container>
  );
};
