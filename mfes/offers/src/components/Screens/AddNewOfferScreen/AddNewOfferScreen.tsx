import { ApolloError } from '@apollo/client';
import { StackActions, useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import {
  OffersAnalyticsEventAddNewOfferConfirmClick,
  OffersAnalyticsEventAddNewOfferScreenOpen,
  OffersAnalyticsEventErrorOfferNotAddedOpen,
  OffersAnalyticsEventOfferAddedSuccessOpen,
  OffersAnalyticsEventSubsRequiredOpen,
} from '../../../analytics/events';
import { isSelectedNewOffer } from '../../../analytics/utils';
import { useApplyOfferMutation } from '../../../graphql/hooks/useApplyOfferMutation';
import { useGetWalletSubscriptionQuery } from '../../../graphql/hooks/useGetWalletSubscriptionQuery';
import { useSettings } from '../../../state/Settings/Settings.context';
import { useTranslations } from '../../../state/Translations/Translations.context';
import { OfferType } from '../../../types/graphql/graphql';
import { Offer } from '../../../types/Offer';
import { logger } from '../../../utils/logger';
import { isSubsRequired } from '../../../utils/offer';
import { AddNewOfferScreenHeader } from '../../Molecules/AddNewOfferScreenHeader/AddNewOfferScreenHeader';
import { ExitAddNewOfferModal } from '../../Molecules/ExitAddNewOfferModal/ExitAddNewOfferModal';
import { OfferCard } from '../../Molecules/OfferCard/OfferCard';
import { OverridingOfferModal } from '../../Molecules/OverridingOfferModal/OverridingOfferModal';
import { OffersScreenProps } from '../../Navigation/OffersNavigator/types';
import { AddNewOfferScreenFooter } from '../../Organisms/AddNewOfferScreenFooter/AddNewOfferScreenFooter';
import { ContradictingOffersSelection } from '../../Organisms/ContradictingOfferSelection/ContradictingOfferSelection';
import {
  OfferCodeValidationInput,
  OfferValidationResult,
} from '../../Organisms/OfferCodeValidationInput/OfferCodeValidationInput';
import { OffersScreenName } from '../OffersScreenName';
import * as S from './AddNewOfferScreen.styles';

export type AddNewOfferScreenProps =
  OffersScreenProps<OffersScreenName.ADD_NEW_OFFER_SCREEN>;

export const AddNewOfferScreen = ({ route }: AddNewOfferScreenProps) => {
  const t = useTranslations();
  const navigation = useNavigation();
  const { userInfo, onAnalyticsEvent } = useSettings();
  const applyOffer = useApplyOfferMutation();
  const getWalletSubscription = useGetWalletSubscriptionQuery();

  const [isExitConfirmationModalVisible, setExitConfirmationModalVisible] =
    useState(false);
  const [selectedOffer, setSelectedOffer] = useState<Offer>();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [validationResult, setValidationResult] =
    useState<OfferValidationResult>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    onAnalyticsEvent(OffersAnalyticsEventAddNewOfferScreenOpen());
  }, [onAnalyticsEvent]);

  const { offerCode } = route.params ?? {};

  const { offer: newOffer, contradictoryOffer: activeOffer } =
    validationResult ?? {};

  const addButtonAction = useMemo(() => {
    if (!activeOffer) {
      return 'add';
    }

    return selectedOffer === activeOffer ? 'keep' : 'switch';
  }, [activeOffer, selectedOffer]);

  const handleCodeChange = useCallback(() => {
    setSelectedOffer(undefined);
    setValidationResult(undefined);
  }, []);

  const handleValidCode = useCallback((result: OfferValidationResult) => {
    setSelectedOffer(result.offer ?? undefined);
    setValidationResult(result);
  }, []);

  const handleAddOffer = useCallback(async () => {
    onAnalyticsEvent(
      OffersAnalyticsEventAddNewOfferConfirmClick({
        selected_new: isSelectedNewOffer(newOffer, activeOffer, selectedOffer),
        offer_code: selectedOffer!.offerCode,
        offer_type: selectedOffer!.offerType,
      }),
    );

    try {
      if (
        !!userInfo.userType &&
        isSubsRequired(selectedOffer!, userInfo.userType)
      ) {
        onAnalyticsEvent(
          OffersAnalyticsEventSubsRequiredOpen({
            offer_code: selectedOffer!.offerCode,
          }),
        );

        navigation.navigate(OffersScreenName.SUBSCRIPTION_REQUIRED_SCREEN, {
          offerCode: selectedOffer!.offerCode,
        });
        return;
      }

      setLoading(true);

      if (selectedOffer?.offerType !== OfferType.CREDIT) {
        const result = await getWalletSubscription();
        if (result?.statusReason?.toUpperCase() === 'PENDING_CANCELLATION') {
          navigation.navigate(OffersScreenName.ADD_OFFER_PENDING_CANCELLATION, {
            cancelledOn: result.cancelledOn,
          });

          setLoading(false);
          return;
        }
      }

      await applyOffer({ variables: { offerCode: selectedOffer!.offerCode } });
      setLoading(false);
      onAnalyticsEvent(
        OffersAnalyticsEventOfferAddedSuccessOpen({
          offer_code: selectedOffer!.offerCode,
          offer_type: selectedOffer!.offerType,
        }),
      );

      navigation.dispatch(
        StackActions.replace(OffersScreenName.ADD_OFFER_SUCCESS_SCREEN),
      );
    } catch (e) {
      const error = e as ApolloError;

      setLoading(false);
      logger.error(`Error applying offer: ${error.message}`);
      onAnalyticsEvent(
        OffersAnalyticsEventErrorOfferNotAddedOpen({
          entered_value: selectedOffer!.offerCode,
          error_message: error.message,
        }),
      );
      navigation.dispatch(
        StackActions.replace(OffersScreenName.ADD_OFFER_ERROR_SCREEN),
      );
    }
  }, [
    activeOffer,
    applyOffer,
    getWalletSubscription,
    navigation,
    newOffer,
    onAnalyticsEvent,
    selectedOffer,
    userInfo.userType,
  ]);

  const handleConfirmSwitchOffer = useCallback(() => {
    setIsModalVisible(false);
    handleAddOffer();
  }, [handleAddOffer]);

  const handleCloseSwitchModal = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  const handleOpenSwitchModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const handleGoBack = useCallback(() => {
    if (validationResult?.isValid) {
      setExitConfirmationModalVisible(true);
    } else {
      navigation.goBack();
    }
  }, [navigation, validationResult?.isValid]);

  const handleRequestClose = useCallback(() => {
    setExitConfirmationModalVisible(false);
  }, []);

  const handleRequestExit = useCallback(() => {
    setExitConfirmationModalVisible(false);
    navigation.goBack();
  }, [navigation]);

  return (
    <>
      <AddNewOfferScreenHeader onPress={handleGoBack} />

      <S.Container testID="AddNewOfferScreen">
        <S.ScrollView
          alwaysBounceVertical={false}
          keyboardShouldPersistTaps="handled"
        >
          <OfferCodeValidationInput
            initialValue={offerCode}
            onCodeChange={handleCodeChange}
            onValidCode={handleValidCode}
          />

          {!newOffer && (
            <S.Instructions>
              <S.Title testID="AddNewOfferScreen.Title">
                {t.ADD_NEW_OFFER_DESCRIPTION_TITLE}
              </S.Title>
              <S.Description testID="AddNewOfferScreen.Description">
                {t.ADD_NEW_OFFER_DESCRIPTION}
              </S.Description>
            </S.Instructions>
          )}

          {newOffer &&
            (activeOffer ? (
              <ContradictingOffersSelection
                newOffer={newOffer}
                activeOffer={activeOffer}
                selectedOffer={selectedOffer}
                onChangeSelectedOffer={setSelectedOffer}
              />
            ) : (
              <S.CardWrapper testID="AddNewOfferScreen.OfferCard">
                <OfferCard offer={newOffer} />
              </S.CardWrapper>
            ))}
        </S.ScrollView>

        <AddNewOfferScreenFooter
          action={addButtonAction}
          onAdd={handleAddOffer}
          onSwitch={handleOpenSwitchModal}
          disabled={loading || !selectedOffer}
        />
      </S.Container>

      <ExitAddNewOfferModal
        isVisible={isExitConfirmationModalVisible}
        onRequestClose={handleRequestClose}
        onRequestExit={handleRequestExit}
      />

      <OverridingOfferModal
        isModalVisible={isModalVisible}
        onConfirm={handleConfirmSwitchOffer}
        onRequestClose={handleCloseSwitchModal}
        activeOfferType={activeOffer?.offerType}
        newOfferType={newOffer?.offerType}
      />
    </>
  );
};
