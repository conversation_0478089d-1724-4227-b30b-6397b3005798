import { styled } from 'styled-components/native';

export const Container = styled.View`
  flex: 1;
`;

export const ScrollView = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    flexGrow: 1,
    padding: 24,
    paddingBottom: 16,
  },
}))`
  background-color: ${({ theme }) => theme.offersMfe.color.white};
`;

export const Instructions = styled.View`
  padding-top: 13px;
`;

export const Title = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 18px;
  line-height: 28px;
  font-weight: bold;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const Description = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 14px;
  line-height: 23px;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const CardWrapper = styled.View`
  padding-top: 13px;
`;
