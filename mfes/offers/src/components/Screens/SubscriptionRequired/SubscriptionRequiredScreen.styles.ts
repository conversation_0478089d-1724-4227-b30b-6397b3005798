import { styled } from 'styled-components/native';

export const Container = styled.ScrollView.attrs(() => ({
  alwaysBounceVertical: false,
  contentContainerStyle: {
    flexGrow: 1,
    padding: 24,
    gap: 24,
  },
}))`
  background-color: white;
`;

export const ContentContainer = styled.View`
  flex: 1;
  align-items: center;
  justify-content: space-between;
`;

export const Title = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.light};
  font-size: 28px;
  text-align: center;
  line-height: 40px;
  letter-spacing: 0.5px;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const Label = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.light};
  font-size: 18px;
  text-align: center;
  line-height: 32px;
  letter-spacing: 0.2px;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const ButtonsContainer = styled.View`
  gap: 16px;
`;
export const LabelsContainer = styled.View`
  gap: 16px;
`;
