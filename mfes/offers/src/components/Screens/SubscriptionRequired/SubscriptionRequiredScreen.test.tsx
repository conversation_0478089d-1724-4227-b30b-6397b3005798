import React from 'react';

import { SettingsSeed } from '../../../state/Settings/Settings.seed';
import { SettingsState } from '../../../state/Settings/Settings.state';
import { fireEvent, render, screen } from '../../../utils/testing';
import { OffersScreenName } from '../OffersScreenName';
import { SubscriptionRequiredScreen } from './SubscriptionRequiredScreen';

beforeEach(() => {
  jest.clearAllMocks();
});

const onFinishSubscriptionSetupMock = jest.fn();

const mockNavigate = jest.fn();

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: () => ({
      navigate: mockNavigate,
    }),
  };
});

const renderComponent = (state: Partial<SettingsState> = {}, params = {}) => {
  const navigation: any = {};
  const route: any = { params };

  return render(
    <SettingsSeed {...state}>
      <SubscriptionRequiredScreen navigation={navigation} route={route} />
    </SettingsSeed>,
  );
};

describe('<SubscriptionRequiredScreen />', () => {
  it('should render Subscription Required title, with description and buttons', () => {
    renderComponent();

    const subsRequried = screen.getByTestId('SubscriptionRequiredScreen');
    expect(subsRequried).toBeTruthy();

    const requiresOfferText = screen.getByText(
      'This offer requires a subscription.',
    );
    expect(requiresOfferText).toBeTruthy();

    const finishSubsBtn = screen.getByText('Finish subscription setup');
    expect(finishSubsBtn).toBeTruthy();

    const removeOfferBtn = screen.getByText('Remove offer');
    expect(removeOfferBtn).toBeTruthy();
  });

  it('should disable finishSubscription button if there is no internet connection', () => {
    renderComponent({ isInternetReachable: false });

    const finishSubsBtn = screen.getByTestId('FinishSubscriptionButton');
    expect(finishSubsBtn).not.toBeNull();
    expect(finishSubsBtn.props.accessibilityState.disabled).toBe(true);
  });

  it('should navigate to UserOffers screen when pressing RemoveOffer button', () => {
    renderComponent();
    const removeButton = screen.getByTestId('RemoveOfferButton');

    fireEvent.press(removeButton);

    expect(mockNavigate).toHaveBeenCalledWith(
      OffersScreenName.USER_OFFERS_SCREEN,
    );
  });

  it('should call onFinishSubscriptionSetup when presing finish subscription button, with the offer code included', () => {
    renderComponent(
      {
        onFinishSubscriptionSetup: onFinishSubscriptionSetupMock,
      },
      { offerCode: 'offer-123' },
    );
    const finishSubsBtn = screen.getByTestId('FinishSubscriptionButton');

    expect(finishSubsBtn).not.toBeNull();
    fireEvent.press(finishSubsBtn);

    expect(onFinishSubscriptionSetupMock).toHaveBeenCalledWith('offer-123');
  });
});
