import React from 'react';

import { render, screen } from '../../../utils/testing';
import { PillMarker, PillMarkerProps, PillMarkerType } from './PillMarker';

beforeEach(() => {
  jest.clearAllMocks();
});

const renderComponent = ({ type }: PillMarkerProps) =>
  render(<PillMarker type={type} />);

describe('<PillMarker />', () => {
  it('should render', () => {
    renderComponent({ type: PillMarkerType.JUST_ADDED });

    const result = screen.queryByText('Just added');
    expect(result).not.toBeNull();
  });
  it('should render NEW_OFFER', () => {
    renderComponent({ type: PillMarkerType.NEW_OFFER });

    const result = screen.queryByText('NEW OFFER');
    expect(result).not.toBeNull();
  });
  it('should render ACTIVE_OFFER', () => {
    renderComponent({ type: PillMarkerType.ACTIVE_OFFER });

    const result = screen.queryByText('ACTIVE OFFER');
    expect(result).not.toBeNull();
  });
});
