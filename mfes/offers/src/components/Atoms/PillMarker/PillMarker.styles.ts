import { styled } from 'styled-components/native';

import AlertIconCircleSvg from '../../../../assets/icons/AlertIconCircle.svg';

export const Container = styled.View<{
  backgroundColor?: string;
}>`
  border-radius: 14px;
  padding: 4px 12px;
  background-color: ${(props) => props.backgroundColor};
  flex-direction: row;
  align-items: center;
  align-self: flex-start;
  gap: 4px;
`;

export const Text = styled.Text<{ color?: string }>`
  color: ${(props) => props.color};
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 10px;
  text-align: center;
  line-height: 19px;
  letter-spacing: 1px;
  text-transform: uppercase;
`;

export const AlertIconCircle = styled(AlertIconCircleSvg)`
  width: 16px;
  height: 16px;
`;
