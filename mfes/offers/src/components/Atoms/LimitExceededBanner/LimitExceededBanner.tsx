import React from 'react';

import { useTranslations } from '../../../state/Translations/Translations.context';
import * as S from './LimitExceededBanner.styles';

export type LimitExceededBannerProps = {
  limit: number;
};

export const LimitExceededBanner = ({ limit }: LimitExceededBannerProps) => {
  const t = useTranslations();

  const maxQtyLabel = [
    t.LIMIT_BANNER.YOURE_AT,
    limit,
    t.LIMIT_BANNER.OFFER_LIMIT,
  ].join(' ');

  return (
    <S.BannerContainer testID="LimitExceededBanner">
      <S.AlertIcon />
      <S.BannerLabel>{maxQtyLabel}</S.BannerLabel>
    </S.BannerContainer>
  );
};
