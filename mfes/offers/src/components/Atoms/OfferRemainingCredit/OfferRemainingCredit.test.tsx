import React from 'react';

import comboOffer from '../../../graphql/mock-responses/offers/ComboOffer';
import creditOffer from '../../../graphql/mock-responses/offers/CreditOffer';
import { OfferQueueStatus, OfferStatus } from '../../../types/graphql/graphql';
import { Offer } from '../../../types/Offer';
import { render, screen } from '../../../utils/testing';
import { OfferRemainingCredit } from './OfferRemainingCredit';

const renderComponent = (offer?: Offer) => {
  return render(<OfferRemainingCredit offer={offer ?? creditOffer} />);
};

describe('<OfferRemainingCredit />', () => {
  it('should show when offer is redeemed', () => {
    const offer = { ...creditOffer, userId: 'user-id' };
    renderComponent(offer);

    const remainingCredit = screen.queryByTestId('OfferRemainingCredit');
    expect(remainingCredit).not.toBeNull();
  });

  it('should hide when offer is not redeemed', () => {
    renderComponent();

    const remainingCredit = screen.queryByTestId('OfferRemainingCredit');
    expect(remainingCredit).toBeNull();
  });

  it('should hide when offer is combo and is pending deletion', () => {
    const offer = {
      ...comboOffer,
      userId: 'user-id',
      queueStatus: OfferQueueStatus.PENDING_DELETION,
    };

    renderComponent(offer);

    const remainingCredit = screen.queryByTestId('OfferRemainingCredit');
    expect(remainingCredit).toBeNull();
  });

  it('should hide when offer is combo and is overriden', () => {
    const offer = {
      ...comboOffer,
      userId: 'user-id',
      queueStatus: null,
      creditStatus: OfferStatus.USED,
      creditAmount: 10,
    };

    renderComponent(offer);

    const remainingCredit = screen.queryByTestId('OfferRemainingCredit');
    expect(remainingCredit).toBeNull();
  });
});
