import React, { useMemo } from 'react';

import { useSettings } from '../../../state/Settings/Settings.context';
import { useTranslations } from '../../../state/Translations/Translations.context';
import { OfferQueueStatus, OfferStatus } from '../../../types/graphql/graphql';
import { Offer } from '../../../types/Offer';
import { reduceToSupportedLocale } from '../../../utils/i18n';
import { isOfferOverridden, isRedeemed } from '../../../utils/offer';
import { FormattedCurrency } from '../FormattedCurrency/FormattedCurrency';
import * as S from './OfferRemainingCredit.styles';

export type OfferRemainingCreditProps = {
  offer: Offer;
};

export const OfferRemainingCredit = ({ offer }: OfferRemainingCreditProps) => {
  const t = useTranslations();
  const { locale } = useSettings();
  const isExpired = offer.status === OfferStatus.EXPIRED;
  const isPendingDeletion =
    offer.queueStatus === OfferQueueStatus.PENDING_DELETION;

  const expiryDate = useMemo(() => {
    if (!offer.expiryDate) {
      return undefined;
    }

    const formatLocale = reduceToSupportedLocale(locale);

    const formatter = new Intl.DateTimeFormat(formatLocale);

    return formatter.format(new Date(offer.expiryDate));
  }, [locale, offer.expiryDate]);

  if (
    !offer.currency ||
    !isRedeemed(offer) ||
    isExpired ||
    isPendingDeletion ||
    isOfferOverridden(offer)
  ) {
    return null;
  }

  return (
    <S.Container testID="OfferRemainingCredit">
      <S.Text>{`${t.CREDIT_REMAINING}: `}</S.Text>
      <FormattedCurrency
        amount={offer.creditBalance ?? 0}
        currency={offer.currency}
        LiteralComponent={S.Text}
        CurrencyPrefixComponent={S.Text}
        CurrencySuffixComponent={S.Text}
        FractionComponent={S.Text}
      />

      {expiryDate && (
        <S.ExpiryDate>{` (${t.EXPIRY} ${expiryDate})`}</S.ExpiryDate>
      )}
    </S.Container>
  );
};
