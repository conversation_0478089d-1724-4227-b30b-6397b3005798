import { styled } from 'styled-components/native';

export const Container = styled.View`
  display: flex;
  flex-direction: row;
  margin-top: 6px;
`;

export const Text = styled.Text`
  color: ${({ theme }) => theme.offersMfe.color.black};
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.light};
  font-size: 13px;
  line-height: 23px;
  letter-spacing: 0.25px;
`;

export const ExpiryDate = styled.Text`
  color: ${({ theme }) => theme.offersMfe.color.black};
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.light};
  font-size: 10px;
  line-height: 21px;
  letter-spacing: 0.21px;
  align-self: flex-end;
`;
