import { styled } from 'styled-components/native';

export const CheckButton = styled.TouchableOpacity`
  width: 96px;
  height: 48px;
  border-radius: 5px;
  align-items: center;
  justify-content: center;
  background-color: #212121;
  opacity: ${({ disabled }) => (disabled ? 0.75 : 1)};
`;

export const Text = styled.Text`
  color: ${({ theme }) => theme.offersMfe.color.white};
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 15px;
  line-height: 20px;
  text-align: center;
`;
