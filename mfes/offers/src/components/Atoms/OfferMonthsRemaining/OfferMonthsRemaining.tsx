import React from 'react';

import { useTranslations } from '../../../state/Translations/Translations.context';
import { Offer } from '../../../types/Offer';
import {
  isOfferOverridden,
  isPendingDeletionOrQueued,
  isUsedOrExpired,
} from '../../../utils/offer';
import * as S from './OfferMonthsRemaining.styles';

export type OfferMonthsRemainingProps = {
  offer: Offer;
};

export const OfferMonthsRemaining = ({ offer }: OfferMonthsRemainingProps) => {
  const t = useTranslations();

  if (
    typeof offer.monthsRemaining !== 'number' ||
    isUsedOrExpired(offer) ||
    isPendingDeletionOrQueued(offer) ||
    isOfferOverridden(offer)
  ) {
    return null;
  }

  return (
    <S.Text testID="OfferMonthsRemaining">{`${t.MONTHS_REMAINING}: ${offer.monthsRemaining}`}</S.Text>
  );
};
