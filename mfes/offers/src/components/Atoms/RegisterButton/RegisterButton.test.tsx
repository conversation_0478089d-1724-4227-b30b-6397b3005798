import React from 'react';

import { fireEvent, render, screen } from '../../../utils/testing';
import { RegisterButton } from './RegisterButton';

const handlePress = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
});

const renderComponent = () => render(<RegisterButton onPress={handlePress} />);

describe('<LoginButton />', () => {
  it('#onPress', () => {
    renderComponent();

    const registerButton = screen.getByTestId('RegisterButton');
    expect(registerButton).toBeTruthy();

    fireEvent.press(registerButton);
    expect(handlePress).toHaveBeenCalledTimes(1);
  });

  it('should use secondary button type', () => {
    renderComponent();

    const registerButton = screen.getByText('Create an account');
    expect(registerButton.props.type).toEqual('secondary');
  });
});
