import {
  Button,
  ButtonA<PERSON>,
  IButton as ButtonProps,
} from '@bp/ui-components/mobile/core';
import React from 'react';

import { useTranslations } from '../../../state/Translations/Translations.context';

export type LoginButtonProps = {
  onPress: ButtonProps['onPress'];
};

export const LoginButton = ({ onPress }: LoginButtonProps) => {
  const t = useTranslations();

  return (
    <Button
      testID={'LoginButton'}
      accessibilityLabel={t.LOGIN}
      type={ButtonAction.PRIMARY}
      onPress={onPress}
    >
      {t.LOGIN}
    </Button>
  );
};
