import React from 'react';

import { fireEvent, render, screen } from '../../../utils/testing';
import { SubscribeButton } from './SubscribeButton';

const handlePress = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
});

const renderComponent = () => render(<SubscribeButton onPress={handlePress} />);

describe('<SubscribeButton />', () => {
  it('#onPress', () => {
    renderComponent();

    const subscribeButton = screen.getByTestId('SubscribeButton');
    expect(subscribeButton).toBeTruthy();

    fireEvent.press(subscribeButton);

    expect(handlePress).toHaveBeenCalledTimes(1);
  });

  it('should render "Subscribe"', () => {
    renderComponent();

    const subscribeButton = screen.getByText('Subscribe');
    expect(subscribeButton).toBeTruthy();
  });
});
