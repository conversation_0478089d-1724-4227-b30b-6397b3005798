import { IButton as ButtonProps } from '@bp/ui-components/mobile/core';
import React from 'react';

import { useTranslations } from '../../../state/Translations/Translations.context';
import * as S from './SubscribeButton.styles';

export type SubscribeButtonProps = {
  onPress?: ButtonProps['onPress'];
};

export const SubscribeButton = ({ onPress }: SubscribeButtonProps) => {
  const t = useTranslations();

  return (
    <S.ButtonContainer
      testID="SubscribeButton"
      accessibilityLabel={t.SUBSCRIBE}
      onPress={onPress}
    >
      <S.ButtonText>{t.SUBSCRIBE}</S.ButtonText>
    </S.ButtonContainer>
  );
};
