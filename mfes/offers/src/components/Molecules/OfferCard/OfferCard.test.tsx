import React from 'react';

import comboOfferMock from '../../../graphql/mock-responses/offers/ComboOffer';
import creditOfferMock from '../../../graphql/mock-responses/offers/CreditOffer';
import subscriptionOffer from '../../../graphql/mock-responses/offers/SubscriptionOffer';
import { render } from '../../../utils/testing';
import { OfferCard } from './OfferCard';

describe('OfferCard Component', () => {
  it('renders CreditOfferCard when offerType is CREDIT', () => {
    const { getByTestId } = render(<OfferCard offer={creditOfferMock} />);
    const creditOfferCard = getByTestId('CreditOfferCard');

    expect(creditOfferCard).toBeTruthy();
  });
  it('renders SubscriptionOfferCard when offerType is SUBS', () => {
    const { getByTestId } = render(<OfferCard offer={subscriptionOffer} />);
    const subscriptionOfferCard = getByTestId('SubscriptionOfferCard');

    expect(subscriptionOfferCard).toBeTruthy();
  });
  it('renders ComboOfferCard when offerType is SUBS', () => {
    const { getByTestId } = render(<OfferCard offer={comboOfferMock} />);
    const ComboOfferCard = getByTestId('ComboOfferCard');

    expect(ComboOfferCard).toBeTruthy();
  });
});
