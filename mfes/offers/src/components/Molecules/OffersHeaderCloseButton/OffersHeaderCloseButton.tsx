import { CloseIcon } from '@bp/ui-components/mobile';
import { Header } from '@bp/ui-components/mobile/core';
import { StackHeaderProps } from '@react-navigation/stack';
import React, { useMemo } from 'react';
import { useTheme } from 'styled-components';

export type OffersHeaderCloseButtonProps = Pick<
  StackHeaderProps,
  'navigation' | 'options' | 'route'
>;

export const OffersHeaderCloseButton = ({
  navigation,
  options,
  route,
}: OffersHeaderCloseButtonProps) => {
  const title = useMemo(() => {
    if (typeof options.headerTitle === 'string') {
      return options.headerTitle;
    }

    return options.title ?? route.name;
  }, [options.headerTitle, options.title, route.name]);

  const theme = useTheme();

  return (
    <Header
      title={title}
      hasLeftButton={false}
      hasRightButton={true}
      onRightButtonClick={navigation.popToTop}
      RightComponent={<CloseIcon color={theme.offersMfe.color.black} />}
      border={false}
    />
  );
};
