import { Header } from '@bp/ui-components/mobile/core';
import { StackHeaderProps } from '@react-navigation/stack';
import React, { useMemo } from 'react';

export type OffersStackHeaderProps = Pick<
  StackHeaderProps,
  'navigation' | 'options' | 'route'
>;

export const OffersStackHeader = ({
  navigation,
  options,
  route,
}: OffersStackHeaderProps) => {
  const title = useMemo(() => {
    if (typeof options.headerTitle === 'string') {
      return options.headerTitle;
    }

    return options.title ?? route.name;
  }, [options.headerTitle, options.title, route.name]);

  return (
    <Header
      title={title}
      hasLeftButton={navigation.canGoBack()}
      onLeftButtonClick={navigation.goBack}
      border={false}
    />
  );
};
