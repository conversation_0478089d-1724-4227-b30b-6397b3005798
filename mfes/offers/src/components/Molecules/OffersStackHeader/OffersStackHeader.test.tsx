import React from 'react';

import { render, screen } from '../../../utils/testing';
import { OffersStackHeader } from './OffersStackHeader';

const canGoBackMock = jest.fn().mockReturnValue(false);
const goBackMock = jest.fn();

const navigation: any = {
  canGoBack: canGoBackMock,
  goBack: goBackMock,
};

const renderComponent = (options = {}) =>
  render(
    <OffersStackHeader
      navigation={navigation}
      options={options}
      route={{
        key: 'route-key',
        name: 'route-name',
      }}
    />,
  );

beforeEach(() => {
  jest.clearAllMocks();
});

describe('<OffersStackHeader />', () => {
  it('should show title from headerTitle property on options', () => {
    renderComponent({ headerTitle: 'header-title', title: 'title' });

    const headerTitle = screen.queryByText('header-title');
    expect(headerTitle).not.toBeNull();

    const title = screen.queryByText('title');
    expect(title).toBeNull();

    const routeName = screen.queryByText('route-name');
    expect(routeName).toBeNull();
  });

  it('should show title from title property on options', () => {
    renderComponent({ title: 'title' });

    const title = screen.queryByText('title');
    expect(title).not.toBeNull();

    const routeName = screen.queryByText('route-name');
    expect(routeName).toBeNull();
  });

  it('should fallback title to route name', () => {
    renderComponent();

    const title = screen.queryByText('route-name');
    expect(title).not.toBeNull();
  });
});
