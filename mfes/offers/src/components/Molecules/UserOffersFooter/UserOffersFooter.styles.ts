import { styled } from 'styled-components/native';

import ChevronRightIconSVG from '../../../../assets/icons/ChevronRightIcon.svg';
import DetailsIconSVG from '../../../../assets/icons/DetailsIcon.svg';

export const DetailsIcon = styled(DetailsIconSVG)`
  height: 24px;
  width: 24px;
`;

export const ChevronRightIcon = styled(ChevronRightIconSVG)`
  height: 24px;
  width: 24px;
`;

export const Container = styled.TouchableOpacity`
  display: flex;
  border-top-width: 1px;
  border-bottom-width: 1px;
  border-color: rgb(222, 222, 222);
  height: 80px;
  flex-direction: row;
  align-items: center;
`;

export const LeftIcon = styled.View`
  margin-left: 24px;
  margin-right: 15px;
`;

export const Text = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.7px;
  font-weight: normal;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const RightIcon = styled.View`
  margin-right: 24px;
  margin-left: auto;
`;

export const TCsText = styled.Text`
  padding-vertical: 16px;
  padding-horizontal: 24px;
  text-align: center;
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.2px;
  font-weight: normal;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const TCsLink = styled.Text`
  color: #002bff;
  text-decoration-line: underline;
  text-decoration-color: #002bff;
`;
