import React from 'react';

import { useTranslations } from '../../../state/Translations/Translations.context';
import * as S from './NothingToShow.styles';

export const NothingToShow = () => {
  const t = useTranslations();

  return (
    <S.Container testID="NothingToShow">
      <S.MagnifyingGlassIcon testID="NothingToShow.MagnifyingGlassIcon" />

      <S.Heading testID="NothingToShow.Heading">
        {t.NOTHING_TO_SHOW_HEADING}
      </S.Heading>

      <S.Description testID="NothingToShow.Description">
        {t.NOTHING_TO_SHOW_DESCRIPTION}
      </S.Description>
    </S.Container>
  );
};
