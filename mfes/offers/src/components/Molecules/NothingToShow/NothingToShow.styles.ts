import { styled } from 'styled-components/native';

import MagnifyingGlassSvgIcon from '../../../../assets/icons/MagnifyingGlassIcon.svg';

export const Container = styled.View`
  flex: 1;
  padding: 24px;
  align-items: center;
  justify-content: center;
  background-color: white;
`;

export const MagnifyingGlassIcon = styled(MagnifyingGlassSvgIcon)`
  width: 64px;
  height: 64px;
`;

export const Heading = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 20px;
  line-height: 30px;
  letter-spacing: 0.15px;
  color: ${({ theme }) => theme.offersMfe.color.black};
  margin-vertical: 12px;
`;

export const Description = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.light};
  font-size: 16px;
  text-align: center;
  line-height: 28px;
  letter-spacing: 0.15px;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;
