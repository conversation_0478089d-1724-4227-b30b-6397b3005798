import { Header } from '@bp/ui-components/mobile/core';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import React, { useCallback } from 'react';
import { BackHandler } from 'react-native';

import { useTranslations } from '../../../state/Translations/Translations.context';

export type AddNewOfferScreenHeaderProps = {
  onPress?: () => void;
};

export const AddNewOfferScreenHeader = ({
  onPress,
}: AddNewOfferScreenHeaderProps) => {
  const t = useTranslations();
  const navigation = useNavigation();

  useFocusEffect(
    useCallback(() => {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          onPress?.();
          return true;
        },
      );

      return () => backHandler.remove();
    }, [onPress]),
  );

  return (
    <Header
      border={false}
      title={t.ADD_OFFER_CODE}
      hasLeftButton={navigation.canGoBack()}
      onLeftButtonClick={onPress}
    />
  );
};
