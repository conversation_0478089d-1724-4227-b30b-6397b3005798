import { styled } from 'styled-components/native';

import MagnifyingGlassSvg from '../../../../assets/icons/MagnifyingGlassIcon.svg';

export const Container = styled.View`
  flex: 1;
  justify-content: center;
  background-color: ${({ theme }) => theme.offersMfe.color.white};
  align-items: center;
`;

export const Title = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 20px;
  text-align: center;
  line-height: 30px;
  letter-spacing: 0.15px;
  margin: 12px 0;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const Label = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 16px;
  text-align: center;
  line-height: 28px;
  letter-spacing: 0.15px;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const MagnifyingGlassIcon = styled(MagnifyingGlassSvg)`
  width: 64px;
  height: 64px;
`;
