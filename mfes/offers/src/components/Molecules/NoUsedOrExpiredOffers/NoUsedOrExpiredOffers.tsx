import React from 'react';

import { useTranslations } from '../../../state/Translations/Translations.context';
import * as S from './NoUsedOrExpiredOffers.styles';

export const NoUsedOrExpiredOffers = () => {
  const t = useTranslations();

  return (
    <S.Container testID="NoUsedOrExpiredOffers">
      <S.MagnifyingGlassIcon />
      <S.Title testID="NoUsedOrExpiredOffers.Title">
        {t.NO_USED_OR_EXPIRED_OFFERS}
      </S.Title>
      <S.Label testID="NoUsedOrExpiredOffers.Label">
        {t.THERE_IS_NOTHING_TO_SHOW_YOU_RIGHT_NOW}
      </S.Label>
    </S.Container>
  );
};
