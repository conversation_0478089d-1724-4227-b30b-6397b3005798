import React from 'react';

import { Offer } from '../../../types/Offer';
import { OfferDescription } from '../../Atoms/OfferDescription/OfferDescription';
import { OfferMonthsRemaining } from '../../Atoms/OfferMonthsRemaining/OfferMonthsRemaining';
import { OfferQueueDescription } from '../../Atoms/OfferQueueDescription/OfferQueueDescription';
import { BaseOfferCard } from '../BaseOfferCard/BaseOfferCard';

export type SubscriptionOfferCardProps = {
  offer: Offer;
  selected?: boolean;
  FallbackPill?: React.ReactNode;
  showMonthsRemaining?: boolean;
};

export const SubscriptionOfferCard = ({
  offer,
  selected = true,
  FallbackPill,
  showMonthsRemaining = true,
}: SubscriptionOfferCardProps) => {
  return (
    <BaseOfferCard
      testID="SubscriptionOfferCard"
      offer={offer}
      FallbackPill={FallbackPill}
      selected={selected}
    >
      <OfferDescription offer={offer} />
      <OfferQueueDescription offer={offer} />
      {showMonthsRemaining && <OfferMonthsRemaining offer={offer} />}
    </BaseOfferCard>
  );
};
