import { StackScreenProps } from '@react-navigation/stack';

import { OffersScreenName } from '../../Screens/OffersScreenName';

export type OffersStackParamList = {
  [OffersScreenName.ADD_NEW_OFFER_SCREEN]?: {
    // Deeplink params can be null
    offerCode?: string | null;
  };
  [OffersScreenName.ADD_OFFER_ERROR_SCREEN]: undefined;
  [OffersScreenName.ADD_OFFER_PENDING_CANCELLATION]: {
    cancelledOn?: string | null;
  };
  [OffersScreenName.ADD_OFFER_SUCCESS_SCREEN]: undefined;
  [OffersScreenName.ARCHIVED_OFFERS_SCREEN]: undefined;
  [OffersScreenName.COMING_SOON_SCREEN]: undefined;
  [OffersScreenName.LOGIN_SCREEN]: undefined;
  [OffersScreenName.NO_CONNECTION_SCREEN]: undefined;
  [OffersScreenName.SUBSCRIPTION_REQUIRED_SCREEN]: {
    offerCode: string;
  };
  [OffersScreenName.USER_OFFERS_SCREEN]?: {
    reload?: boolean;
  };
};

export type OffersScreenProps<T extends keyof OffersStackParamList> =
  StackScreenProps<OffersStackParamList, T>;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends OffersStackParamList {}
  }
}
