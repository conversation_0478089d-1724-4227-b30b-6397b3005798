import React from 'react';

import * as OffersByUserQuery from '../../../graphql/hooks/useGetOffersByUserQuery';
import { mockGetOffersByUserQueryResult } from '../../../graphql/mock-responses/queries/GetOffersByUserQuery';
import {
  SettingsSeed,
  SettingsSeedProps,
} from '../../../state/Settings/Settings.seed';
import { UserCountry } from '../../../types/UserCountry';
import { UserType } from '../../../types/UserType';
import { render, screen } from '../../../utils/testing';
import { OffersNavigator } from './OffersNavigator';

jest
  .spyOn(OffersByUserQuery, 'useGetOffersByUserQuery')
  .mockReturnValue(async () => mockGetOffersByUserQueryResult.getOffersByUser);

const onExitMfe = jest.fn();

const renderComponent = (userInfo: SettingsSeedProps['userInfo'] = {}) =>
  render(
    <SettingsSeed userInfo={userInfo} onExitMfe={onExitMfe}>
      <OffersNavigator />
    </SettingsSeed>,
  );

beforeEach(() => jest.clearAllMocks());

describe('<OffersNavigator />', () => {
  it('should show LoginScreen for guest users', () => {
    renderComponent({ loggedIn: false });

    const loginScreen = screen.getByTestId('LoginScreen');
    expect(loginScreen).toBeTruthy();
  });

  it('should show ComingSoonScreen for legacy users', () => {
    renderComponent({
      loggedIn: true,
      userType: UserType.PAYG,
    });

    const comingSoonScreen = screen.getByTestId('ComingSoonScreen');
    expect(comingSoonScreen).toBeTruthy();
  });

  it('should show ComingSoonScreen for non-UK users', () => {
    renderComponent({
      loggedIn: true,
      userCountry: UserCountry.NL,
      userType: UserType.PAYG_WALLET,
    });

    const comingSoonScreen = screen.getByTestId('ComingSoonScreen');
    expect(comingSoonScreen).toBeTruthy();
  });

  it('should show UserOffersScreen for UK wallet users', () => {
    renderComponent({
      loggedIn: true,
      userCountry: UserCountry.UK,
      userType: UserType.PAYG_WALLET,
    });

    const userOffersScreen = screen.getByTestId('UserOffersScreen');
    expect(userOffersScreen).toBeTruthy();
  });

  it('should call onExitMfe on unmount', () => {
    renderComponent();

    screen.unmount();
    expect(onExitMfe).toHaveBeenCalledTimes(1);
  });
});
