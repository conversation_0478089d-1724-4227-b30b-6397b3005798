import React from 'react';

import {
  SettingsSeed,
  SettingsSeedProps,
} from '../../../state/Settings/Settings.seed';
import { render, screen } from '../../../utils/testing';
import { OffersMfe } from './OffersMfe';

beforeEach(() => {
  jest.clearAllMocks();
});

const renderComponent = (userInfo?: SettingsSeedProps['userInfo']) =>
  render(
    <SettingsSeed userInfo={userInfo}>
      <OffersMfe />
    </SettingsSeed>,
  );

describe('<OffersMfe />', () => {
  it('should show loading spinner when loading', () => {
    renderComponent({ loading: true });

    const loadingSpinner = screen.getByTestId('LoadingSpinner');
    expect(loadingSpinner).toBeTruthy();
  });

  it('should hide loading spinner when not loading', () => {
    renderComponent({ loading: false });

    const loadingSpinner = screen.queryByTestId('LoadingSpinner');
    expect(loadingSpinner).toBeNull();
  });
});
