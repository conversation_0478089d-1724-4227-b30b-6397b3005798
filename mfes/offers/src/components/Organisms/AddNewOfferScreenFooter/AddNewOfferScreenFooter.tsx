import {
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo } from 'react';

import { useSettings } from '../../../state/Settings/Settings.context';
import { useTranslations } from '../../../state/Translations/Translations.context';
import { OffersScreenName } from '../../Screens/OffersScreenName';
import * as S from './AddNewOfferScreenFooter.styles';

type ActionType = 'add' | 'switch' | 'keep';

export type AddNewOfferScreenFooterProps = {
  action: ActionType;
  onAdd: () => void;
  onSwitch: () => void;
  disabled: boolean;
};

export const AddNewOfferScreenFooter = (
  props: AddNewOfferScreenFooterProps,
) => {
  const { action, onAdd, onSwitch, disabled } = props;

  const t = useTranslations();
  const navigation = useNavigation();
  const { isInternetReachable } = useSettings();

  const onKeepPress = useCallback(() => {
    navigation.navigate(OffersScreenName.USER_OFFERS_SCREEN);
  }, [navigation]);

  const { buttonText, onPress } = useMemo(() => {
    const buttonTextOptions: Record<ActionType, string> = {
      add: t.ADD_OFFER_OR_PROMOTION,
      keep: t.KEEP_CURRENT_OFFER,
      switch: t.SWITCH_TO_NEW_OFFER,
    };
    const onPressOptions: Record<ActionType, () => void> = {
      add: onAdd,
      keep: onKeepPress,
      switch: onSwitch,
    };

    return {
      buttonText: buttonTextOptions[action],
      onPress: onPressOptions[action],
    };
  }, [action, onAdd, onKeepPress, onSwitch, t]);

  return (
    <S.Container testID="AddNewOfferScreenFooter" showShadow={action !== 'add'}>
      <Button
        testID="AddNewOfferScreenFooter.Button"
        type={ButtonAction.PRIMARY}
        onPress={onPress}
        disabled={disabled || !isInternetReachable}
        accessibilityLabel={buttonText}
        accessibilityHint={buttonText}
        size={ButtonSize.DEFAULT}
      >
        {buttonText}
      </Button>
    </S.Container>
  );
};
