import { waitFor } from '@testing-library/react-native';
import React from 'react';

import * as ValidateOfferQueryHook from '../../../graphql/hooks/useValidateOfferQuery';
import { mockValidateOfferQueryResult } from '../../../graphql/mock-responses/queries/ValidateOfferQuery';
import { OfferInvalidReason } from '../../../types/graphql/graphql';
import { fireEvent, render, screen } from '../../../utils/testing';
import {
  OfferCodeValidationInput,
  OfferCodeValidationInputProps,
} from './OfferCodeValidationInput';

const mockValidateOffer = jest
  .fn()
  .mockResolvedValue(mockValidateOfferQueryResult.validateOffer);

jest
  .spyOn(ValidateOfferQueryHook, 'useValidateOfferQuery')
  .mockReturnValue(mockValidateOffer);

const renderComponent = (props: OfferCodeValidationInputProps = {}) =>
  render(<OfferCodeValidationInput {...props} />);

describe('<OfferCodeValidationInput />', () => {
  beforeEach(() => {
    jest.clearAllMocks(); // Clear mocks between tests to reset spy state
  });

  it('should call onCodeChange when user changes the offer code', () => {
    const onCodeChange = jest.fn();
    renderComponent({ onCodeChange });

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '0123456789');

    expect(onCodeChange).toHaveBeenCalled();
  });

  it('should call onValidCode when offer code is valid', async () => {
    const onValidCode = jest.fn();
    renderComponent({ onValidCode });

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, 'abc0123456789');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    await waitFor(() => expect(onValidCode).toHaveBeenCalled());
    expect(mockValidateOffer).toHaveBeenCalledWith({
      variables: { offerCode: 'ABC0123456789' },
    });
  });

  it('should show error message when offer code has special characters', () => {
    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '12345678-');

    const errorText = screen.queryByTestId(
      'OfferCodeValidationInput.ErrorMessage',
    );
    expect(errorText).not.toBeNull();
  });

  it('should show error message when offer code length is less than 8', () => {
    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '1234567');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    const errorText = screen.queryByTestId(
      'OfferCodeValidationInput.ErrorMessage',
    );
    expect(errorText).not.toBeNull();
  });

  it('should show error message when offer code length is greater than 20', () => {
    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '012345678901234567891');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    const errorText = screen.queryByTestId(
      'OfferCodeValidationInput.ErrorMessage',
    );
    expect(errorText).not.toBeNull();
  });

  it('should show error message when offer code is invalid', async () => {
    mockValidateOffer.mockResolvedValueOnce({
      isValid: false,
      reason: OfferInvalidReason.OFFER_NOT_FOUND,
    });

    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '0123456789');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    await screen.findByTestId('OfferCodeValidationInput.ErrorMessage');
  });

  it('should show error message when offer code fails to validate', async () => {
    mockValidateOffer.mockRejectedValueOnce(new Error());
    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '0123456789');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    await screen.findByTestId('OfferCodeValidationInput.ErrorMessage');
  });

  it('should clear error message when offer code changes', () => {
    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '123');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    expect(
      screen.queryByTestId('OfferCodeValidationInput.ErrorMessage'),
    ).not.toBeNull();

    fireEvent.changeText(input, '1234');
    expect(
      screen.queryByTestId('OfferCodeValidationInput.ErrorMessage'),
    ).toBeNull();
  });

  it('should clear valid state when offer code changes', async () => {
    renderComponent();

    const input = screen.getByTestId('OfferCodeTextInput');
    fireEvent.changeText(input, '123456789');

    const checkButton = screen.getByTestId('CheckButton');
    fireEvent.press(checkButton);

    await screen.findByTestId('OfferCodeInput.CircleCheckIcon');

    fireEvent.changeText(input, '0123456789');
    expect(screen.queryByTestId('OfferCodeInput.CircleCheckIcon')).toBeNull();
  });

  it('should auto-validate when initial offer code is set', () => {
    renderComponent({ initialValue: '123456789' });

    const input = screen.getByTestId('OfferCodeTextInput');
    expect(input.props.value).toBe('123456789');

    expect(mockValidateOffer).toHaveBeenCalled();
  });
});
