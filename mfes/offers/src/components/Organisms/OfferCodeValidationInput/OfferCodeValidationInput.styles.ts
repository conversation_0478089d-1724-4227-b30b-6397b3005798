import { styled } from 'styled-components/native';

export const Container = styled.View<{ error: boolean }>`
  flex-direction: row;
  gap: 10px;
  margin-bottom: ${({ error }) => (error ? 0 : 10)}px;
`;

export const InputField = styled.View`
  flex: 1;
`;

export const ErrorMessage = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  min-height: 24px;
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.2px;
  color: #e64949;
  margin-top: 3px;
`;
