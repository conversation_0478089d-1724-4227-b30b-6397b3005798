import { styled } from 'styled-components/native';

export const Container = styled.View``;

export const Title = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.1px;
  font-weight: bold;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const Description = styled.Text`
  font-family: ${({ theme }) => theme.offersMfe.text.fontFamily.regular};
  font-size: 13px;
  line-height: 23px;
  letter-spacing: 0.2px;
  color: ${({ theme }) => theme.offersMfe.color.black};
`;

export const RadioGroup = styled.View`
  flex: 1;
  gap: 16px;
  margin-top: 16px;
`;
