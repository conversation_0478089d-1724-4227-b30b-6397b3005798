import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventOffersErrorScreenOpenPayload = {
  error_message: string;
};

export type OffersAnalyticsEventOffersErrorScreenOpenType = {
  type: OffersAnalyticsEvent.OFFERS_ERROR_SCREEN_OPEN;
  payload: OffersAnalyticsEventOffersErrorScreenOpenPayload;
};

export const OffersAnalyticsEventOffersErrorScreenOpen = (
  payload: OffersAnalyticsEventOffersErrorScreenOpenPayload,
): OffersAnalyticsEventOffersErrorScreenOpenType => ({
  type: OffersAnalyticsEvent.OFFERS_ERROR_SCREEN_OPEN,
  payload,
});
