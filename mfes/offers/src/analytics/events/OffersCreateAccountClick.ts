import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventOffersCreateAccountClickPayload = Record<
  string,
  never
>;

export type OffersAnalyticsEventOffersCreateAccountClickType = {
  type: OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_CLICK;
  payload: OffersAnalyticsEventOffersCreateAccountClickPayload;
};

export const OffersAnalyticsEventOffersCreateAccountClick = (
  payload: OffersAnalyticsEventOffersCreateAccountClickPayload = {},
): OffersAnalyticsEventOffersCreateAccountClickType => ({
  type: OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_CLICK,
  payload,
});
