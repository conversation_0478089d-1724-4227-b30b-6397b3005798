import { OfferType } from '../../types/graphql/graphql';
import { UserCountry } from '../../types/UserCountry';
import { UserType } from '../../types/UserType';
import { OffersAnalyticsEvent } from '..';
import {
  OffersAnalyticsEventAddNewOfferCheckClick,
  OffersAnalyticsEventAddNewOfferCheckInvalid,
  OffersAnalyticsEventAddNewOfferCheckValid,
  OffersAnalyticsEventAddNewOfferConfirmClick,
  OffersAnalyticsEventAddNewOfferScreenOpen,
  OffersAnalyticsEventErrorOfferNotAddedOpen,
  OffersAnalyticsEventErrorOfferNotGoBackClick,
  OffersAnalyticsEventOfferAddedSuccessOpen,
  OffersAnalyticsEventOffersComingSoonOpen,
  OffersAnalyticsEventOffersCreateAccountClick,
  OffersAnalyticsEventOffersLegacyPaygSubsAndSaveClick,
  OffersAnalyticsEventOffersLegacySubsMySubscriptionClick,
  OffersAnalyticsEventOffersLoginClick,
  OffersAnalyticsEventOffersLoginCreateAccountOpen,
  OffersAnalyticsEventOffersScreenActiveOpen,
  OffersAnalyticsEventOffersScreenNewOfferClick,
  OffersAnalyticsEventOffersScreenSubscribeClick,
  OffersAnalyticsEventOffersScreenTCsClick,
  OffersAnalyticsEventOffersScreenUsedExpiredClick,
  OffersAnalyticsEventOffersScreenUsedExpiredOpen,
  OffersAnalyticsEventSubsRequiredOpen,
  OffersAnalyticsEventSubsRequiredRemoveClick,
  OffersAnalyticsEventSubsRequiredSetupClick,
} from './index';

describe('Analytic events', () => {
  test('OffersAnalyticsEventAddNewOfferCheckInvalid', () => {
    const x = OffersAnalyticsEventAddNewOfferCheckInvalid({
      error_message: 'error message',
      entered_value: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_INVALID,
      payload: { error_message: 'error message', entered_value: '123' },
    });
  });

  test('OffersAnalyticsEventAddNewOfferCheckValid', () => {
    const x = OffersAnalyticsEventAddNewOfferCheckValid({
      contradicting_offer: false,
      offer_code: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_VALID,
      payload: { contradicting_offer: false, offer_code: '123' },
    });
  });

  test('OffersAnalyticsEventAddNewOfferConfirmClick', () => {
    const x = OffersAnalyticsEventAddNewOfferConfirmClick({
      offer_type: OfferType.SUBS,
      selected_new: 'NA',
      offer_code: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CONFIRM_CLICK,
      payload: {
        offer_type: OfferType.SUBS,
        selected_new: 'NA',
        offer_code: '123',
      },
    });
  });

  test('OffersAnalyticsEventAddNewOfferCheckClick', () => {
    const x = OffersAnalyticsEventAddNewOfferCheckClick({
      entered_value: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_CLICK,
      payload: {
        entered_value: '123',
      },
    });
  });

  test('OffersAnalyticsEventAddNewOfferScreenOpen', () => {
    const x = OffersAnalyticsEventAddNewOfferScreenOpen();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_SCREEN_OPEN,
      payload: {},
    });
  });

  test('OffersAnalyticsEventErrorOfferNotAddedOpen', () => {
    const x = OffersAnalyticsEventErrorOfferNotAddedOpen({
      entered_value: '123',
      error_message: 'error message',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ERROR_OFFER_NOT_ADDED_OPEN,
      payload: {
        entered_value: '123',
        error_message: 'error message',
      },
    });
  });

  test('OffersAnalyticsEventErrorOfferNotGoBackClick', () => {
    const x = OffersAnalyticsEventErrorOfferNotGoBackClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_ERROR_OFFER_NOT_ADDED_GO_BACK_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOfferAddedSuccessOpen', () => {
    const x = OffersAnalyticsEventOfferAddedSuccessOpen({
      offer_code: '123',
      offer_type: OfferType.SUBS,
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_OFFER_ADDED_SUCCESS_OPEN,
      payload: {
        offer_code: '123',
        offer_type: OfferType.SUBS,
      },
    });
  });

  test('OffersAnalyticsEventSubsRequiredOpen', () => {
    const x = OffersAnalyticsEventSubsRequiredOpen({
      offer_code: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_OPEN,
      payload: {
        offer_code: '123',
      },
    });
  });

  test('OffersAnalyticsEventSubsRequiredRemoveClick', () => {
    const x = OffersAnalyticsEventSubsRequiredRemoveClick({
      offer_code: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_REMOVE_CLICK,
      payload: {
        offer_code: '123',
      },
    });
  });

  test('OffersAnalyticsEventSubsRequiredSetupClick', () => {
    const x = OffersAnalyticsEventSubsRequiredSetupClick({
      offer_code: '123',
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_SETUP_CLICK,
      payload: {
        offer_code: '123',
      },
    });
  });

  test('OffersAnalyticsEventOffersCreateAccountClick', () => {
    const x = OffersAnalyticsEventOffersCreateAccountClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersLoginCreateAccountOpen', () => {
    const x = OffersAnalyticsEventOffersLoginCreateAccountOpen();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_OPEN,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersLoginClick', () => {
    const x = OffersAnalyticsEventOffersLoginClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_LOGIN_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersComingSoonOpen', () => {
    const x = OffersAnalyticsEventOffersComingSoonOpen({
      country: UserCountry.UK,
      user_type: UserType.SUBS,
    });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_COMING_SOON_OPEN,
      payload: {
        country: UserCountry.UK,
        user_type: UserType.SUBS,
      },
    });
  });

  test('OffersAnalyticsEventOffersLegacySubsMySubscriptionClick', () => {
    const x = OffersAnalyticsEventOffersLegacySubsMySubscriptionClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_LEGACY_SUBS_MY_SUBSCRIPTION_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersLegacyPAYGSubsAndSaveClick', () => {
    const x = OffersAnalyticsEventOffersLegacyPaygSubsAndSaveClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_LEGACY_PAYG_SUB_AND_SAVE_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersScreenActiveOpen', () => {
    const x = OffersAnalyticsEventOffersScreenActiveOpen({ offer_count: 4 });
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SCREEN_ACTIVE_OPEN,
      payload: {
        offer_count: 4,
      },
    });
  });

  test('OffersAnalyticsEventOffersScreenNewOfferClick', () => {
    const x = OffersAnalyticsEventOffersScreenNewOfferClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SCREEN_NEW_OFFER_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersScreenSubscribeClick', () => {
    const x = OffersAnalyticsEventOffersScreenSubscribeClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SCREEN_SUBSCRIBE_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersScreenUsedExpiredClick', () => {
    const x = OffersAnalyticsEventOffersScreenUsedExpiredClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_CLICK,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersScreenUsedExpiredOpen', () => {
    const x = OffersAnalyticsEventOffersScreenUsedExpiredOpen();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_OPEN,
      payload: {},
    });
  });

  test('OffersAnalyticsEventOffersScreenTCsClick', () => {
    const x = OffersAnalyticsEventOffersScreenTCsClick();
    expect(x).toEqual({
      type: OffersAnalyticsEvent.OFFERS_SCREEN_TCS_CLICK,
      payload: {},
    });
  });
});
