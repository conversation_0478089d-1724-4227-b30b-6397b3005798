import { OfferType } from '../../types/graphql/graphql';
import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventAddNewOfferConfirmClickPayload = {
  offer_type: OfferType;
  selected_new: string;
  offer_code: string;
};

export type OffersAnalyticsEventAddNewOfferConfirmClickType = {
  type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CONFIRM_CLICK;
  payload: OffersAnalyticsEventAddNewOfferConfirmClickPayload;
};

export const OffersAnalyticsEventAddNewOfferConfirmClick = (
  payload: OffersAnalyticsEventAddNewOfferConfirmClickPayload,
): OffersAnalyticsEventAddNewOfferConfirmClickType => ({
  type: OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CONFIRM_CLICK,
  payload,
});
