import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventSubsRequiredRemoveClickPayload = {
  offer_code: string;
};

export type OffersAnalyticsEventSubsRequiredRemoveClickType = {
  type: OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_REMOVE_CLICK;
  payload: OffersAnalyticsEventSubsRequiredRemoveClickPayload;
};

export const OffersAnalyticsEventSubsRequiredRemoveClick = (
  payload: OffersAnalyticsEventSubsRequiredRemoveClickPayload,
): OffersAnalyticsEventSubsRequiredRemoveClickType => ({
  type: OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_REMOVE_CLICK,
  payload,
});
