import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventOffersLegacySubsMySubscriptionClickPayload =
  Record<string, never>;

export type OffersAnalyticsEventOffersLegacySubsMySubscriptionClickType = {
  type: OffersAnalyticsEvent.OFFERS_LEGACY_SUBS_MY_SUBSCRIPTION_CLICK;
  payload: OffersAnalyticsEventOffersLegacySubsMySubscriptionClickPayload;
};

export const OffersAnalyticsEventOffersLegacySubsMySubscriptionClick = (
  payload: OffersAnalyticsEventOffersLegacySubsMySubscriptionClickPayload = {},
): OffersAnalyticsEventOffersLegacySubsMySubscriptionClickType => ({
  type: OffersAnalyticsEvent.OFFERS_LEGACY_SUBS_MY_SUBSCRIPTION_CLICK,
  payload,
});
