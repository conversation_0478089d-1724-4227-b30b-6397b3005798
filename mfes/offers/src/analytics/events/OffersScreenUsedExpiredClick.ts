import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventOffersScreenUsedExpiredClickPayload = Record<
  string,
  never
>;

export type OffersAnalyticsEventOffersScreenUsedExpiredClickType = {
  type: OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_CLICK;
  payload: OffersAnalyticsEventOffersScreenUsedExpiredClickPayload;
};

export const OffersAnalyticsEventOffersScreenUsedExpiredClick = (
  payload: OffersAnalyticsEventOffersScreenUsedExpiredClickPayload = {},
): OffersAnalyticsEventOffersScreenUsedExpiredClickType => ({
  type: OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_CLICK,
  payload,
});
