import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventOffersScreenUsedExpiredOpenPayload = Record<
  string,
  never
>;

export type OffersAnalyticsEventOffersScreenUsedExpiredOpenType = {
  type: OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_OPEN;
  payload: OffersAnalyticsEventOffersScreenUsedExpiredOpenPayload;
};

export const OffersAnalyticsEventOffersScreenUsedExpiredOpen = (
  payload: OffersAnalyticsEventOffersScreenUsedExpiredOpenPayload = {},
): OffersAnalyticsEventOffersScreenUsedExpiredOpenType => ({
  type: OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_OPEN,
  payload,
});
