import { OffersAnalyticsEvent } from '../index';

export type OffersAnalyticsEventOffersScreenTCsClickPayload = Record<
  string,
  never
>;

export type OffersAnalyticsEventOffersScreenTCsClickType = {
  type: OffersAnalyticsEvent.OFFERS_SCREEN_TCS_CLICK;
  payload: OffersAnalyticsEventOffersScreenTCsClickPayload;
};

export const OffersAnalyticsEventOffersScreenTCsClick = (
  payload: OffersAnalyticsEventOffersScreenTCsClickPayload = {},
): OffersAnalyticsEventOffersScreenTCsClickType => ({
  type: OffersAnalyticsEvent.OFFERS_SCREEN_TCS_CLICK,
  payload,
});
