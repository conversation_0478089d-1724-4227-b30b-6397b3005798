import { RetryErrorState } from './RetryError.state';

export enum RetryErrorAction {
  SET_ERROR = 'RetryErrorAction.SET_ERROR',
  CLEAR = 'RetryErrorAction.CLEAR',
}

/* RetryErrorActionSetError */

type RetryErrorActionSetErrorPayloadType = {
  error: NonNullable<RetryErrorState['error']>;
  onRequestRetry: NonNullable<RetryErrorState['onRequestRetry']>;
};

export type RetryErrorActionSetErrorType = {
  type: RetryErrorAction.SET_ERROR;
  payload: RetryErrorActionSetErrorPayloadType;
};

export const RetryErrorActionSetError = (
  payload: RetryErrorActionSetErrorPayloadType,
): RetryErrorActionSetErrorType => ({
  type: RetryErrorAction.SET_ERROR,
  payload,
});

/* RetryErrorActionClear */

type RetryErrorActionClearPayloadType = {};

export type RetryErrorActionClearType = {
  type: RetryErrorAction.CLEAR;
  payload: RetryErrorActionClearPayloadType;
};

export const RetryErrorActionClear = (
  payload: RetryErrorActionClearPayloadType = {},
): RetryErrorActionClearType => ({
  type: RetryErrorAction.CLEAR,
  payload,
});

/* All types */

export type RetryErrorActionType =
  | RetryErrorActionSetErrorType
  | RetryErrorActionClearType;
