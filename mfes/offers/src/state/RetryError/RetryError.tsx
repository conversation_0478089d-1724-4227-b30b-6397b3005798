import React, { useMemo, useReducer } from 'react';

import { RetryErrorContext } from './RetryError.context';
import { RetryErrorReducer } from './RetryError.reducer';
import { retryErrorStateInitial } from './RetryError.state';

export type RetryErrorContextProviderProps = {
  children: React.ReactNode;
};

export const RetryErrorContextProvider = ({
  children,
}: RetryErrorContextProviderProps) => {
  const [retryErrorState, retryErrorDispatch] = useReducer(
    RetryErrorReducer,
    retryErrorStateInitial,
  );

  const value = useMemo(() => {
    return {
      retryErrorState,
      retryErrorDispatch,
    };
  }, [retryErrorState]);

  return (
    <RetryErrorContext.Provider value={value}>
      {children}
    </RetryErrorContext.Provider>
  );
};
