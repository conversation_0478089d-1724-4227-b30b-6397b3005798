import {
  RetryErrorActionClear,
  RetryErrorActionSetError,
} from './RetryError.actions';
import { RetryErrorReducer } from './RetryError.reducer';
import { retryErrorStateInitial } from './RetryError.state';

describe('RetryErrorReducer()', () => {
  describe('RetryErrorAction.SET_ERROR', () => {
    it('should set error', () => {
      const error = new Error();
      const onRequestRetry = jest.fn();

      const alteredState = RetryErrorReducer(
        retryErrorStateInitial,
        RetryErrorActionSetError({ error, onRequestRetry }),
      );

      expect(alteredState.error).toEqual(error);
      expect(alteredState.onRequestRetry).toEqual(onRequestRetry);
    });
  });

  describe('RetryErrorAction.CLEAR', () => {
    it('should clear error', () => {
      const alteredState = RetryErrorReducer(
        {
          ...retryErrorStateInitial,
          error: new Error(),
          onRequestRetry: jest.fn(),
        },
        RetryErrorActionClear(),
      );

      expect(alteredState.error).toBeUndefined();
      expect(alteredState.onRequestRetry).toBeUndefined();
    });
  });
});
