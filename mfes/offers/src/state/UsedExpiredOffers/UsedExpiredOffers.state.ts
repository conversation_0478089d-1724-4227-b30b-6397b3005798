import { Offer } from '../../types/Offer';
import { TrialOffer } from '../../types/TrialOffer';

export type UsedExpiredOffersState = {
  isFetching: boolean;
  offers: Array<Offer>;
  lastEvaluatedKey?: string;
  trialOffer?: TrialOffer;
};

export const usedExpiredOffersStateInitial: UsedExpiredOffersState = {
  isFetching: false,
  offers: [],
  lastEvaluatedKey: undefined,
  trialOffer: undefined,
};
