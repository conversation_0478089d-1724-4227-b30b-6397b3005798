import React, { createContext, useContext } from 'react';

import { UsedExpiredOffersActionType } from './UsedExpiredOffers.actions';
import {
  UsedExpiredOffersState,
  usedExpiredOffersStateInitial,
} from './UsedExpiredOffers.state';

export type UsedExpiredOffersContextType = {
  usedExpiredOffersState: UsedExpiredOffersState;
  usedExpiredOffersDispatch: React.Dispatch<UsedExpiredOffersActionType>;
};

export const usedExpiredOffersContextInitial: UsedExpiredOffersContextType = {
  usedExpiredOffersState: usedExpiredOffersStateInitial,
  usedExpiredOffersDispatch: () => {
    /* noop */
  },
};

export const UsedExpiredOffersContext =
  createContext<UsedExpiredOffersContextType>(usedExpiredOffersContextInitial);

export const useUsedExpiredOffers = () => useContext(UsedExpiredOffersContext);

export const useUsedExpiredOffersState = () =>
  useUsedExpiredOffers().usedExpiredOffersState;
