import defaultsDeep from 'lodash.defaultsdeep';
import React from 'react';

import * as translations from '../../translations';
import { TranslationShape } from '../../translations/schema';
import { OffersLocales, OffersLocaleType } from '../../types/i18n';
import { useSettings } from '../Settings/Settings.context';
import { TranslationsContext } from './Translations.context';

const translationMap: Record<OffersLocaleType, TranslationShape> = {
  [OffersLocales.DE_DE]: translations.de_DE as unknown as TranslationShape,
  [OffersLocales.EN_GB]: translations.en_GB as unknown as TranslationShape,
  [OffersLocales.EN_US]: translations.en_US as unknown as TranslationShape,
  [OffersLocales.NL_NL]: translations.nl_NL as unknown as TranslationShape,
  [OffersLocales.ES_ES]: translations.es_ES as unknown as TranslationShape,
  [OffersLocales.FR_FR]: translations.fr_FR as unknown as TranslationShape,
  [OffersLocales.ZZ]: translations.zz,
  [OffersLocales.TEST]: translations.messages,
};

export type TranslationsContextProviderProps = {
  children?: React.ReactNode;
};

export const TranslationsContextProvider = ({
  children,
}: TranslationsContextProviderProps) => {
  const { locale } = useSettings();
  const trans = defaultsDeep({}, translationMap[locale], translations.messages);

  return (
    <TranslationsContext.Provider value={trans}>
      {children}
    </TranslationsContext.Provider>
  );
};
