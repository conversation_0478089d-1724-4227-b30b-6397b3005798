import React, { useEffect, useMemo, useReducer } from 'react';

import { ViewedOffersActionSetViewedOfferCodes } from './ViewedOffers.actions';
import { ViewedOffersContext } from './ViewedOffers.context';
import {
  loadViewedOffersState,
  saveViewedOffersState,
} from './ViewedOffers.functions';
import { ViewedOffersReducer } from './ViewedOffers.reducer';
import { viewedOffersStateInitial } from './ViewedOffers.state';

export type ViewedOffersContextProviderProps = {
  children: React.ReactNode;
};

export const ViewedOffersContextProvider = ({
  children,
}: ViewedOffersContextProviderProps) => {
  const [viewedOffersState, viewedOffersDispatch] = useReducer(
    ViewedOffersReducer,
    viewedOffersStateInitial,
  );

  const value = useMemo(() => {
    return { viewedOffersState, viewedOffersDispatch };
  }, [viewedOffersState]);

  // Load state from storage
  useEffect(() => {
    (async () => {
      const persistentState = await loadViewedOffersState();

      if (persistentState !== null) {
        viewedOffersDispatch(
          ViewedOffersActionSetViewedOfferCodes(persistentState),
        );
      }
    })();
  }, []);

  // Persist state changes to storage
  useEffect(() => {
    if (viewedOffersState === viewedOffersStateInitial) {
      return; // do not persist initial state
    }

    saveViewedOffersState(viewedOffersState);
  }, [viewedOffersState]);

  return (
    <ViewedOffersContext.Provider value={value}>
      {children}
    </ViewedOffersContext.Provider>
  );
};
