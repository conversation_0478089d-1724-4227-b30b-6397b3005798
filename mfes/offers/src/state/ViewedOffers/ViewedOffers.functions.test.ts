import AsyncStorage from '@react-native-async-storage/async-storage';

import comboOffer from '../../graphql/mock-responses/offers/ComboOffer';
import creditOffer from '../../graphql/mock-responses/offers/CreditOffer';
import subscriptionOffer from '../../graphql/mock-responses/offers/SubscriptionOffer';
import {
  loadViewedOffersState,
  markAsViewed,
  saveViewedOffersState,
} from './ViewedOffers.functions';

describe('state/ViewedOffers/ViewedOffers.functions', () => {
  describe('markAsViewed()', () => {
    it('should set true for each offer code', () => {
      const result = markAsViewed([
        creditOffer.offerCode,
        subscriptionOffer.offerCode,
        comboOffer.offerCode,
      ]);

      expect(result).toEqual({
        [creditOffer.offerCode]: true,
        [subscriptionOffer.offerCode]: true,
        [comboOffer.offerCode]: true,
      });
    });
  });

  describe('loadViewedOffersState()', () => {
    it('should return null when there is not data in storage', async () => {
      const result = await loadViewedOffersState();

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        'OFFERS_MFE_VIEWED_OFFER_STATE',
      );

      expect(result).toBeNull();
    });

    it('should return data when there is data in storage', async () => {
      jest
        .spyOn(AsyncStorage, 'getItem')
        .mockResolvedValueOnce('{"viewedOfferCodes": {"offer-code-1": true}}');

      const result = await loadViewedOffersState();

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        'OFFERS_MFE_VIEWED_OFFER_STATE',
      );

      expect(result).toEqual({
        viewedOfferCodes: {
          'offer-code-1': true,
        },
      });
    });
  });

  describe('saveViewedOffersState()', () => {
    it('should save data in storage', async () => {
      await saveViewedOffersState({
        viewedOfferCodes: {},
      });

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'OFFERS_MFE_VIEWED_OFFER_STATE',
        expect.stringContaining('{"viewedOfferCodes":{}'),
      );
    });
  });
});
