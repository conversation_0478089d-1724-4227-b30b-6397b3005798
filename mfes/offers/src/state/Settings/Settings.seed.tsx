import React from 'react';

import { SettingsContextProvider } from './Settings';
import { SettingsState } from './Settings.state';

export type SettingsSeedProps = Partial<SettingsState> & {
  children?: React.ReactNode;
};

export const SettingsSeed = ({ children, ...props }: SettingsSeedProps) => (
  <SettingsContextProvider apiKey="apiKey" apiURL="apiURL" {...props}>
    {children}
  </SettingsContextProvider>
);
