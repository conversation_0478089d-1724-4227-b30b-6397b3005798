import { createContext, useContext } from 'react';

import { SettingsState, settingsStateInitial } from './Settings.state';

export type SettingsContextType = SettingsState;

export const settingsContextInitial: SettingsContextType = settingsStateInitial;

export const SettingsContext = createContext<SettingsContextType>(
  settingsContextInitial,
);

export const useSettings = () => useContext(SettingsContext);
