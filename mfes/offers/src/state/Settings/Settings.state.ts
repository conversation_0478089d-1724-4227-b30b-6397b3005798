import { OffersAnalyticsEventType } from '../../analytics';
import { OffersLocale, OffersLocaleType } from '../../types/i18n';
import { UserCountry } from '../../types/UserCountry';
import { UserType } from '../../types/UserType';

export type SettingsState = {
  /**
   * The API key used to authenticate `apiURL`
   */
  apiKey: string;

  /**
   * The base URL for API requests
   * MUST begin with HTTPS
   * MUST not have a trailing slash
   * @example https://[domain].bp.com/vx/guest
   */
  apiURL: string;

  /**
   * You can manipulate the functionality of the offers micro front-end by
   * enabling or disabling features in the provider.
   */
  featureFlags: Partial<{
    enableSampleFeature: boolean;
  }>;

  /**
   * introOffer configuration
   */

  introOffer?: {
    subscriptionAmount: number;
  };

  /** Whether the device is connected to the internet */
  isInternetReachable: boolean;

  /**
   * Controls the language and formatting of data (e.g.: currency, dates)
   */
  locale: OffersLocaleType;

  /**
   * Called when a trackable-event occurs in the micro front-end
   */
  onAnalyticsEvent: (analyticsEvent: OffersAnalyticsEventType) => void;

  /**
   * Called when user leaves the mfe
   */
  onExitMfe: () => void;

  /**
   * Called when user requests to login
   */
  onRequestLogin: () => void;

  /**
   * Called when user requests to register a new account
   */
  onRequestRegister: () => void;

  /**
   * Called when user clicks on My Subcription in the Coming Soon screen
   */
  onNavigateSubscription: () => void;

  /**
   * Called when user requests to finish subscription setup from the SubscriptionRequired screen
   */
  onFinishSubscriptionSetup: (offerCode: string) => void;

  /**
   * Provides link to terms and conditions web page
   */
  termsAndConditionsLink: string;

  /**
   * Controls the users details and user type
   */
  userInfo: {
    /**
     * Loading status indicator
     */
    loading?: boolean;

    /**
     * Controls if a user is logged in or not
     */
    loggedIn?: boolean;

    /**
     * Controls the user id, for api calls
     */
    userId?: string;

    /**
     * Controls the users authentication token, used for any api calls
     */
    getToken?: () => Promise<string | null> | string | null;

    /**
     * Controls the rendering of different screens or text based on the type of account.
     */
    userType?: UserType;

    /**
     * Controls user country
     */
    userCountry?: UserCountry;
  };

  /**
   * Represents the brand associated with the application instance.
   * For example, it could be 'Aral' or 'bp'.
   */
  brand?: string;
};

export const settingsStateInitial: SettingsState = {
  apiKey: '',
  apiURL: '',
  featureFlags: {
    enableSampleFeature: false,
  },
  isInternetReachable: true,
  locale: OffersLocale.ZZ,
  onAnalyticsEvent: () => {
    /* noop */
  },
  onExitMfe: () => {
    /* noop */
  },
  onNavigateSubscription: () => {
    /* noop */
  },
  onRequestLogin: () => {
    /* noop */
  },
  onRequestRegister: () => {
    /* noop */
  },
  onFinishSubscriptionSetup: () => {
    /* noop */
  },
  termsAndConditionsLink: '',
  userInfo: {
    loading: false,
    loggedIn: false,
    userId: undefined,
    getToken: undefined,
    userType: undefined,
    userCountry: undefined,
  },
  introOffer: undefined,
  brand: undefined,
};
