import { graphql } from '../../types/graphql';
import { ApplyOfferMutation } from '../../types/graphql/graphql';
export type { ApplyOfferMutationVariables } from '../../types/graphql/graphql';

export type ApplyOfferMutationResult = ApplyOfferMutation;

export const APPLY_OFFER_MUTATION = graphql(`
  mutation applyOffer(
    $offerCode: String!
    $userId: String
    $updateWalletSubs: Boolean
  ) {
    applyOffer(
      offerCode: $offerCode
      userId: $userId
      updateWalletSubs: $updateWalletSubs
    ) {
      error
      offer {
        offerCode
        offerName
        offerPublicDescription
        offerNotes
        offerType
        offerCountry
        creditAmount
        creditBalance
        currency
        partnerName
        subsDiscount
        subsDuration
        userId
        status

        #computed
        redemptionExpiryDate
        redemptionDate
        expiryDate
      }
    }
  }
`);
