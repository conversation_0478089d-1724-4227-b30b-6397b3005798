import {
  OfferCountry,
  OfferCurrency,
  OfferStatus,
  OfferType,
} from '../../../types/graphql/graphql';
import { Offer } from '../../../types/Offer';

const comboOffer: Offer = {
  offerCode: 'WELSUBSC9M1234',
  offerCountry: OfferCountry.UK,
  offerName: '3 months free and £15 charging credit',
  offerPublicDescription:
    'Your first 3 months of subscription are free and £15 charging credit',
  offerType: OfferType.COMBO,
  currency: OfferCurrency.GBP,
  creditAmount: 15,
  creditBalance: 15,
  subsDiscount: 0,
  subsDuration: 3,
  expiryDate: '2040-12-31T00:00:00.000Z',
  status: OfferStatus.AVAILABLE,
  monthsRemaining: 5,
};

export default comboOffer;
