import { ApolloError, useApolloClient } from '@apollo/client';
import { useCallback } from 'react';

import { useSettings } from '../../state/Settings/Settings.context';
import {
  GET_OFFERS_BY_USER_QUERY,
  GetOffersByUserQueryVariables,
} from '../queries/GET_OFFERS_BY_USER_QUERY';

export type GetOffersByUserQueryOptions = {
  variables?: Omit<GetOffersByUserQueryVariables, 'userId'>;
  abortController?: AbortController;
};

export const useGetOffersByUserQuery = () => {
  const apolloClient = useApolloClient();
  const { userInfo } = useSettings();

  return useCallback(
    async (options?: GetOffersByUserQueryOptions) => {
      const variables = options?.variables ?? {};

      if (!userInfo.userId) {
        throw new Error('Missing userId, check userInfo in OffersProvider.');
      }

      const { data } = await apolloClient.query({
        variables: {
          userId: userInfo.userId,
          pageSize: 100,
          ...variables,
        },
        query: GET_OFFERS_BY_USER_QUERY,
        fetchPolicy: 'no-cache',
        errorPolicy: 'ignore',
        context: {
          fetchOptions: {
            signal: options?.abortController?.signal,
          },
        },
      });

      if (data.getOffersByUser.error) {
        throw new ApolloError({
          errorMessage: data.getOffersByUser.error,
        });
      }

      return data.getOffersByUser;
    },
    [apolloClient, userInfo.userId],
  );
};
