import { useApolloClient } from '@apollo/client';
import { useCallback } from 'react';

import { useSettings } from '../../state/Settings/Settings.context';
import { GetWalletSubscriptionQueryVariables } from '../../types/graphql/graphql';
import { GET_WALLET_SUBSCRIPTION_QUERY } from '../queries/GET_WALLET_SUBSCRIPTION_QUERY';

export type GetWalletSubscriptionQueryOptions = {
  variables?: Omit<GetWalletSubscriptionQueryVariables, 'userId'>;
  abortController?: AbortController;
};
export const useGetWalletSubscriptionQuery = () => {
  const apolloClient = useApolloClient();
  const { userInfo } = useSettings();

  return useCallback(
    async (options?: GetWalletSubscriptionQueryOptions) => {
      if (!userInfo.userId) {
        throw new Error('Missing userId, check userInfo in OffersProvider.');
      }

      const { data } = await apolloClient.query({
        variables: { userId: userInfo.userId },
        query: GET_WALLET_SUBSCRIPTION_QUERY,
        fetchPolicy: 'no-cache',
        errorPolicy: 'ignore',
        context: {
          fetchOptions: {
            signal: options?.abortController?.signal,
          },
        },
      });

      return data.getWalletSubscription;
    },
    [apolloClient, userInfo.userId],
  );
};
