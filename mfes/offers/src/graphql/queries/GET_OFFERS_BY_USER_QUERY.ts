import { graphql } from '../../types/graphql';
import { GetOffersByUserQuery } from '../../types/graphql/graphql';
export type { GetOffersByUserQueryVariables } from '../../types/graphql/graphql';

export type GetOffersByUserQueryResult = GetOffersByUserQuery;

export const GET_OFFERS_BY_USER_QUERY = graphql(`
  query getOffersByUser(
    $userId: String!
    $status: [OfferStatus!]
    $offerType: [OfferType!]
    $sortBy: UserOffersOrder
    $pageSize: Int
    $lastEvaluatedKey: String
  ) {
    getOffersByUser(
      userId: $userId
      status: $status
      offerType: $offerType
      sortBy: $sortBy
      pageSize: $pageSize
      lastEvaluatedKey: $lastEvaluatedKey
    ) {
      offers {
        offerCode
        offerName
        offerPublicDescription
        offerNotes
        offerType
        offerCountry
        creditAmount
        creditBalance
        creditStatus
        currency
        partnerName
        subsDiscount
        subsDuration
        subsStatus
        userId
        status
        queueStatus

        #computed
        redemptionExpiryDate
        redemptionDate
        expiryDate
        monthsRemaining
      }
      lastEvaluatedKey
      maxOfferQuantity
      maxOfferQuantityExceeded
      error
      trialOffer {
        trialStatus
        trialBillingDate
        planDuration
        trialRedemptionDate
      }
    }
  }
`);
