import React, { useCallback } from 'react';
import { ListItem, RadioButton } from '@bp/ui-components/mobile/core';

import { Accordion } from '../../components/Accordion/Accordion';
import { useSandboxApp } from '../../contexts/SandboxApp/SandboxApp.context';
import { SandboxAppActionUserTypeChange } from '../../contexts/SandboxApp/SandboxApp.actions';
import { UserType } from '../../../src';

const userTypes: Record<UserType, string> = {
  [UserType.NEW]: 'NEW (Legacy)',
  [UserType.SUBS]: 'SUBS (Legacy)',
  [UserType.PAYG]: 'PAYG (Legacy)',
  [UserType.PAYG_WALLET]: 'PAYG-WALLET (New)',
  [UserType.SUBS_WALLET]: 'Subs-WALLET (New)',
};

export const SandboxSettingsUserType = () => {
  const { sandboxAppState, sandboxAppDispatch } = useSandboxApp();

  const handleUserTypePress = useCallback(
    (userType: UserType) => {
      sandboxAppDispatch(
        SandboxAppActionUserTypeChange({
          userType,
        }),
      );
    },
    [sandboxAppDispatch],
  );

  return (
    <Accordion title="User type">
      {Object.entries(userTypes).map(([userType, userTypeName]) => (
        <ListItem
          key={userType}
          title={userTypeName}
          onPress={() => handleUserTypePress(userType as UserType)}
          RightComponent={
            <RadioButton
              onPress={() => handleUserTypePress(userType as UserType)}
              selected={sandboxAppState.userInfo?.userType === userType}
            />
          }
        />
      ))}
    </Accordion>
  );
};
