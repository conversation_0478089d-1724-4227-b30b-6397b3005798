import React, { useCallback } from 'react';
import { ListItem, Switch } from '@bp/ui-components/mobile/core';
import { offersProviderDefaultProps } from '../../../src';

import { Accordion } from '../../components/Accordion/Accordion';
import { useSandboxApp } from '../../contexts/SandboxApp/SandboxApp.context';
import { SandboxAppActionFeatureFlagToggle } from '../../contexts/SandboxApp/SandboxApp.actions';

type FeatureFlagsKeys = keyof typeof offersProviderDefaultProps.featureFlags;

const featureFlags = Object.keys(
  offersProviderDefaultProps.featureFlags,
) as Array<FeatureFlagsKeys>;

export const SandboxSettingsFeatureFlags = () => {
  const { sandboxAppState, sandboxAppDispatch } = useSandboxApp();

  const handleFlagPress = useCallback(
    (featureFlag: FeatureFlagsKeys) => {
      sandboxAppDispatch(
        SandboxAppActionFeatureFlagToggle({
          featureFlag,
        }),
      );
    },
    [sandboxAppDispatch],
  );

  return (
    <Accordion title="Feature flags">
      {featureFlags.map((featureFlag) => (
        <ListItem
          key={featureFlag}
          title={featureFlag}
          onPress={() => handleFlagPress(featureFlag)}
          RightComponent={
            <Switch
              onChange={() => handleFlagPress(featureFlag)}
              enabled={sandboxAppState.featureFlags[featureFlag] === true}
            />
          }
        />
      ))}
    </Accordion>
  );
};
