import { OffersLocales, OffersLocaleType, UserCountry } from '../../../src';
import { SandboxAppAction, SandboxAppActionType } from './SandboxApp.actions';
import { SandboxAppState } from './SandboxApp.state';

const COUNTRY_TO_LOCALE: Record<UserCountry, OffersLocaleType> = {
  [UserCountry.UK]: OffersLocales.EN_GB,
  [UserCountry.US]: OffersLocales.EN_US,
  [UserCountry.NL]: OffersLocales.NL_NL,
  [UserCountry.DE]: OffersLocales.DE_DE,
  [UserCountry.ES]: OffersLocales.ES_ES,
};

export function SandboxAppReducer(
  state: SandboxAppState,
  action: SandboxAppActionType,
): SandboxAppState {
  switch (action.type) {
    case SandboxAppAction.FEATURE_FLAG_TOGGLE: {
      const { featureFlags } = state;

      featureFlags[action.payload.featureFlag] =
        !featureFlags[action.payload.featureFlag];

      return {
        ...state,
        featureFlags,
      };
    }

    case SandboxAppAction.LOCALE_CHANGE: {
      return {
        ...state,
        locale: action.payload.locale,
      };
    }

    case SandboxAppAction.USER_TYPE_CHANGE: {
      const { userType } = action.payload;

      return {
        ...state,
        userInfo: {
          ...state.userInfo,
          userType,
        },
      };
    }

    case SandboxAppAction.COUNTRY_CHANGE: {
      const { userCountry } = action.payload;

      return {
        ...state,
        userInfo: {
          ...state.userInfo,
          userCountry,
        },
      };
    }

    case SandboxAppAction.LOGIN: {
      const { userCountry } = action.payload;

      const locale = userCountry
        ? COUNTRY_TO_LOCALE[userCountry]
        : state.locale;

      return {
        ...state,
        locale,
        userInfo: {
          ...state.userInfo,
          ...action.payload,
          loggedIn: true,
        },
      };
    }

    case SandboxAppAction.LOGOUT:
      return {
        ...state,
        userInfo: {
          ...state.userInfo,
          loggedIn: false,
          userId: undefined,
          getToken: undefined,
        },
      };

    default:
      return state;
  }
}
