import React, { useReducer } from 'react';

import { SandboxAppContext } from './SandboxApp.context';
import { SandboxAppReducer } from './SandboxApp.reducer';
import { sandboxAppStateInitial } from './SandboxApp.state';

export type SandboxAppContextProviderProps = {
  children: React.ReactNode;
};

export const SandboxAppContextProvider = ({
  children,
}: SandboxAppContextProviderProps) => {
  const [sandboxAppState, sandboxAppDispatch] = useReducer(
    SandboxAppReducer,
    sandboxAppStateInitial,
  );

  return (
    <SandboxAppContext.Provider value={{ sandboxAppState, sandboxAppDispatch }}>
      {children}
    </SandboxAppContext.Provider>
  );
};
