import { OffersProviderProps, OffersLocaleType } from '../../../src';
import { UserCountry, UserType } from '../../../src';

export enum SandboxAppAction {
  FEATURE_FLAG_TOGGLE = 'SandboxAppAction.FEATURE_FLAG_TOGGLE',
  LOCALE_CHANGE = 'SandboxAppAction.LOCALE_CHANGE',
  USER_TYPE_CHANGE = 'SandboxAppAction.USER_TYPE_CHANGE',
  COUNTRY_CHANGE = 'SandboxAppAction.COUNTRY_CHANGE',
  LOGIN = 'SandboxAppAction.LOGIN',
  LOGOUT = 'SandboxAppAction.LOGOUT',
}

/* SandboxAppActionFeatureFlagToggle */

type SandboxAppActionFeatureFlagTogglePayloadType = {
  featureFlag: keyof NonNullable<OffersProviderProps['featureFlags']>;
};

export type SandboxAppActionFeatureFlagToggleType = {
  type: SandboxAppAction.FEATURE_FLAG_TOGGLE;
  payload: SandboxAppActionFeatureFlagTogglePayloadType;
};

export const SandboxAppActionFeatureFlagToggle = (
  payload: SandboxAppActionFeatureFlagTogglePayloadType,
): SandboxAppActionFeatureFlagToggleType => ({
  type: SandboxAppAction.FEATURE_FLAG_TOGGLE,
  payload,
});

/* SandboxAppActionLocaleChange */

type SandboxAppActionLocaleChangePayloadType = {
  locale: OffersLocaleType;
};

export type SandboxAppActionLocaleChangeType = {
  type: SandboxAppAction.LOCALE_CHANGE;
  payload: SandboxAppActionLocaleChangePayloadType;
};

export const SandboxAppActionLocaleChange = (
  payload: SandboxAppActionLocaleChangePayloadType,
): SandboxAppActionLocaleChangeType => ({
  type: SandboxAppAction.LOCALE_CHANGE,
  payload,
});

/* SandboxAppActionUserTypeChange */

type SandboxAppActionUserTypeChangePayloadType = {
  userType: UserType;
};

export type SandboxAppActionUserTypeChangeType = {
  type: SandboxAppAction.USER_TYPE_CHANGE;
  payload: SandboxAppActionUserTypeChangePayloadType;
};

export const SandboxAppActionUserTypeChange = (
  payload: SandboxAppActionUserTypeChangePayloadType,
): SandboxAppActionUserTypeChangeType => ({
  type: SandboxAppAction.USER_TYPE_CHANGE,
  payload,
});

/* SandboxAppActionCountryChange */

type SandboxAppActionCountryChangePayloadType = {
  userCountry: UserCountry;
};

export type SandboxAppActionCountryChangeType = {
  type: SandboxAppAction.COUNTRY_CHANGE;
  payload: SandboxAppActionCountryChangePayloadType;
};

export const SandboxAppActionCountryChange = (
  payload: SandboxAppActionCountryChangePayloadType,
): SandboxAppActionCountryChangeType => ({
  type: SandboxAppAction.COUNTRY_CHANGE,
  payload,
});

/* SandboxAppActionLogin */

type SandboxAppActionLoginPayloadType = Omit<
  NonNullable<OffersProviderProps['userInfo']>,
  'loading' | 'loggedIn'
>;

export type SandboxAppActionLoginType = {
  type: SandboxAppAction.LOGIN;
  payload: SandboxAppActionLoginPayloadType;
};

export const SandboxAppActionLogin = (
  payload: SandboxAppActionLoginPayloadType = {},
): SandboxAppActionLoginType => ({
  type: SandboxAppAction.LOGIN,
  payload,
});

/* SandboxAppActionLogout */

export type SandboxAppActionLogoutType = {
  type: SandboxAppAction.LOGOUT;
};

export const SandboxAppActionLogout = (): SandboxAppActionLogoutType => ({
  type: SandboxAppAction.LOGOUT,
});

/* Actions */

export type SandboxAppActionType =
  | SandboxAppActionFeatureFlagToggleType
  | SandboxAppActionLocaleChangeType
  | SandboxAppActionUserTypeChangeType
  | SandboxAppActionCountryChangeType
  | SandboxAppActionLoginType
  | SandboxAppActionLogoutType;
