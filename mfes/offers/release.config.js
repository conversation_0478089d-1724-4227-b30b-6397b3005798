module.exports = {
  branches: ['main', 'next'],
  extends: [
    '@bp/pulse-mobile-sdk/release.config.js',
    'semantic-release-monorepo',
  ],
  tagFormat: 'offers/v${version}',
  plugins: [
    [
      'semantic-release-ado',
      {
        varName: 'version',
        setOnlyOnRelease: true,
      },
    ],
    [
      '@semantic-release/commit-analyzer',
      {
        parserOpts: {
          mergePattern:
            /^Merged PR (\d+): (\w*)(?:\(([\w\$\.\-\* ]*)\))?\: (.*)((.|\n)*)/,
          mergeCorrespondence: ['id', 'type', 'scope', 'subject'],
          headerPattern:
            /^Merged PR (\d+): (\w*)(?:\(([\w\$\.\-\* ]*)\))?\: (.*)((.|\n)*)/,
          headerCorrespondence: ['id', 'type', 'scope', 'subject'],
        },
      },
    ],
    ['@semantic-release/commit-analyzer'],
    ['@semantic-release/release-notes-generator'],
    [
      '@semantic-release/changelog',
      {
        changelogFile: 'docs/wiki/CHANGELOG.md',
      },
    ],
    [
      '@semantic-release/exec',
      {
        prepareCmd:
          'yarn version ${nextRelease.version} && git add "../../app/package.json" "../../yarn.lock"',
      },
    ],
    [
      '@semantic-release/npm',
      {
        pkgRoot: 'dist',
      },
    ],
    [
      '@semantic-release/git',
      {
        assets: ['docs/wiki/CHANGELOG.md', 'package.json'],
        message: 'chore(release): ${nextRelease.version} [skip ci]',
      },
    ],
  ],
};
