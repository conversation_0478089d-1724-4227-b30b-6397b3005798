import React from 'react';
import { Svg, Path } from 'react-native-svg';
import {
  Dimensions,
  SafeAreaView,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import {
  createStackNavigator,
  StackNavigationOptions,
} from '@react-navigation/stack';

import { RegistrationMFE } from './sandbox/providers/RegistrationMFE';
import { SandboxAuthProvider } from './sandbox/providers/SandboxAuth';
import { SandboxConfigProvider } from './sandbox/providers/SandboxConfig';
import { RegistrationFlow } from './sandbox/screens/RegistrationFlow/RegistrationFlow';
import { Theme as ThemeProvider } from './src/themes/themes';
import { SandboxScreenName } from './sandbox/common/enums';
import { navigate, navigation } from './sandbox/utils/navigation';

const Stack = createStackNavigator();

const isSmallViewPort = Dimensions.get('screen').width > 320;

const headerTitleStyle: TextStyle = {
  fontSize: 20,
  fontStyle: 'normal',
  fontWeight: 'normal',
  lineHeight: 24,
  paddingRight: isSmallViewPort ? 0 : 20,
};

const defaultScreenOptions: StackNavigationOptions = {
  headerBackTitleVisible: false,
  headerLeft: () => null,
  headerRight: () => null,
  headerShadowVisible: false,
  headerTitleAlign: 'center',
  headerTitleStyle,
  title: '',
};

const Component = () => {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ThemeProvider>
        <SandboxAuthProvider>
          <SandboxConfigProvider>
            <NavigationContainer ref={navigation}>
              <Stack.Navigator
                initialRouteName={SandboxScreenName.REGISTRATION_FLOW}
              >
                <Stack.Screen
                  name={SandboxScreenName.REGISTRATION_FLOW}
                  component={RegistrationFlow}
                  options={{
                    ...defaultScreenOptions,
                    title: 'Registration Flow',
                    headerShown: true,
                    headerRight: () => (
                      <TouchableOpacity
                        style={{ height: 70, width: 60 }}
                        onPress={() =>
                          navigate(
                            SandboxScreenName.REGISTRATION_MFE,
                            {},
                            false,
                          )
                        }
                      >
                        <Svg height="50" width="50" viewBox="0 0 30 30">
                          <Path
                            d="M5 10 L15 10 L15 5 L25 15 L15 25 L15 20 L5 20 Z"
                            fill="#008000"
                            stroke="#008000"
                            strokeWidth="2"
                          />
                        </Svg>
                      </TouchableOpacity>
                    ),
                  }}
                />
                <Stack.Screen
                  name={SandboxScreenName.REGISTRATION_MFE}
                  component={RegistrationMFE}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Navigator>
            </NavigationContainer>
          </SandboxConfigProvider>
        </SandboxAuthProvider>
      </ThemeProvider>
    </SafeAreaView>
  );
};

export default Component;
