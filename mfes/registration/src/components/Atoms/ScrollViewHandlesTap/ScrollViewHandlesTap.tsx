import React from 'react';
import { ScrollView } from 'react-native';

interface IProps {
  spaceBetween?: boolean;
  children: Array<React.ReactNode>;
}

export const ScrollViewHandlesTap = ({ spaceBetween, children }: IProps) => (
  <ScrollView
    contentContainerStyle={{
      flexGrow: 1,
      paddingHorizontal: 24,
      justifyContent: spaceBetween ? 'space-between' : 'flex-start',
    }}
    keyboardShouldPersistTaps="handled"
  >
    {children}
  </ScrollView>
);
