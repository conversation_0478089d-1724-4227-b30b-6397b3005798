import { fireEvent } from '@testing-library/react-native';
import React from 'react';

import { Theme } from '../../../themes/themes';
import { render } from '../../../utils/testing';
import { AlertButton } from './AlertButton';

describe('AlertButton', () => {
  it('accepts a custom test id', () => {
    const text = 'test';
    const customTestId = `custom-test-id`;
    const component = render(
      <Theme>
        <AlertButton text={text} testID={customTestId} />
      </Theme>,
    );

    expect(component.getByTestId(customTestId)).toBeDefined();
  });

  it('generates a test id by appending `-button` to the button text when the `testID` prop is not defined', () => {
    const text = 'test';
    const generatedTestId = `${text}-button`;
    const component = render(
      <Theme>
        <AlertButton text={text} />
      </Theme>,
    );

    expect(component.getByTestId(generatedTestId)).toBeDefined();
  });

  it('should call `onPress` callback when the button is pressed', () => {
    const testId = `on-press-event`;
    const onPressMock = jest.fn();
    const component = render(
      <Theme>
        <AlertButton text={''} testID={testId} onPress={onPressMock} />
      </Theme>,
    );

    fireEvent(component.getByTestId(testId), 'onPress');
    expect(onPressMock).toHaveBeenCalled();
  });
});
