import { styled } from 'styled-components/native';

import { s, vs } from '../../../utils/scale';

export const TextContainer = styled.Text`
  font-family: 'Roboto-Regular';
  font-size: ${s(14)}px;
  line-height: ${vs(21)}px;
  letter-spacing: 0;
  flex: 1;
`;

export const BlackText = styled(TextContainer)`
  color: ${({ theme }) => theme.registrationMfe.color.black};
`;

export const BlueText = styled(TextContainer)`
  color: ${({ theme }) => theme.bpCore.colors.anchor};
`;

export const DisabledText = styled(TextContainer)`
  opacity: 0.5;
`;
