// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Spinner component should render 1`] = `
<Modal
  animationType="none"
  deviceHeight={null}
  deviceWidth={null}
  hardwareAccelerated={false}
  hideModalContentWhileAnimating={false}
  onBackdropPress={[Function]}
  onModalHide={[Function]}
  onModalWillHide={[Function]}
  onModalWillShow={[Function]}
  onRequestClose={[Function]}
  panResponderThreshold={4}
  scrollHorizontal={false}
  scrollOffset={0}
  scrollOffsetMax={0}
  scrollTo={null}
  statusBarTranslucent={false}
  supportedOrientations={
    Array [
      "portrait",
      "landscape",
    ]
  }
  swipeThreshold={100}
  transparent={true}
  visible={true}
>
  <View
    accessibilityState={
      Object {
        "busy": undefined,
        "checked": undefined,
        "disabled": undefined,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      Object {
        "backgroundColor": "black",
        "bottom": 0,
        "height": 1334,
        "left": 0,
        "opacity": 0,
        "position": "absolute",
        "right": 0,
        "top": 0,
        "width": 750,
      }
    }
  />
  <View
    collapsable={false}
    deviceHeight={null}
    deviceWidth={null}
    hideModalContentWhileAnimating={false}
    onBackdropPress={[Function]}
    onModalHide={[Function]}
    onModalWillHide={[Function]}
    onModalWillShow={[Function]}
    panResponderThreshold={4}
    pointerEvents="box-none"
    scrollHorizontal={false}
    scrollOffset={0}
    scrollOffsetMax={0}
    scrollTo={null}
    statusBarTranslucent={false}
    style={
      Object {
        "flex": 1,
        "justifyContent": "center",
        "margin": 37.5,
        "transform": Array [
          Object {
            "translateY": 1334,
          },
        ],
      }
    }
    supportedOrientations={
      Array [
        "portrait",
        "landscape",
      ]
    }
    swipeThreshold={100}
  >
    <ActivityIndicator
      color="#000096"
      size="large"
      style={
        Object {
          "alignItems": "center",
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 1,
          "height": "100%",
          "justifyContent": "center",
          "position": "absolute",
          "width": "100%",
          "zIndex": 999,
        }
      }
      testID="Spinner"
    />
  </View>
</Modal>
`;
