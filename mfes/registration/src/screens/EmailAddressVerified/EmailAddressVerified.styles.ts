import { styled } from 'styled-components/native';

import { s, vs } from '../../utils/scale';

export const ContentContainer = styled.View`
  background-color: white;
  height: 100%;
  padding: ${s(24)}px;
  justify-content: center;
`;

export const ButtonsContainer = styled.View`
  bottom: ${vs(24)}px;
  left: ${s(24)}px;
  position: absolute;
  width: 100%;
`;

export const IconContainer = styled.View`
  bottom: ${vs(24)}px;
  width: 100%;
`;

export const Paragraph = styled.Text`
  text-align: center;
  margin: 0 8px;
  font-size: 16px;
  line-height: 24px;
  color: ${({ theme }) => theme.bpCore.colors.black};
`;

export const LargeTitleText = styled(Paragraph)`
  font-size: 24px;
  line-height: 30px;
  letter-spacing: 0.15px;
  text-align: center;
  font-weight: normal;
`;

export const BodyText = styled(Paragraph)`
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.15px;
  color: ${({ theme }) => theme.bpCore.colors.text};
`;

export const LabelText = styled(Paragraph)`
  font-size: 13px;
  line-height: 24px;
  letter-spacing: 0.15px;
`;

export const LinkButton = styled.Text`
  text-align: center;
  font-size: 14px;
  line-height: 23px;
  letter-spacing: 0.7px;
  color: ${({ theme }) => theme.bpCore.colors.primary[900]};
`;
