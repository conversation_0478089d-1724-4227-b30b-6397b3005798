import { act, fireEvent, screen } from '@testing-library/react-native';
import React from 'react';

import * as messages from '../../../translations/messages.json';
import { render } from '../../../utils/testing';
import { AddEmail } from '../AddEmail';

const mockTranslations = messages;
const mockUseSettingsObj = {
  t: mockTranslations,
  onUserEmailUpdate: () => Promise.resolve({ response: { success: true } }),
  authContext: {
    user: undefined,
    updateEmail: jest.fn(() =>
      Promise.resolve({ response: { success: true } }),
    ),
  },
  onAnalyticsEvent: () => {},
};
const mockUseUserInfoObj = {
  userInfo: {
    email: '<EMAIL>',
  },
  setUserInfo: jest.fn(),
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/SettingsProvider', () => ({
  useSettings: () => mockUseSettings(),
}));
const mockUseUserInfo = jest.fn().mockReturnValue(mockUseUserInfoObj);
jest.mock('../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUseUserInfo(),
}));

const mockUpdatedEmail = '<EMAIL>';

jest.mock('react-native-encrypted-storage', () => {});

jest.mock('react-native-webview', () => {});

describe('AddEmail', () => {
  beforeEach(() => {
    render(<AddEmail />);
  });

  it('renders actionable elements correctly', async () => {
    const input = screen.getByText(mockTranslations.addEmail.emailLabel);

    const saveButton = screen.getByText(mockTranslations.addEmail.saveButton);

    expect(input).toBeDefined();

    expect(saveButton).toBeDefined();
  });

  it('input box has correct accessibility label', () => {
    const saveButton = screen.getByLabelText(
      mockTranslations.addEmail.saveButton,
    );
    expect(saveButton).toBeDefined();
  });

  /*
  Skipping this test as the functionality is actually broken. This needs to be
  fixed but will not be doing it as part of the monorepo migration.
  markDirty prop is missing on the CustomInput component and so the error
  message is never rendered
  */
  it.skip('should validate input', async () => {
    const inputBox = screen.getByPlaceholderText(
      mockTranslations.addEmail.emailPlaceholder,
    );
    const saveButton = screen.getByLabelText(
      mockTranslations.addEmail.saveButton,
    );
    await act(() => fireEvent.changeText(inputBox, '123'));
    await act(() => fireEvent.press(saveButton));

    const emailError = await screen.findByText(
      mockTranslations.addEmail.emailError,
    );
    expect(emailError).toBeTruthy();
  });

  /*
  Skipping this test as it is effectively testing reacts state management and
  multiple other components. Will not apply the fix during the monorepo migration
  but this should be modified to test correctly or removed
  */
  it.skip('should change button state to loading when pressed', async () => {
    const inputBox = screen.getByPlaceholderText(
      mockTranslations.addEmail.emailPlaceholder,
    );
    const saveButton = screen.getByLabelText(
      mockTranslations.addEmail.saveButton,
    );

    await act(() => fireEvent.changeText(inputBox, mockUpdatedEmail));
    await act(() => fireEvent.press(saveButton));

    const loading = screen.getByRole('progressbar');

    expect(loading).toBeDefined();
  });
});
