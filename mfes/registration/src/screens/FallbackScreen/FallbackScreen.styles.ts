import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

export const Container = styled.KeyboardAvoidingView`
  flex: 1;
  background-color: #fff;
  padding: 24px;
  padding-top: 56px;
`;

export const MiddleViewContainer = styled.View`
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 50%;
  padding-top: ${Dimensions.get('screen').height / 5}px;
`;

export const BottomViewContainer = styled.View`
  justify-content: flex-end;
  margin-bottom: 32px;
  height: 50%;
`;

export const BannerText = styled.Text`
  color: #000000;
  font-size: 24px;
  letter-spacing: 1.05px;
  line-height: 24px;
`;

export const SubBannerText = styled.Text`
  color: #000000;
  font-size: 14px;
  letter-spacing: 0.7px;
  line-height: 23px;
  opacity: 0.9;
  text-align: center;
`;
