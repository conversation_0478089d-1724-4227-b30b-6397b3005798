// @ts-nocheck
import { useAppSettings } from '@bp/profile-mfe';
import { fireEvent, screen } from '@testing-library/react-native';
import React from 'react';

import * as messages from '../../translations/messages.json';
import { render } from '../../utils/testing';
import { Screens } from '../index';

const mockTranslations = messages;

const mockStyle = {
  registrationMfe: { buttons: { secondary: { grey: '#111111a2' } } },
};
const mockStyleFn = jest.fn().mockReturnValue(mockStyle);
jest.mock('styled-components', () => ({
  useTheme: () => mockStyleFn(),
}));

const renderWithTheme = () => render(<Screens />);

jest.mock('@bp/profile-mfe', () => ({
  useAppSettings: jest.fn(),
}));

useAppSettings.mockImplementation(() => ({
  userInfo: {
    partnerType: undefined,
  },
}));

describe('Index Screens', () => {
  beforeEach(() => {
    renderWithTheme();
  });
  it('should render without errors', () => {
    const button = screen.getByLabelText(
      mockTranslations.customise.continueButton,
    );
    expect(button).toBeDefined();
  });
  it('should match', () => {
    const mockScreen = renderWithTheme();
    expect(mockScreen.toJSON()).toMatchSnapshot();
  });
  it('should be able to exit', () => {
    const backButton = screen.getByTestId('exitHeaderButton');
    expect(backButton).toBeDefined();

    fireEvent.press(backButton);
  });
});
