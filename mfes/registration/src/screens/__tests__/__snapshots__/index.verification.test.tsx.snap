// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Index Screens should match 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <RNCSafeAreaProvider
    onInsetsChange={[Function]}
    style={
      Object {
        "flex": 1,
      }
    }
  >
    <View
      style={
        Array [
          Object {
            "backgroundColor": "rgb(242, 242, 242)",
            "flex": 1,
          },
          undefined,
        ]
      }
    >
      <View
        collapsable={false}
        pointerEvents="box-none"
        style={
          Object {
            "zIndex": 1,
          }
        }
      >
        <View
          accessibilityElementsHidden={false}
          importantForAccessibility="auto"
          onLayout={[Function]}
          pointerEvents="box-none"
          style={null}
        >
          <View
            collapsable={false}
            pointerEvents="box-none"
            style={
              Object {
                "bottom": 0,
                "left": 0,
                "opacity": 1,
                "position": "absolute",
                "right": 0,
                "top": 0,
                "zIndex": 0,
              }
            }
          >
            <View
              collapsable={false}
              style={
                Object {
                  "backgroundColor": "rgb(255, 255, 255)",
                  "borderBottomColor": "rgb(216, 216, 216)",
                  "borderBottomWidth": 0,
                  "elevation": 0,
                  "flex": 1,
                  "shadowColor": "rgb(216, 216, 216)",
                  "shadowOffset": Object {
                    "height": 0.5,
                    "width": 0,
                  },
                  "shadowOpacity": 0,
                  "shadowRadius": 0,
                }
              }
            />
          </View>
          <View
            collapsable={false}
            pointerEvents="box-none"
            style={
              Object {
                "height": 44,
                "maxHeight": undefined,
                "minHeight": undefined,
                "opacity": undefined,
                "transform": undefined,
              }
            }
          >
            <View
              pointerEvents="none"
              style={
                Object {
                  "height": 0,
                }
              }
            />
            <View
              pointerEvents="box-none"
              style={
                Object {
                  "alignItems": "stretch",
                  "flex": 1,
                  "flexDirection": "row",
                }
              }
            >
              <View
                collapsable={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "alignItems": "flex-start",
                    "flexBasis": 0,
                    "flexGrow": 1,
                    "justifyContent": "center",
                    "marginStart": 0,
                    "opacity": 1,
                  }
                }
              />
              <View
                collapsable={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "justifyContent": "center",
                    "marginHorizontal": 16,
                    "maxWidth": 718,
                    "opacity": 1,
                  }
                }
              >
                <Text
                  accessibilityRole="header"
                  aria-level="1"
                  collapsable={false}
                  numberOfLines={1}
                  onLayout={[Function]}
                  style={
                    Object {
                      "color": "rgb(28, 28, 30)",
                      "fontSize": 20,
                      "fontStyle": "normal",
                      "fontWeight": "normal",
                      "lineHeight": 24,
                      "paddingRight": 0,
                    }
                  }
                />
              </View>
              <View
                collapsable={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "alignItems": "flex-end",
                    "flexBasis": 0,
                    "flexGrow": 1,
                    "justifyContent": "center",
                    "marginEnd": 0,
                    "opacity": 1,
                  }
                }
              >
                <View
                  accessibilityState={
                    Object {
                      "busy": undefined,
                      "checked": undefined,
                      "disabled": undefined,
                      "expanded": undefined,
                      "selected": undefined,
                    }
                  }
                  accessibilityValue={
                    Object {
                      "max": undefined,
                      "min": undefined,
                      "now": undefined,
                      "text": undefined,
                    }
                  }
                  accessible={true}
                  collapsable={false}
                  focusable={true}
                  onClick={[Function]}
                  onResponderGrant={[Function]}
                  onResponderMove={[Function]}
                  onResponderRelease={[Function]}
                  onResponderTerminate={[Function]}
                  onResponderTerminationRequest={[Function]}
                  onStartShouldSetResponder={[Function]}
                  style={
                    Object {
                      "opacity": 1,
                      "paddingRight": 24,
                    }
                  }
                  testID="exitHeaderButton"
                >
                  <RNSVGSvgView
                    align="xMidYMid"
                    bbHeight="24"
                    bbWidth="24"
                    focusable={false}
                    height={24}
                    meetOrSlice={0}
                    minX={0}
                    minY={0}
                    style={
                      Array [
                        Object {
                          "backgroundColor": "transparent",
                          "borderWidth": 0,
                        },
                        Object {
                          "flex": 0,
                          "height": 24,
                          "width": 24,
                        },
                      ]
                    }
                    vbHeight={24}
                    vbWidth={24}
                    width={24}
                  >
                    <RNSVGGroup
                      fill={
                        Object {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                    >
                      <RNSVGGroup
                        fill={null}
                        propList={
                          Array [
                            "fill",
                          ]
                        }
                      >
                        <RNSVGPath
                          d="M6.705 5.646 12 10.941l5.295-5.295a.749.749 0 0 1 1.059 1.06L13.059 12l5.295 5.295a.749.749 0 1 1-1.06 1.059L12 13.059l-5.295 5.295a.749.749 0 1 1-1.059-1.06L10.941 12 5.646 6.705a.749.749 0 1 1 1.06-1.059z"
                          fill={
                            Object {
                              "payload": 4279308561,
                              "type": 0,
                            }
                          }
                          propList={
                            Array [
                              "fill",
                            ]
                          }
                        />
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGSvgView>
                  <Modal
                    animationType="none"
                    deviceHeight={1334}
                    deviceWidth={null}
                    hardwareAccelerated={false}
                    hideModalContentWhileAnimating={false}
                    onBackdropPress={[Function]}
                    onModalHide={[Function]}
                    onModalWillHide={[Function]}
                    onModalWillShow={[Function]}
                    onRequestClose={[Function]}
                    panResponderThreshold={4}
                    scrollHorizontal={false}
                    scrollOffset={0}
                    scrollOffsetMax={0}
                    scrollTo={null}
                    statusBarTranslucent={true}
                    supportedOrientations={
                      Array [
                        "portrait",
                        "landscape",
                      ]
                    }
                    swipeThreshold={100}
                    testID="alert"
                    transparent={true}
                    visible={false}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
      <RNSScreenContainer
        onLayout={[Function]}
        style={
          Object {
            "flex": 1,
          }
        }
      >
        <RNSScreen
          activityState={2}
          collapsable={false}
          gestureResponseDistance={
            Object {
              "bottom": -1,
              "end": -1,
              "start": -1,
              "top": -1,
            }
          }
          pointerEvents="box-none"
          style={
            Object {
              "bottom": 0,
              "left": 0,
              "position": "absolute",
              "right": 0,
              "top": 0,
            }
          }
        >
          <View
            collapsable={false}
            style={
              Object {
                "opacity": 1,
              }
            }
          />
          <View
            accessibilityElementsHidden={false}
            closing={false}
            collapsable={false}
            gestureVelocityImpact={0.3}
            importantForAccessibility="auto"
            onClose={[Function]}
            onGestureBegin={[Function]}
            onGestureCanceled={[Function]}
            onGestureEnd={[Function]}
            onOpen={[Function]}
            onTransition={[Function]}
            pointerEvents="box-none"
            style={
              Array [
                Object {
                  "display": "flex",
                  "overflow": undefined,
                },
                Object {
                  "bottom": 0,
                  "left": 0,
                  "position": "absolute",
                  "right": 0,
                  "top": 0,
                },
              ]
            }
            transitionSpec={
              Object {
                "close": Object {
                  "animation": "spring",
                  "config": Object {
                    "damping": 500,
                    "mass": 3,
                    "overshootClamping": true,
                    "restDisplacementThreshold": 10,
                    "restSpeedThreshold": 10,
                    "stiffness": 1000,
                  },
                },
                "open": Object {
                  "animation": "spring",
                  "config": Object {
                    "damping": 500,
                    "mass": 3,
                    "overshootClamping": true,
                    "restDisplacementThreshold": 10,
                    "restSpeedThreshold": 10,
                    "stiffness": 1000,
                  },
                },
              }
            }
          >
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                Object {
                  "flex": 1,
                }
              }
            >
              <View
                collapsable={false}
                handlerTag={2}
                handlerType="PanGestureHandler"
                needsOffscreenAlphaCompositing={false}
                onGestureHandlerEvent={[Function]}
                onGestureHandlerStateChange={[Function]}
                style={
                  Object {
                    "flex": 1,
                    "transform": Array [
                      Object {
                        "translateX": 0,
                      },
                      Object {
                        "translateX": 0,
                      },
                    ],
                  }
                }
              >
                <View
                  pointerEvents="box-none"
                  style={
                    Array [
                      Object {
                        "flex": 1,
                        "overflow": "hidden",
                      },
                      Array [
                        Object {
                          "backgroundColor": "rgb(242, 242, 242)",
                        },
                        undefined,
                      ],
                    ]
                  }
                >
                  <View
                    style={
                      Object {
                        "flex": 1,
                        "flexDirection": "column-reverse",
                      }
                    }
                  >
                    <View
                      style={
                        Object {
                          "flex": 1,
                        }
                      }
                    >
                      <View
                        style={
                          Object {
                            "backgroundColor": "white",
                            "height": "100%",
                            "justifyContent": "center",
                            "paddingBottom": 48,
                            "paddingLeft": 48,
                            "paddingRight": 48,
                            "paddingTop": 48,
                          }
                        }
                      >
                        <View
                          accessibilityLabel="Image"
                          accessible={true}
                          maxHeight={64}
                          style={
                            Object {
                              "alignItems": "center",
                              "maxHeight": 64,
                              "width": "100%",
                            }
                          }
                          testID="Image"
                        >
                          <SvgMock />
                        </View>
                        <Text
                          accessibilityLabel="Verification link sent"
                          style={
                            Object {
                              "color": "#000000",
                              "fontSize": 24,
                              "fontWeight": "normal",
                              "letterSpacing": 0.15,
                              "lineHeight": 30,
                              "marginBottom": 0,
                              "marginLeft": 8,
                              "marginRight": 8,
                              "marginTop": 0,
                              "textAlign": "center",
                            }
                          }
                          testID="Verification link sent"
                        >
                          Verification link sent
                        </Text>
                        <View
                          style={
                            Object {
                              "height": 25.346,
                              "width": 0,
                            }
                          }
                        />
                        <Text
                          accessibilityLabel="We’ve sent you a verification link to "
                          style={
                            Object {
                              "color": "#000000",
                              "fontSize": 16,
                              "fontWeight": "300",
                              "letterSpacing": 0.15,
                              "lineHeight": 28,
                              "marginBottom": 0,
                              "marginLeft": 8,
                              "marginRight": 8,
                              "marginTop": 0,
                              "textAlign": "center",
                            }
                          }
                          testID="We’ve sent you a verification link to "
                        >
                          We’ve sent you a verification link to 
                          

                          <EMAIL>
                           via email. Please click on the link in the email to continue.
                        </Text>
                        <View
                          style={
                            Object {
                              "height": 26.68,
                              "width": 0,
                            }
                          }
                        />
                        <View
                          accessibilityState={
                            Object {
                              "busy": undefined,
                              "checked": undefined,
                              "disabled": undefined,
                              "expanded": undefined,
                              "selected": undefined,
                            }
                          }
                          accessibilityValue={
                            Object {
                              "max": undefined,
                              "min": undefined,
                              "now": undefined,
                              "text": undefined,
                            }
                          }
                          accessible={true}
                          collapsable={false}
                          focusable={true}
                          onClick={[Function]}
                          onResponderGrant={[Function]}
                          onResponderMove={[Function]}
                          onResponderRelease={[Function]}
                          onResponderTerminate={[Function]}
                          onResponderTerminationRequest={[Function]}
                          onStartShouldSetResponder={[Function]}
                          style={
                            Object {
                              "opacity": 1,
                            }
                          }
                        >
                          <Text
                            style={
                              Object {
                                "color": "#0036ad",
                                "fontSize": 14,
                                "letterSpacing": 0.7,
                                "lineHeight": 23,
                                "textAlign": "center",
                              }
                            }
                          >
                            Change email address
                          </Text>
                        </View>
                        <View
                          style={
                            Object {
                              "height": 39.4864,
                              "width": 0,
                            }
                          }
                        />
                        <View
                          style={
                            Object {
                              "bottom": 39.42857142857143,
                              "left": 48,
                              "position": "absolute",
                              "width": "100%",
                            }
                          }
                        >
                          <View
                            accessibilityState={
                              Object {
                                "busy": undefined,
                                "checked": undefined,
                                "disabled": undefined,
                                "expanded": undefined,
                                "selected": undefined,
                              }
                            }
                            accessibilityValue={
                              Object {
                                "max": undefined,
                                "min": undefined,
                                "now": undefined,
                                "text": undefined,
                              }
                            }
                            accessible={true}
                            collapsable={false}
                            focusable={true}
                            onClick={[Function]}
                            onResponderGrant={[Function]}
                            onResponderMove={[Function]}
                            onResponderRelease={[Function]}
                            onResponderTerminate={[Function]}
                            onResponderTerminationRequest={[Function]}
                            onStartShouldSetResponder={[Function]}
                            style={
                              Object {
                                "opacity": 1,
                              }
                            }
                          >
                            <Text
                              style={
                                Object {
                                  "color": "#0036ad",
                                  "fontSize": 14,
                                  "letterSpacing": 0.7,
                                  "lineHeight": 23,
                                  "textAlign": "center",
                                }
                              }
                            >
                              I have opened the {{brand}} pulse link
                            </Text>
                          </View>
                        </View>
                        <Modal
                          animationType="none"
                          deviceHeight={1334}
                          deviceWidth={null}
                          hardwareAccelerated={false}
                          hideModalContentWhileAnimating={false}
                          onBackdropPress={[Function]}
                          onModalHide={[Function]}
                          onModalWillHide={[Function]}
                          onModalWillShow={[Function]}
                          onRequestClose={[Function]}
                          panResponderThreshold={4}
                          scrollHorizontal={false}
                          scrollOffset={0}
                          scrollOffsetMax={0}
                          scrollTo={null}
                          statusBarTranslucent={true}
                          supportedOrientations={
                            Array [
                              "portrait",
                              "landscape",
                            ]
                          }
                          swipeThreshold={100}
                          testID="alert"
                          transparent={true}
                          visible={true}
                        >
                          <View
                            accessibilityState={
                              Object {
                                "busy": undefined,
                                "checked": undefined,
                                "disabled": undefined,
                                "expanded": undefined,
                                "selected": undefined,
                              }
                            }
                            accessible={true}
                            collapsable={false}
                            focusable={true}
                            onClick={[Function]}
                            onResponderGrant={[Function]}
                            onResponderMove={[Function]}
                            onResponderRelease={[Function]}
                            onResponderTerminate={[Function]}
                            onResponderTerminationRequest={[Function]}
                            onStartShouldSetResponder={[Function]}
                            style={
                              Object {
                                "backgroundColor": "#111111",
                                "bottom": 0,
                                "height": 1334,
                                "left": 0,
                                "opacity": 0,
                                "position": "absolute",
                                "right": 0,
                                "top": 0,
                                "width": 750,
                              }
                            }
                          />
                          <View
                            collapsable={false}
                            deviceHeight={1334}
                            deviceWidth={null}
                            hideModalContentWhileAnimating={false}
                            onBackdropPress={[Function]}
                            onModalHide={[Function]}
                            onModalWillHide={[Function]}
                            onModalWillShow={[Function]}
                            panResponderThreshold={4}
                            pointerEvents="box-none"
                            scrollHorizontal={false}
                            scrollOffset={0}
                            scrollOffsetMax={0}
                            scrollTo={null}
                            statusBarTranslucent={true}
                            style={
                              Object {
                                "flex": 1,
                                "justifyContent": "center",
                                "margin": 37.5,
                                "transform": Array [
                                  Object {
                                    "translateY": 1334,
                                  },
                                ],
                              }
                            }
                            supportedOrientations={
                              Array [
                                "portrait",
                                "landscape",
                              ]
                            }
                            swipeThreshold={100}
                          >
                            <RCTSafeAreaView
                              style={
                                Object {
                                  "alignItems": "center",
                                  "backgroundColor": "#ffffff",
                                  "borderBottomLeftRadius": 5,
                                  "borderBottomRightRadius": 5,
                                  "borderTopLeftRadius": 5,
                                  "borderTopRightRadius": 5,
                                  "justifyContent": "center",
                                  "shadowColor": "rgba(35, 35, 35, 0.26)",
                                  "shadowOffset": Object {
                                    "height": 40,
                                    "width": 0,
                                  },
                                  "shadowOpacity": 1,
                                  "shadowRadius": 30,
                                }
                              }
                            >
                              <Text
                                style={
                                  Array [
                                    Object {
                                      "color": "#111111",
                                      "fontFamily": "Roboto-Regular",
                                      "fontSize": 16,
                                      "letterSpacing": 0.7,
                                      "lineHeight": 24,
                                    },
                                    Object {
                                      "color": "#111111",
                                      "marginBottom": 2,
                                      "marginLeft": 16,
                                      "marginRight": 16,
                                      "marginTop": 24,
                                      "textAlign": "center",
                                    },
                                  ]
                                }
                                testID="alert-title"
                                type="lead"
                              >
                                <Text>
                                  Verification not completed
                                </Text>
                              </Text>
                              <View
                                style={Object {}}
                                testID="alert-body"
                              >
                                <Text
                                  hasTitle={true}
                                  style={
                                    Array [
                                      Object {
                                        "color": "#111111",
                                        "fontFamily": "Roboto-Regular",
                                        "fontSize": 14,
                                        "letterSpacing": 0.7,
                                        "lineHeight": 23,
                                      },
                                      Object {
                                        "color": "#111111CC",
                                        "marginBottom": 24,
                                        "marginLeft": 16,
                                        "marginRight": 16,
                                        "marginTop": 0,
                                        "textAlign": "center",
                                      },
                                    ]
                                  }
                                  testID="alert-content"
                                  type="value"
                                >
                                  <Text>
                                    Sorry, we haven't been able to authenticate your verification from our side. 
 
 Please go back and check the email address you entered is correct or check your spam folder for the verification link and try again.
                                  </Text>
                                </Text>
                              </View>
                              <View
                                style={
                                  Object {
                                    "alignItems": "center",
                                    "borderTopColor": "#ededed",
                                    "borderTopWidth": 1,
                                    "flexDirection": "row",
                                    "height": 56,
                                    "width": "100%",
                                  }
                                }
                                testID="alert-footer"
                              >
                                <View
                                  accessibilityLabel="Okay"
                                  accessibilityRole="button"
                                  accessibilityState={
                                    Object {
                                      "busy": undefined,
                                      "checked": undefined,
                                      "disabled": undefined,
                                      "expanded": undefined,
                                      "selected": undefined,
                                    }
                                  }
                                  accessibilityValue={
                                    Object {
                                      "max": undefined,
                                      "min": undefined,
                                      "now": undefined,
                                      "text": undefined,
                                    }
                                  }
                                  accessible={true}
                                  collapsable={false}
                                  focusable={true}
                                  onClick={[Function]}
                                  onResponderGrant={[Function]}
                                  onResponderMove={[Function]}
                                  onResponderRelease={[Function]}
                                  onResponderTerminate={[Function]}
                                  onResponderTerminationRequest={[Function]}
                                  onStartShouldSetResponder={[Function]}
                                  style={
                                    Object {
                                      "alignItems": "center",
                                      "borderLeftColor": "#ededed",
                                      "borderRightColor": "#ededed",
                                      "borderRightWidth": 1,
                                      "flexBasis": 0,
                                      "flexGrow": 1,
                                      "flexShrink": 1,
                                      "height": 56,
                                      "justifyContent": "center",
                                      "opacity": 1,
                                      "width": "100%",
                                    }
                                  }
                                  testID="Okay-button"
                                >
                                  <Text
                                    color="#111111"
                                    style={
                                      Array [
                                        Object {
                                          "color": "#111111",
                                          "fontFamily": "Roboto-Regular",
                                          "fontSize": 14,
                                          "letterSpacing": 0.7,
                                          "lineHeight": 23,
                                        },
                                        Object {
                                          "color": "#111111",
                                          "fontSize": 15,
                                          "lineHeight": 20,
                                        },
                                      ]
                                    }
                                    type="value"
                                  >
                                    Okay
                                  </Text>
                                </View>
                              </View>
                            </RCTSafeAreaView>
                          </View>
                        </Modal>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </RNSScreen>
      </RNSScreenContainer>
    </View>
  </RNCSafeAreaProvider>
</View>
`;
