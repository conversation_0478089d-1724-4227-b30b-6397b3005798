// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Index Screens should match 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <RNCSafeAreaProvider
    onInsetsChange={[Function]}
    style={
      Object {
        "flex": 1,
      }
    }
  >
    <View
      style={
        Array [
          Object {
            "backgroundColor": "rgb(242, 242, 242)",
            "flex": 1,
          },
          undefined,
        ]
      }
    >
      <View
        collapsable={false}
        pointerEvents="box-none"
        style={
          Object {
            "zIndex": 1,
          }
        }
      >
        <View
          accessibilityElementsHidden={false}
          importantForAccessibility="auto"
          onLayout={[Function]}
          pointerEvents="box-none"
          style={null}
        >
          <View
            collapsable={false}
            pointerEvents="box-none"
            style={
              Object {
                "bottom": 0,
                "left": 0,
                "opacity": 1,
                "position": "absolute",
                "right": 0,
                "top": 0,
                "zIndex": 0,
              }
            }
          >
            <View
              collapsable={false}
              style={
                Object {
                  "backgroundColor": "rgb(255, 255, 255)",
                  "borderBottomColor": "rgb(216, 216, 216)",
                  "borderBottomWidth": 0,
                  "elevation": 0,
                  "flex": 1,
                  "shadowColor": "rgb(216, 216, 216)",
                  "shadowOffset": Object {
                    "height": 0.5,
                    "width": 0,
                  },
                  "shadowOpacity": 0,
                  "shadowRadius": 0,
                }
              }
            />
          </View>
          <View
            collapsable={false}
            pointerEvents="box-none"
            style={
              Object {
                "height": 44,
                "maxHeight": undefined,
                "minHeight": undefined,
                "opacity": undefined,
                "transform": undefined,
              }
            }
          >
            <View
              pointerEvents="none"
              style={
                Object {
                  "height": 0,
                }
              }
            />
            <View
              pointerEvents="box-none"
              style={
                Object {
                  "alignItems": "stretch",
                  "flex": 1,
                  "flexDirection": "row",
                }
              }
            >
              <View
                collapsable={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "alignItems": "flex-start",
                    "flexBasis": 0,
                    "flexGrow": 1,
                    "justifyContent": "center",
                    "marginStart": 0,
                    "opacity": 1,
                  }
                }
              />
              <View
                collapsable={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "justifyContent": "center",
                    "marginHorizontal": 16,
                    "maxWidth": 718,
                    "opacity": 1,
                  }
                }
              >
                <Text
                  accessibilityRole="header"
                  aria-level="1"
                  collapsable={false}
                  numberOfLines={1}
                  onLayout={[Function]}
                  style={
                    Object {
                      "color": "rgb(28, 28, 30)",
                      "fontSize": 20,
                      "fontStyle": "normal",
                      "fontWeight": "normal",
                      "lineHeight": 24,
                      "paddingRight": 0,
                    }
                  }
                >
                  Customise the bp pulse app
                </Text>
              </View>
              <View
                collapsable={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "alignItems": "flex-end",
                    "flexBasis": 0,
                    "flexGrow": 1,
                    "justifyContent": "center",
                    "marginEnd": 0,
                    "opacity": 1,
                  }
                }
              >
                <View
                  accessibilityState={
                    Object {
                      "busy": undefined,
                      "checked": undefined,
                      "disabled": undefined,
                      "expanded": undefined,
                      "selected": undefined,
                    }
                  }
                  accessibilityValue={
                    Object {
                      "max": undefined,
                      "min": undefined,
                      "now": undefined,
                      "text": undefined,
                    }
                  }
                  accessible={true}
                  collapsable={false}
                  focusable={true}
                  onClick={[Function]}
                  onResponderGrant={[Function]}
                  onResponderMove={[Function]}
                  onResponderRelease={[Function]}
                  onResponderTerminate={[Function]}
                  onResponderTerminationRequest={[Function]}
                  onStartShouldSetResponder={[Function]}
                  style={
                    Object {
                      "opacity": 1,
                      "paddingRight": 24,
                    }
                  }
                  testID="exitHeaderButton"
                >
                  <RNSVGSvgView
                    align="xMidYMid"
                    bbHeight="24"
                    bbWidth="24"
                    focusable={false}
                    height={24}
                    meetOrSlice={0}
                    minX={0}
                    minY={0}
                    style={
                      Array [
                        Object {
                          "backgroundColor": "transparent",
                          "borderWidth": 0,
                        },
                        Object {
                          "flex": 0,
                          "height": 24,
                          "width": 24,
                        },
                      ]
                    }
                    vbHeight={24}
                    vbWidth={24}
                    width={24}
                  >
                    <RNSVGGroup
                      fill={
                        Object {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                    >
                      <RNSVGGroup
                        fill={null}
                        propList={
                          Array [
                            "fill",
                          ]
                        }
                      >
                        <RNSVGPath
                          d="M6.705 5.646 12 10.941l5.295-5.295a.749.749 0 0 1 1.059 1.06L13.059 12l5.295 5.295a.749.749 0 1 1-1.06 1.059L12 13.059l-5.295 5.295a.749.749 0 1 1-1.059-1.06L10.941 12 5.646 6.705a.749.749 0 1 1 1.06-1.059z"
                          fill={
                            Object {
                              "payload": 4279308561,
                              "type": 0,
                            }
                          }
                          propList={
                            Array [
                              "fill",
                            ]
                          }
                        />
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGSvgView>
                  <Modal
                    animationType="none"
                    deviceHeight={1334}
                    deviceWidth={null}
                    hardwareAccelerated={false}
                    hideModalContentWhileAnimating={false}
                    onBackdropPress={[Function]}
                    onModalHide={[Function]}
                    onModalWillHide={[Function]}
                    onModalWillShow={[Function]}
                    onRequestClose={[Function]}
                    panResponderThreshold={4}
                    scrollHorizontal={false}
                    scrollOffset={0}
                    scrollOffsetMax={0}
                    scrollTo={null}
                    statusBarTranslucent={true}
                    supportedOrientations={
                      Array [
                        "portrait",
                        "landscape",
                      ]
                    }
                    swipeThreshold={100}
                    testID="alert"
                    transparent={true}
                    visible={false}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
      <RNSScreenContainer
        onLayout={[Function]}
        style={
          Object {
            "flex": 1,
          }
        }
      >
        <RNSScreen
          activityState={2}
          collapsable={false}
          gestureResponseDistance={
            Object {
              "bottom": -1,
              "end": -1,
              "start": -1,
              "top": -1,
            }
          }
          pointerEvents="box-none"
          style={
            Object {
              "bottom": 0,
              "left": 0,
              "position": "absolute",
              "right": 0,
              "top": 0,
            }
          }
        >
          <View
            collapsable={false}
            style={
              Object {
                "opacity": 1,
              }
            }
          />
          <View
            accessibilityElementsHidden={false}
            closing={false}
            collapsable={false}
            gestureVelocityImpact={0.3}
            importantForAccessibility="auto"
            onClose={[Function]}
            onGestureBegin={[Function]}
            onGestureCanceled={[Function]}
            onGestureEnd={[Function]}
            onOpen={[Function]}
            onTransition={[Function]}
            pointerEvents="box-none"
            style={
              Array [
                Object {
                  "display": "flex",
                  "overflow": undefined,
                },
                Object {
                  "bottom": 0,
                  "left": 0,
                  "position": "absolute",
                  "right": 0,
                  "top": 0,
                },
              ]
            }
            transitionSpec={
              Object {
                "close": Object {
                  "animation": "spring",
                  "config": Object {
                    "damping": 500,
                    "mass": 3,
                    "overshootClamping": true,
                    "restDisplacementThreshold": 10,
                    "restSpeedThreshold": 10,
                    "stiffness": 1000,
                  },
                },
                "open": Object {
                  "animation": "spring",
                  "config": Object {
                    "damping": 500,
                    "mass": 3,
                    "overshootClamping": true,
                    "restDisplacementThreshold": 10,
                    "restSpeedThreshold": 10,
                    "stiffness": 1000,
                  },
                },
              }
            }
          >
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                Object {
                  "flex": 1,
                }
              }
            >
              <View
                collapsable={false}
                handlerTag={3}
                handlerType="PanGestureHandler"
                needsOffscreenAlphaCompositing={false}
                onGestureHandlerEvent={[Function]}
                onGestureHandlerStateChange={[Function]}
                style={
                  Object {
                    "flex": 1,
                    "transform": Array [
                      Object {
                        "translateX": 0,
                      },
                      Object {
                        "translateX": 0,
                      },
                    ],
                  }
                }
              >
                <View
                  pointerEvents="box-none"
                  style={
                    Array [
                      Object {
                        "flex": 1,
                        "overflow": "hidden",
                      },
                      Array [
                        Object {
                          "backgroundColor": "rgb(242, 242, 242)",
                        },
                        undefined,
                      ],
                    ]
                  }
                >
                  <View
                    style={
                      Object {
                        "flex": 1,
                        "flexDirection": "column-reverse",
                      }
                    }
                  >
                    <View
                      style={
                        Object {
                          "flex": 1,
                        }
                      }
                    >
                      <View
                        onLayout={[Function]}
                        showCountrySelector={false}
                        style={
                          Object {
                            "backgroundColor": "#FFFFFF",
                            "flexBasis": 0,
                            "flexGrow": 1,
                            "flexShrink": 1,
                            "justifyContent": "space-between",
                            "paddingHorizontal": 12,
                            "paddingTop": 0,
                          }
                        }
                      >
                        <RCTScrollView
                          style={Object {}}
                        >
                          <View>
                            <View
                              style={
                                Object {
                                  "marginHorizontal": 12,
                                }
                              }
                            >
                              <View
                                style={
                                  Object {
                                    "display": "flex",
                                    "flexDirection": "row",
                                  }
                                }
                              >
                                <Text
                                  style={
                                    Object {
                                      "color": "#111111",
                                      "fontSize": 28,
                                      "letterSpacing": 1.4,
                                      "lineHeight": 42,
                                      "marginBottom": 13.142857142857142,
                                      "marginTop": 32.857142857142854,
                                    }
                                  }
                                >
                                  Enter your first name
                                </Text>
                              </View>
                              <View
                                style={
                                  Object {
                                    "alignItems": "center",
                                    "flexDirection": "row",
                                    "position": "relative",
                                  }
                                }
                              >
                                <TextInput
                                  accessibilityLabel="Enter your first name"
                                  autoCapitalize="words"
                                  borderColor="default"
                                  editable={true}
                                  keyboardType="default"
                                  onBlur={[Function]}
                                  onChangeText={[Function]}
                                  onFocus={[Function]}
                                  placeholder="first name"
                                  style={
                                    Object {
                                      "borderColor": "#dedede",
                                      "borderWidth": 2,
                                      "flexBasis": 0,
                                      "flexGrow": 1,
                                      "flexShrink": 1,
                                      "fontSize": 32,
                                      "height": 90,
                                      "letterSpacing": 1.4,
                                      "opacity": 1,
                                      "paddingHorizontal": 32,
                                      "paddingRight": 72,
                                      "paddingVertical": 19.714285714285715,
                                    }
                                  }
                                  testID="Enter your first name"
                                  value="first name"
                                />
                              </View>
                              <View
                                style={
                                  Object {
                                    "display": "flex",
                                    "flexDirection": "row",
                                  }
                                }
                              >
                                <Text
                                  style={
                                    Object {
                                      "color": "#111111",
                                      "fontSize": 28,
                                      "letterSpacing": 1.4,
                                      "lineHeight": 42,
                                      "marginBottom": 13.142857142857142,
                                      "marginTop": 32.857142857142854,
                                    }
                                  }
                                >
                                  Enter your last name
                                </Text>
                              </View>
                              <View
                                style={
                                  Object {
                                    "alignItems": "center",
                                    "flexDirection": "row",
                                    "position": "relative",
                                  }
                                }
                              >
                                <TextInput
                                  accessibilityLabel="Enter your last name"
                                  autoCapitalize="words"
                                  borderColor="default"
                                  editable={true}
                                  keyboardType="default"
                                  onBlur={[Function]}
                                  onChangeText={[Function]}
                                  onFocus={[Function]}
                                  placeholder="last name"
                                  style={
                                    Object {
                                      "borderColor": "#dedede",
                                      "borderWidth": 2,
                                      "flexBasis": 0,
                                      "flexGrow": 1,
                                      "flexShrink": 1,
                                      "fontSize": 32,
                                      "height": 90,
                                      "letterSpacing": 1.4,
                                      "opacity": 1,
                                      "paddingHorizontal": 32,
                                      "paddingRight": 72,
                                      "paddingVertical": 19.714285714285715,
                                    }
                                  }
                                  testID="Enter your last name"
                                  value="last name"
                                />
                              </View>
                              <View
                                style={
                                  Object {
                                    "height": 30,
                                    "width": 1,
                                  }
                                }
                              />
                              <View
                                showBorder={false}
                                style={
                                  Object {
                                    "borderColor": "#FFFFFF",
                                    "borderWidth": 1,
                                    "marginHorizontal": 12,
                                    "marginVertical": 0,
                                    "paddingHorizontal": 0,
                                    "paddingVertical": 6,
                                  }
                                }
                              >
                                <View
                                  style={
                                    Object {
                                      "flexDirection": "row",
                                      "flexWrap": "nowrap",
                                      "marginVertical": 13.142857142857142,
                                      "width": "100%",
                                    }
                                  }
                                >
                                  <View
                                    accessibilityLabel="I would like to receive general & personalised communications and offers from bp pulse"
                                    accessibilityRole="checkbox"
                                    accessibilityState={
                                      Object {
                                        "busy": undefined,
                                        "checked": undefined,
                                        "disabled": undefined,
                                        "expanded": undefined,
                                        "selected": undefined,
                                      }
                                    }
                                    accessibilityValue={
                                      Object {
                                        "max": undefined,
                                        "min": undefined,
                                        "now": undefined,
                                        "text": undefined,
                                      }
                                    }
                                    accessible={true}
                                    collapsable={false}
                                    focusable={true}
                                    onClick={[Function]}
                                    onResponderGrant={[Function]}
                                    onResponderMove={[Function]}
                                    onResponderRelease={[Function]}
                                    onResponderTerminate={[Function]}
                                    onResponderTerminationRequest={[Function]}
                                    onStartShouldSetResponder={[Function]}
                                    style={
                                      Object {
                                        "backgroundColor": "#FFFFFF",
                                        "borderColor": "#b6b6b6",
                                        "borderStyle": "solid",
                                        "borderWidth": 1,
                                        "height": 24,
                                        "opacity": 1,
                                        "width": 24,
                                      }
                                    }
                                  />
                                  <View
                                    style={
                                      Object {
                                        "display": "flex",
                                        "flexBasis": 0,
                                        "flexDirection": "row",
                                        "flexGrow": 1,
                                        "flexShrink": 1,
                                        "fontFamily": "Roboto-Regular",
                                        "fontSize": 28,
                                        "letterSpacing": 0,
                                        "lineHeight": 34.5,
                                        "marginLeft": 32,
                                      }
                                    }
                                  >
                                    <Text
                                      style={
                                        Object {
                                          "color": "#000000",
                                          "fontFamily": "Roboto-Regular",
                                          "fontSize": 28,
                                          "letterSpacing": 0,
                                          "lineHeight": 34.5,
                                          "width": "100%",
                                        }
                                      }
                                    >
                                      I would like to receive general & personalised communications and offers from bp pulse
                                    </Text>
                                  </View>
                                </View>
                              </View>
                              <View
                                style={
                                  Object {
                                    "marginHorizontal": 12,
                                    "paddingBottom": 24,
                                  }
                                }
                              >
                                <View
                                  style={
                                    Object {
                                      "flexDirection": "row",
                                      "flexWrap": "nowrap",
                                      "marginVertical": 13.142857142857142,
                                      "width": "100%",
                                    }
                                  }
                                >
                                  <View
                                    accessibilityLabel="terms and conditions"
                                    accessibilityRole="checkbox"
                                    accessibilityState={
                                      Object {
                                        "busy": undefined,
                                        "checked": undefined,
                                        "disabled": false,
                                        "expanded": undefined,
                                        "selected": undefined,
                                      }
                                    }
                                    accessibilityValue={
                                      Object {
                                        "max": undefined,
                                        "min": undefined,
                                        "now": undefined,
                                        "text": undefined,
                                      }
                                    }
                                    accessible={true}
                                    collapsable={false}
                                    focusable={true}
                                    onClick={[Function]}
                                    onResponderGrant={[Function]}
                                    onResponderMove={[Function]}
                                    onResponderRelease={[Function]}
                                    onResponderTerminate={[Function]}
                                    onResponderTerminationRequest={[Function]}
                                    onStartShouldSetResponder={[Function]}
                                    style={
                                      Object {
                                        "backgroundColor": "#FFFFFF",
                                        "borderColor": "#b6b6b6",
                                        "borderStyle": "solid",
                                        "borderWidth": 1,
                                        "height": 24,
                                        "opacity": 1,
                                        "width": 24,
                                      }
                                    }
                                    testID="terms and conditions"
                                  />
                                  <View
                                    style={
                                      Object {
                                        "display": "flex",
                                        "flexBasis": 0,
                                        "flexDirection": "row",
                                        "flexGrow": 1,
                                        "flexShrink": 1,
                                        "fontFamily": "Roboto-Regular",
                                        "fontSize": 28,
                                        "letterSpacing": 0,
                                        "lineHeight": 34.5,
                                        "marginLeft": 32,
                                      }
                                    }
                                  >
                                    <Text
                                      style={
                                        Object {
                                          "flexBasis": 0,
                                          "flexGrow": 1,
                                          "flexShrink": 1,
                                          "fontFamily": "Roboto-Regular",
                                          "fontSize": 28,
                                          "letterSpacing": 0,
                                          "lineHeight": 34.5,
                                        }
                                      }
                                    >
                                      <Text
                                        style={
                                          Object {
                                            "color": "#000000",
                                            "flexBasis": 0,
                                            "flexGrow": 1,
                                            "flexShrink": 1,
                                            "fontFamily": "Roboto-Regular",
                                            "fontSize": 28,
                                            "letterSpacing": 0,
                                            "lineHeight": 34.5,
                                          }
                                        }
                                      >
                                        I have read and agreed to the bp pulse 
                                      </Text>
                                      <Text
                                        accessibilityLabel="terms and conditions"
                                        accessibilityRole="button"
                                        accessible={true}
                                        onPress={[Function]}
                                        style={
                                          Object {
                                            "color": "#2b47e6",
                                            "flexBasis": 0,
                                            "flexGrow": 1,
                                            "flexShrink": 1,
                                            "fontFamily": "Roboto-Regular",
                                            "fontSize": 28,
                                            "letterSpacing": 0,
                                            "lineHeight": 34.5,
                                          }
                                        }
                                      >
                                        terms and conditions
                                      </Text>
                                      <Text
                                        style={
                                          Object {
                                            "color": "#000000",
                                            "flexBasis": 0,
                                            "flexGrow": 1,
                                            "flexShrink": 1,
                                            "fontFamily": "Roboto-Regular",
                                            "fontSize": 28,
                                            "letterSpacing": 0,
                                            "lineHeight": 34.5,
                                          }
                                        }
                                      >
                                         and 
                                      </Text>
                                      <Text
                                        accessibilityLabel="privacy policy"
                                        accessibilityRole="button"
                                        accessible={true}
                                        onPress={[Function]}
                                        style={
                                          Object {
                                            "color": "#2b47e6",
                                            "flexBasis": 0,
                                            "flexGrow": 1,
                                            "flexShrink": 1,
                                            "fontFamily": "Roboto-Regular",
                                            "fontSize": 28,
                                            "letterSpacing": 0,
                                            "lineHeight": 34.5,
                                          }
                                        }
                                      >
                                        privacy policy
                                      </Text>
                                      <Text
                                        style={
                                          Object {
                                            "color": "#000000",
                                            "flexBasis": 0,
                                            "flexGrow": 1,
                                            "flexShrink": 1,
                                            "fontFamily": "Roboto-Regular",
                                            "fontSize": 28,
                                            "letterSpacing": 0,
                                            "lineHeight": 34.5,
                                          }
                                        }
                                      >
                                        .
                                      </Text>
                                    </Text>
                                  </View>
                                </View>
                              </View>
                            </View>
                          </View>
                        </RCTScrollView>
                        <Modal
                          animationType="none"
                          deviceHeight={1334}
                          deviceWidth={null}
                          hardwareAccelerated={false}
                          hideModalContentWhileAnimating={false}
                          onBackdropPress={[Function]}
                          onModalHide={[Function]}
                          onModalWillHide={[Function]}
                          onModalWillShow={[Function]}
                          onRequestClose={[Function]}
                          panResponderThreshold={4}
                          scrollHorizontal={false}
                          scrollOffset={0}
                          scrollOffsetMax={0}
                          scrollTo={null}
                          statusBarTranslucent={true}
                          supportedOrientations={
                            Array [
                              "portrait",
                              "landscape",
                            ]
                          }
                          swipeThreshold={100}
                          testID="alert"
                          transparent={true}
                          visible={false}
                        />
                        <View
                          accessibilityHint="Continue"
                          accessibilityLabel="Continue"
                          accessibilityRole="button"
                          accessibilityState={
                            Object {
                              "busy": undefined,
                              "checked": undefined,
                              "disabled": true,
                              "expanded": undefined,
                              "selected": undefined,
                            }
                          }
                          accessibilityValue={
                            Object {
                              "max": undefined,
                              "min": undefined,
                              "now": undefined,
                              "text": undefined,
                            }
                          }
                          accessible={true}
                          collapsable={false}
                          focusable={true}
                          onClick={[Function]}
                          onResponderGrant={[Function]}
                          onResponderMove={[Function]}
                          onResponderRelease={[Function]}
                          onResponderTerminate={[Function]}
                          onResponderTerminationRequest={[Function]}
                          onStartShouldSetResponder={[Function]}
                          style={
                            Object {
                              "alignItems": "center",
                              "backgroundColor": "#afafaf",
                              "borderBottomLeftRadius": 23,
                              "borderBottomRightRadius": 23,
                              "borderColor": "transparent",
                              "borderRadius": 5,
                              "borderStyle": "solid",
                              "borderTopLeftRadius": 23,
                              "borderTopRightRadius": 23,
                              "borderWidth": 0,
                              "justifyContent": "center",
                              "marginBottom": 24,
                              "marginHorizontal": 24,
                              "minHeight": 46,
                              "opacity": 1,
                              "paddingHorizontal": 25.5,
                              "paddingVertical": 12,
                            }
                          }
                          testID="continueButton"
                        >
                          <View>
                            <Text
                              disabled={true}
                              inverted={false}
                              isIconComponentRight={false}
                              size="large"
                              style={
                                Object {
                                  "color": "#ffffff",
                                  "fontFamily": "Roboto-Regular",
                                  "fontSize": 15,
                                  "letterSpacing": 0.7,
                                  "textAlign": "center",
                                  "width": "auto",
                                }
                              }
                              type="primary"
                            >
                              Jump right in!
                            </Text>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </RNSScreen>
      </RNSScreenContainer>
    </View>
  </RNCSafeAreaProvider>
</View>
`;
