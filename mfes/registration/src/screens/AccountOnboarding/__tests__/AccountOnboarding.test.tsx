import { act, fireEvent, screen, waitFor } from '@testing-library/react-native';
import React from 'react';

import { RegAnalyticsEventType } from '../../../analytics';
import { AppCountry, ScreenName } from '../../../common/enums';
import { Theme as ThemeProvider } from '../../../themes/themes';
import * as messages from '../../../translations/messages.json';
import { render } from '../../../utils/testing';
import { AccountOnboarding } from '../AccountOnboarding';

const mockTranslations = messages;

const mockUseSettingsObj = {
  onboardingComplete: false,
  onboardingFailed: true,
  t: mockTranslations,
  country: AppCountry.UK,
  getIdToken: '456d',
  user: {
    userId: 'abc123bp',
    authenticated: false,
    email: '<EMAIL>',
    emailVerified: false,
    firstName: '',
    lastName: '',
    phoneNumber: '',
  },
  onAnalyticsEvent: (event: RegAnalyticsEventType) => console.log(event),
  getAccessToken: () => '1a2b3c',
  onEmailVerificationCompleted: (email: string) => console.log(email),
  onExitMFE: () => {},
  onInitiateOnboarding: jest.fn(),
  onLogout: jest.fn(),
};
const mockUseUserInfoObj = {
  userInfo: {
    phoneNumber: '+*********',
    phoneCountryCode: '+44',
    firstName: 'Tester',
    lastName: 'Bp',
    email: '<EMAIL>',
  },
  clearUserInfo: jest.fn(),
  setUserInfo: jest.fn(),
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/SettingsProvider', () => ({
  useSettings: () => mockUseSettings(),
}));
const mockUseUserInfo = jest.fn().mockReturnValue(mockUseUserInfoObj);
jest.mock('../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUseUserInfo(),
}));

jest.mock('../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => ({ navigate: jest.fn() }),
}));

jest.mock('react-native-encrypted-storage', () => {});

jest.mock('react-native-webview', () => {});

// Bypass navigation props type checking
const navigationProps: any = {};

const renderWithTheme = () =>
  render(
    <ThemeProvider>
      <AccountOnboarding
        route={{
          params: {
            mockTriggerModal: { mockTriggerDisplayOnboardingModal: true },
          },
          key: 'key-AccountOnboarding',
          name: ScreenName.ACCOUNT_ONBOARDING,
        }}
        navigation={navigationProps}
      />
    </ThemeProvider>,
  );

describe('EmailVerification', () => {
  const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

  beforeEach(() => {
    consoleSpy.mockClear();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('Account onboarding - registration flow', () => {
    beforeEach(() => {
      renderWithTheme();
    });

    afterEach(() => {
      mockUseSettingsObj.onInitiateOnboarding.mockClear();
    });

    it('should trigger onInitateOnboarding immediately', async () => {
      await waitFor(() =>
        expect(mockUseSettingsObj.onInitiateOnboarding).toHaveBeenCalledTimes(
          1,
        ),
      );
    });

    it('should log out user on trigger of log out on onboarding modal failure', async () => {
      const logOutButton = screen.getByText(
        mockTranslations.unableToCompleteSetUpAlert.logOutButton,
      );

      expect(logOutButton).toBeDefined();

      await act(async () => fireEvent.press(logOutButton));

      expect(mockUseSettingsObj.onLogout).toHaveBeenCalledTimes(1);
    });

    it('should trigger onInitateOnboarding on onboarding modal try again', async () => {
      const tryAgainButton = screen.getByText(
        mockTranslations.unableToCompleteSetUpAlert.tryAgainButton,
      );

      expect(tryAgainButton).toBeDefined();

      expect(mockUseSettingsObj.onInitiateOnboarding).toHaveBeenCalledTimes(1);

      await act(async () => fireEvent.press(tryAgainButton));

      expect(mockUseSettingsObj.onInitiateOnboarding).toHaveBeenCalledTimes(2);
    });
  });
});
