import { act, fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';

import { ScreenName } from '../../../common/enums';
import { Theme as ThemeProvider } from '../../../themes/themes';
import * as messages from '../../../translations/messages.json';
import { EmailVerification } from '../EmailVerification';

const mockTranslations = messages;

const mockUseSettingsObj = {
  onboardingComplete: false,
  onboardingFailed: false,
  t: mockTranslations,
  onExitMFE: () => {},
  onLogout: () => {},
  authContext: {
    getUser: () => Promise.resolve(() => ({ emailVerified: true })),
    user: {
      email: '<EMAIL>',
    },
    updateEmail: jest.fn(),
  },
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/SettingsProvider', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockNavigate = jest.fn();

jest.mock('../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => ({ navigate: mockNavigate }),
}));

// Bypass navigation props type checking
const navigationProps: any = {};

const renderWithTheme = (registrationFlow: boolean) =>
  render(
    <ThemeProvider>
      <EmailVerification
        route={{
          params: {
            hasPreviousVerificationEmail: false,
            registrationFlow,
            mockTriggerModal: { mockTriggerDisplayOnboardingModal: true },
          },
          key: 'key-EmailVerification',
          name: ScreenName.EMAIL_VERIFICATION,
        }}
        navigation={navigationProps}
      />
    </ThemeProvider>,
  );

describe('EmailVerification', () => {
  const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

  beforeEach(() => {
    consoleSpy.mockClear();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('Email verification - registration flow', () => {
    beforeEach(() => {
      renderWithTheme(false);
    });

    it.skip('should trigger RegAnalyticsEventEmailVerScreenOpen analytics event', async () => {
      expect(console.log).toBeCalledTimes(1);
      expect(console.log).toHaveBeenLastCalledWith({
        payload: {},
        type: 'RegAnalyticsEvent.EMAIL_VER_SCREEN_OPEN',
      });
    });

    it.skip('should trigger RegAnalyticsEventEmailVerScreenVerifiedEmailClick analytics event', async () => {
      const emailVerifiedButton = screen.getByText(
        mockTranslations.emailVerification.emailVerifiedButton,
      );

      await act(async () => fireEvent.press(emailVerifiedButton));

      expect(console.log).toBeCalledTimes(2);
      expect(console.log).toHaveBeenLastCalledWith({
        payload: {},
        type: 'RegAnalyticsEvent.EMAIL_VER_SCREEN_VERIFIED_EMAIL_CLICK',
      });
    });

    it.skip('should trigger RegAnalyticsEventEmailVerScreenChangeEmailClick analytics event', async () => {
      const emailChangeButton = screen.getByText(
        mockTranslations.emailVerification.changeEmailButton,
      );

      await act(async () => fireEvent.press(emailChangeButton));

      expect(console.log).toBeCalledTimes(2);
      expect(console.log).toHaveBeenLastCalledWith({
        payload: {},
        type: 'RegAnalyticsEvent.EMAIL_VER_SCREEN_CHANGE_EMAIL_CLICK',
      });
    });
  });
});
