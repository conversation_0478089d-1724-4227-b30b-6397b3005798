import { act, fireEvent, screen } from '@testing-library/react-native';
import React from 'react';

import { ScreenName } from '../../../common/enums';
import {
  ISettingsContextOutput,
  useSettings,
} from '../../../providers/SettingsProvider';
import { Theme as ThemeProvider } from '../../../themes/themes';
import { ITranslation } from '../../../translations/schema';
import { render } from '../../../utils/testing';
import { EmailVerification } from '../EmailVerification';

// Bypass navigation props type checking
const navigationProps: any = {};

const renderWithTheme = (registrationFlow: boolean) =>
  render(
    <ThemeProvider>
      <EmailVerification
        route={{
          params: {
            hasPreviousVerificationEmail: false,
            registrationFlow,
            mockTriggerModal: { mockTriggerDisplayOnboardingModal: true },
          },
          key: 'key-EmailVerification',
          name: ScreenName.EMAIL_VERIFICATION,
        }}
        navigation={navigationProps}
      />
    </ThemeProvider>,
  );

describe('EmailVerification Failed Scenario', () => {
  const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

  beforeEach(() => {
    consoleSpy.mockClear();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('Email verification - registration flow', () => {
    let defaultSettings: ISettingsContextOutput;
    let t: ITranslation;

    beforeEach(() => {
      defaultSettings = useSettings();
      ({ t } = defaultSettings);

      jest
        .spyOn(require('../../../providers/SettingsProvider'), 'useSettings')
        .mockImplementation(() => defaultSettings);

      renderWithTheme(false);
    });

    it('should display Alert Modal with Buttons when verification fails', async () => {
      const emailVerifiedButton = screen.getByText(
        t.emailVerification.emailVerifiedButton,
      );

      await act(async () => fireEvent.press(emailVerifiedButton));

      const errorModal = screen.getByText(t.verificationError.okayButton);
      const errorModalButton = screen.getByLabelText(
        t.verificationError.okayButton,
      );

      await act(async () => fireEvent.press(errorModalButton));

      expect(errorModal).toBeTruthy();
      expect(errorModalButton).toBeTruthy();
    });
  });
});
