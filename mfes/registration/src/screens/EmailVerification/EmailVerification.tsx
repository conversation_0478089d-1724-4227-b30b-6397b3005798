import { SpacerDynamic, SvgImageContainer } from '@bp/ui-components/mobile';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import React, { useEffect, useState } from 'react';
import { TouchableOpacity } from 'react-native';

import { RegAnalyticsEventEmailVerScreenChangeEmailClick } from '../../analytics/events/EmailVerScreenChangeEmailClick';
import { RegAnalyticsEventEmailVerScreenVerifiedEmailClick } from '../../analytics/events/EmailVerScreenVerifiedEmailClick';
import { RegAnalyticsEventUnableToVerifyEmailCloseClick } from '../../analytics/events/UnableToVerifyEmailCloseClick';
import { RegAnalyticsEventUnableToVerifyEmailOpen } from '../../analytics/events/UnableToVerifyEmailOpen';
import { RegAnalyticsEventVerificationLinkSentOpen } from '../../analytics/events/VerificationLinkSentOpen';
import { Email } from '../../assets/images';
import { ScreenName } from '../../common/enums';
import { RootStackParamList } from '../../common/interfaces';
import { IAlertButton } from '../../components/Atoms/AlertButton/AlertButton';
import { LoadingOverlay } from '../../components/Atoms/LoadingOverlay/LoadingOverlay';
import { CustomAlert } from '../../components/Molecules/CustomAlert/CustomAlert';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/SettingsProvider';
import * as S from './EmailVerification.styles';

export const EmailVerification = ({
  route: { params },
}: NativeStackScreenProps<
  RootStackParamList,
  ScreenName.EMAIL_VERIFICATION
>) => {
  const { registrationFlow, hasPreviousVerificationEmail = false } =
    params || {};
  const { navigate } = useHostNavigation();
  const {
    t,
    onAnalyticsEvent,
    authContext: { user, getUser, updateEmail },
  } = useSettings();
  const [loading, setLoading] = useState<boolean>(false);
  const [emailVerificationError, setEmailVerificationError] =
    useState<boolean>(false);

  useEffect(() => {
    onAnalyticsEvent(RegAnalyticsEventVerificationLinkSentOpen({}));
  }, [onAnalyticsEvent]);

  useEffect(() => {
    const sendVerificationEmail = async () => {
      if (!user || hasPreviousVerificationEmail) {
        return;
      }

      try {
        setLoading(true);
        const response = await updateEmail(user.email);
        if (response?.statusCode !== 200) {
          throw new Error(`Response not successful: ${response?.message}`);
        }
      } catch (error) {
        console.error('Error sending verification link: ', error);
        setEmailVerificationError(true);
      } finally {
        setLoading(false);
      }
    };
    sendVerificationEmail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const alertButtons: Array<IAlertButton> = [
    {
      text: t.verificationError.okayButton,
      onPress: () => {
        onAnalyticsEvent(RegAnalyticsEventUnableToVerifyEmailCloseClick({}));
        setEmailVerificationError(false);
      },
    },
  ];

  const checkVerifiedEmailState = async () => {
    try {
      setLoading(true);
      onAnalyticsEvent(RegAnalyticsEventEmailVerScreenVerifiedEmailClick({}));

      const response = await getUser();
      if (response?.emailVerified) {
        navigate(ScreenName.CUSTOMISE, {}, false);
      } else {
        onAnalyticsEvent(
          RegAnalyticsEventUnableToVerifyEmailOpen({
            errorMessage: 'User email not verified.',
          }),
        );
        setEmailVerificationError(true);
      }
    } catch (error) {
      onAnalyticsEvent(
        RegAnalyticsEventUnableToVerifyEmailOpen({ errorMessage: error }),
      );
      setEmailVerificationError(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <S.ContentContainer>
      <SvgImageContainer maxHeight={64}>
        <Email />
      </SvgImageContainer>

      <S.LargeTitleText
        accessibilityLabel={t.emailVerification.sentEmailHeader}
        testID={t.emailVerification.sentEmailHeader}
      >
        {t.emailVerification.sentEmailHeader}
      </S.LargeTitleText>

      <SpacerDynamic vSpace={1.9} />

      <S.LargeLabelText
        accessibilityLabel={t.emailVerification.openEmailLinkText0}
        testID={t.emailVerification.openEmailLinkText0}
      >
        {t.emailVerification.openEmailLinkText0}
        {'\n'}
        {user?.email}
        {t.emailVerification.openEmailLinkText1}
      </S.LargeLabelText>
      <SpacerDynamic vSpace={2} />

      <TouchableOpacity
        onPress={() => {
          onAnalyticsEvent(RegAnalyticsEventEmailVerScreenChangeEmailClick({}));
          navigate(ScreenName.UPDATE_EMAIL, { registrationFlow }, false);
        }}
      >
        <S.LinkButton>{t.emailVerification.changeEmailButton}</S.LinkButton>
      </TouchableOpacity>

      <SpacerDynamic vSpace={2.96} />

      <S.ButtonsContainer>
        <TouchableOpacity onPress={checkVerifiedEmailState}>
          <S.LinkButton>{t.emailVerification.emailVerifiedButton}</S.LinkButton>
        </TouchableOpacity>
      </S.ButtonsContainer>
      <LoadingOverlay active={loading} />
      <CustomAlert
        body={t.verificationError.body}
        buttons={alertButtons}
        isVisible={emailVerificationError}
        title={t.verificationError.title}
      />
    </S.ContentContainer>
  );
};
