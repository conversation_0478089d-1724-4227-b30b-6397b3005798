import { styled } from 'styled-components/native';

import { s, vs } from '../../utils/scale';

export const ContentContainer = styled.View`
  align-content: space-between;
  background-color: white;
  flex-flow: row wrap;
  height: 100%;
  padding-horizontal: ${s(24)}px;
`;

export const MiddleContainer = styled.View`
  align-items: center;
  justify-content: center;
  margin-bottom: 15%;
  width: 100%;
`;

export const Header = styled.Text`
  font-size: ${s(20)}px;
  letter-spacing: ${s(0.15)}px;
  line-height: ${s(30)}px;
  margin-bottom: ${vs(8)}px;
  margin-top: ${vs(8)}px;
  text-align: center;
`;

export const SubHeader = styled.Text`
  font-size: ${s(17)}px;
  letter-spacing: ${s(0.15)}px;
  line-height: ${s(28)}px;
  opacity: 0.75;
  text-align: center;
`;
export const BottomContainer = styled.View`
  width: 100%;
`;

export const ExitButtonContainer = styled.View`
  margin-top: ${vs(18)}px;
`;
