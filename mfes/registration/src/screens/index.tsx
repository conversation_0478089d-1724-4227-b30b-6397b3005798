import { CommonActions } from '@react-navigation/native';
import {
  createStackNavigator,
  StackNavigationOptions,
} from '@react-navigation/stack';
import React from 'react';
import { Dimensions, TextStyle } from 'react-native';

import { RegAnalyticsEventType } from '../analytics';
import { RegAnalyticsEventAddEmailAddressExitClick } from '../analytics/events/AddEmailAddressExitClick';
import { RegAnalyticsEventCustomiseBpPulseAppExitClick } from '../analytics/events/CustomiseBpPulseAppExitClick';
import { RegAnalyticsEventUpdateEmailAddressCloseClick } from '../analytics/events/UpdateEmailAddressCloseClick';
import { RegAnalyticsEventVerificationLinkSentExitClick } from '../analytics/events/VerificationLinkSentExitClick';
import { ScreenName } from '../common/enums';
import { RootStackParamList } from '../common/interfaces';
import { BackHeaderButton } from '../components/Organisms/BackHeaderButton/BackHeaderButton';
import { ExitHeaderButton } from '../components/Organisms/ExitHeaderButton/ExitHeaderButton';
import { useHostNavigation } from '../providers/HostNavigationProvider';
import { useSettings } from '../providers/SettingsProvider';
import { useUserInfo } from '../providers/UserInfoProvider';
import { AccountOnboarding } from './AccountOnboarding/AccountOnboarding';
import { AddEmail } from './AddEmail/AddEmail';
import { AuthenticateError } from './AuthenticateError/AuthenticateError';
import { Customise } from './Customise/Customise';
import { EmailAddressVerified } from './EmailAddressVerified/EmailAddressVerified';
import { EmailVerification } from './EmailVerification/EmailVerification';
import { Transient } from './Transient/Transient';
import { UpdateEmail } from './UpdateEmail/UpdateEmail';
import { Welcome } from './Welcome/Welcome';

const isSmallViewPort = Dimensions.get('screen').width > 320;

const Stack = createStackNavigator<RootStackParamList>();

export const Screens = () => {
  const { navigation } = useHostNavigation();
  const {
    t,
    onExitMFE,
    onLogout,
    onboardingComplete,
    onboardingFailed,
    authContext: { authenticated, consentsValid, user },
  } = useSettings();
  const { clearUserInfo } = useUserInfo();

  // Determine initial screen to present to user based on account status
  const getInitialScreen = (): keyof RootStackParamList => {
    if (!authenticated || onboardingFailed) {
      return ScreenName.AUTHENTICATE_ERROR;
    }

    if (user) {
      const { email, emailVerified, firstName, lastName, country } = user;

      if (!email) {
        return ScreenName.ADD_EMAIL;
      }

      if (!emailVerified) {
        return ScreenName.EMAIL_VERIFICATION;
      }

      if (!firstName || !lastName || !country || !consentsValid) {
        return ScreenName.CUSTOMISE;
      }

      if (!onboardingComplete) {
        return ScreenName.ACCOUNT_ONBOARDING;
      }

      if (onboardingComplete) {
        onExitMFE();
      }

      return ScreenName.TRANSIENT;
    }

    return ScreenName.AUTHENTICATE_ERROR;
  };

  const handleBackPress = () => {
    if (navigation.canGoBack()) {
      return navigation.dispatch(CommonActions.goBack());
    }
    return onExitMFE();
  };
  const handleExitPress = () => {
    onLogout();
    clearUserInfo();
    onExitMFE();
  };

  const headerLeftFn = (analyticsEvent?: RegAnalyticsEventType) =>
    BackHeaderButton({ analyticsEvent, onBackPress: handleBackPress });

  const headerRightFn = (analyticsEvent?: RegAnalyticsEventType) =>
    ExitHeaderButton({
      analyticsEvent,
      onExitPress: handleExitPress,
    });

  const headerTitleStyle: TextStyle = {
    fontSize: 20,
    fontStyle: 'normal',
    fontWeight: 'normal',
    lineHeight: 24,
    paddingRight: isSmallViewPort ? 0 : 20,
  };

  const defaultScreenOptions: StackNavigationOptions = {
    headerBackTitleVisible: false,
    headerLeft: () => null,
    headerRight: () => null,
    headerShadowVisible: false,
    headerTitleAlign: 'center',
    headerTitleStyle,
    title: '',
  };

  return (
    <Stack.Navigator initialRouteName={getInitialScreen()}>
      <Stack.Screen
        name={ScreenName.ACCOUNT_ONBOARDING}
        component={AccountOnboarding}
        options={{
          ...defaultScreenOptions,
        }}
      />
      <Stack.Screen
        name={ScreenName.ADD_EMAIL}
        component={AddEmail}
        options={{
          ...defaultScreenOptions,
          title: t.addEmail.screenTitle,
          headerRight: () =>
            headerRightFn(RegAnalyticsEventAddEmailAddressExitClick({})),
        }}
      />
      <Stack.Screen
        name={ScreenName.AUTHENTICATE_ERROR}
        component={AuthenticateError}
        options={{
          ...defaultScreenOptions,
        }}
      />
      <Stack.Screen
        name={ScreenName.CUSTOMISE}
        component={Customise}
        options={{
          ...defaultScreenOptions,
          title: t.customise.screenTitle,
          headerRight: () =>
            headerRightFn(RegAnalyticsEventCustomiseBpPulseAppExitClick({})),
        }}
      />
      <Stack.Screen
        name={ScreenName.EMAIL_ADDRESS_VERIFIED}
        component={EmailAddressVerified}
        options={{
          ...defaultScreenOptions,
        }}
      />
      <Stack.Screen
        name={ScreenName.EMAIL_VERIFICATION}
        component={EmailVerification}
        options={{
          ...defaultScreenOptions,
          headerRight: () =>
            headerRightFn(RegAnalyticsEventVerificationLinkSentExitClick({})),
        }}
      />
      <Stack.Screen
        name={ScreenName.TRANSIENT}
        component={Transient}
        options={{
          ...defaultScreenOptions,
        }}
      />
      <Stack.Screen
        name={ScreenName.UPDATE_EMAIL}
        component={UpdateEmail}
        options={{
          ...defaultScreenOptions,
          title: t.updateEmail.screenTitle,
          headerLeft: () => headerLeftFn(),
          headerRight: () =>
            headerRightFn(RegAnalyticsEventUpdateEmailAddressCloseClick({})),
        }}
      />
      <Stack.Screen
        name={ScreenName.WELCOME}
        component={Welcome}
        options={{
          ...defaultScreenOptions,
        }}
      />
    </Stack.Navigator>
  );
};
