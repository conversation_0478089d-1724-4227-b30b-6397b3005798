import { useAppSettings } from '@bp/profile-mfe';
import { SFConsent, SFConsentChannel } from '@bp/pulse-auth-sdk';
import {
  Button,
  ButtonAction,
  Select,
  Spacer,
} from '@bp/ui-components/mobile/core';
import React, { useCallback, useEffect, useState } from 'react';

import { RegAnalyticsEventCustomiseBpPulseAppOpen } from '../../analytics/events/CustomiseBpPulseAppOpen';
import { RegAnalyticsEventJumpRightInClick } from '../../analytics/events/JumpRightInClick';
import { RegAnalyticsEventMarketingOptInSave } from '../../analytics/events/MarketingOptInSave';
import { RegAnalyticsEventMarketingOptOutSave } from '../../analytics/events/MarketingOptOutSave';
import { RegAnalyticsEventUnableToSaveDetailsErrorGoBackClick } from '../../analytics/events/UnableToSaveDetailsErrorGoBackClick';
import { RegAnalyticsEventUnableToSaveDetailsErrorOpen } from '../../analytics/events/UnableToSaveDetailsErrorOpen';
import { AddComment, EmailLogo, Mobile } from '../../assets/images';
import {
  defaultBrandCountries,
  getCountryDetails,
  getInitialCountryByLocale,
} from '../../common/countries';
import {
  AppCountry,
  MinimumUnselectedConsentAgreements,
  ScreenName,
  SupportedPartners,
} from '../../common/enums';
import { CustomiseCountry } from '../../common/interfaces';
import { IAlertButton } from '../../components/Atoms/AlertButton/AlertButton';
import { DynamicText } from '../../components/Atoms/DynamicText/DynamicText';
import { LoadingOverlay } from '../../components/Atoms/LoadingOverlay/LoadingOverlay';
import { CustomAlert } from '../../components/Molecules/CustomAlert/CustomAlert';
import {
  CustomInput,
  ErrorIcon,
} from '../../components/Molecules/CustomInput/CustomInput';
import {
  InputType,
  useInputValidation,
  ValidationError,
} from '../../hooks/useInputValidation';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/SettingsProvider';
import { supportedLocaleToLanguageCode } from '../../utils/supportedLocaleToLanguageCode';
import { CheckBox } from './CheckBox/CheckBox';
import * as S from './Customise.styles';

export const Customise = () => {
  const {
    locale,
    t,
    termsAndConditions,
    termsAndConditionsType,
    privacyPolicy,
    privacyPolicyType,
    defaultAppCountry,
    authContext: { updateContact, updateConsent, user },
    featureFlags: {
      brandCountries,
      enableCountrySelection,
      enableNewMarketingConsents,
      enableSpain,
      enableAralActivationMessage,
    },
    externalLinks: { de_pricing_link = '' } = {},
    onAnalyticsEvent,
    setCountry,
    country,
  } = useSettings();

  const { userInfo } = useAppSettings();
  const [selectedCountryValue, setSelectedCountryValue] = useState<
    CustomiseCountry | undefined
  >();
  const [firstName, setFirstName, isFirstNameValid, firstNameErrorMessage] =
    useInputValidation(user?.firstName ?? '', InputType.FIRST_NAME);
  const [lastName, setLastName, isLastNameValid, lastNameErrorMessage] =
    useInputValidation(user?.lastName ?? '', InputType.LAST_NAME);
  const [isMarketingChecked, setIsMarketingChecked] = useState(false);
  const [isEmailMarketingChecked, setIsEmailMarketingChecked] = useState(false);
  const [isTextMarketingChecked, setIsTextMarketingChecked] = useState(false);
  const [isPushMarketingChecked, setIsPushMarketingChecked] = useState(false);
  const [isMinimumMarketingSelected, setIsMinimumMarketingSelected] =
    useState(false);
  const [
    isTermsAndConditionsChecked,
    setIsTermsAndConditionsChecked,
    isTermsAndConditionsValid,
  ] = useInputValidation(false, InputType.TERMS_AND_CONDITIONS);

  const [loading, setLoading] = useState<boolean>(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const [isUnableToSave, setIsUnableToSave] = useState(false);
  const [displayPricingList, setDisplayPricingList] = useState(false);
  const [displayCountrySelector, setDisplayCountrySelector] = useState(false);
  const [availableCountriesNames, setAvailableCountriesNames] = useState<
    Array<string>
  >([]);

  const [availableCountries, setAvailableCountries] = useState<
    Array<CustomiseCountry>
  >([]);

  const { navigate } = useHostNavigation();

  const nickNameExists = user?.nickName?.includes('.');

  useEffect(() => {
    onAnalyticsEvent(RegAnalyticsEventCustomiseBpPulseAppOpen({}));
  }, [onAnalyticsEvent]);

  useEffect(() => {
    setDisplayCountrySelector(
      enableCountrySelection &&
        brandCountries?.length > 1 &&
        (userInfo.partnerType !== SupportedPartners.UBER ||
          country !== AppCountry.UK),
    );
  }, [enableCountrySelection, brandCountries, userInfo.partnerType, country]);

  useEffect(() => {
    setDisplayPricingList(
      !!de_pricing_link &&
        selectedCountryValue?.appCountry === AppCountry.GERMANY,
    );
  }, [de_pricing_link, selectedCountryValue?.appCountry]);

  const countryChanged = useCallback(
    (updatedCountry: string) => {
      if (updatedCountry) {
        const updatedCountryObject = availableCountries.find(
          (country) => country.displayName === updatedCountry,
        );
        setCountry(updatedCountryObject?.appCountry);
        setSelectedCountryValue(updatedCountryObject);
      }
    },
    [availableCountries, setCountry],
  );

  useEffect(() => {
    if (user?.firstName && user.firstName.includes('.') && !user?.lastName) {
      const [first = '', last = ''] = user.firstName.split('.');
      setFirstName(first.trim());
      setLastName(last.trim());
    } else if (user?.nickName && nickNameExists) {
      const [firstNick = '', lastNick = ''] = user.nickName
        .split('.')
        .filter(Boolean);
      setFirstName(firstNick.trim());
      setLastName(lastNick.trim());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    let countries = enableSpain
      ? brandCountries
      : brandCountries?.filter(({ type }) => type !== AppCountry.SPAIN);

    if (!countries?.length) {
      countries = defaultBrandCountries;
    }
    const orderedCountries = countries
      .map((country) =>
        getCountryDetails(country, supportedLocaleToLanguageCode(locale)),
      )
      .sort((a, b) => {
        return a.name < b?.name ? -1 : a.name > b.name ? 1 : 0;
      });

    setAvailableCountries(orderedCountries);
  }, [enableSpain, t, brandCountries, locale]);

  useEffect(() => {
    const countries = brandCountries?.length
      ? brandCountries
      : defaultBrandCountries;

    const defaultBrandAppCountry = displayCountrySelector
      ? undefined
      : defaultAppCountry;

    const initialCountry: CustomiseCountry = getInitialCountryByLocale(
      countries,
      locale,
      defaultBrandAppCountry,
    );

    if (initialCountry) {
      countryChanged(initialCountry.displayName);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    availableCountries,
    brandCountries,
    defaultAppCountry,
    displayCountrySelector,
    locale,
  ]);

  const unableToSaveAlertButtons: Array<IAlertButton> = [
    {
      text: t.unableToSaveAlert.tryAgain,
      onPress: () => {
        onAnalyticsEvent(
          RegAnalyticsEventUnableToSaveDetailsErrorGoBackClick({}),
        );
        setIsUnableToSave(false);
      },
    },
  ];

  useEffect(() => {
    onAnalyticsEvent(RegAnalyticsEventCustomiseBpPulseAppOpen({}));
  }, [onAnalyticsEvent]);

  useEffect(() => {
    setIsFormValid(
      isFirstNameValid && isLastNameValid && isTermsAndConditionsValid,
    );
  }, [isFirstNameValid, isLastNameValid, isTermsAndConditionsValid]);

  useEffect(() => {
    [
      isEmailMarketingChecked,
      isPushMarketingChecked,
      isTextMarketingChecked,
    ].filter((value) => !value).length ===
    MinimumUnselectedConsentAgreements.MINIMUM_UNSELECTED_CONSENTS
      ? setIsMinimumMarketingSelected(false)
      : setIsMinimumMarketingSelected(true);
  }, [isEmailMarketingChecked, isPushMarketingChecked, isTextMarketingChecked]);

  useEffect(() => {
    setIsEmailMarketingChecked(isMarketingChecked);
    setIsTextMarketingChecked(isMarketingChecked);
    setIsPushMarketingChecked(isMarketingChecked);
    setIsMinimumMarketingSelected(isMarketingChecked);
  }, [isMarketingChecked]);

  const bpMarketingConsents = () => {
    if (!enableNewMarketingConsents || !isMarketingChecked) {
      return <></>;
    }

    return (
      <>
        <CheckBox
          accessibilityLabel={t.customise.email}
          onPress={() => setIsEmailMarketingChecked(!isEmailMarketingChecked)}
          disabled={isEmailMarketingChecked && !isMinimumMarketingSelected}
          isChecked={isEmailMarketingChecked}
        >
          <S.CheckBoxTextContainer>
            <EmailLogo />
            <S.CheckBoxIconTextContent>
              {t.customise.email}
            </S.CheckBoxIconTextContent>
          </S.CheckBoxTextContainer>
        </CheckBox>
        <CheckBox
          accessibilityLabel={t.customise.textMessage}
          onPress={() => {
            setIsTextMarketingChecked(!isTextMarketingChecked);
          }}
          disabled={isTextMarketingChecked && !isMinimumMarketingSelected}
          isChecked={isTextMarketingChecked}
        >
          <S.CheckBoxTextContainer>
            <AddComment />
            <S.CheckBoxIconTextContent>
              {t.customise.textMessage}
            </S.CheckBoxIconTextContent>
          </S.CheckBoxTextContainer>
        </CheckBox>
        <CheckBox
          accessibilityLabel={t.customise.pushNotifications}
          onPress={() => {
            setIsPushMarketingChecked(!isPushMarketingChecked);
          }}
          disabled={isPushMarketingChecked && !isMinimumMarketingSelected}
          isChecked={isPushMarketingChecked}
        >
          <S.CheckBoxTextContainer>
            <Mobile />
            <S.CheckBoxIconTextContent>
              {t.customise.pushNotifications}
            </S.CheckBoxIconTextContent>
          </S.CheckBoxTextContainer>
        </CheckBox>
      </>
    );
  };

  const aboutPricingText = () => {
    return displayPricingList ? (
      <>
        <S.PricingHeaderText>{t.customise.pricingHeader}</S.PricingHeaderText>
        <DynamicText
          content={t.customise.pricingSubText}
          placeholders={t.customise}
          urls={{ pricingList: de_pricing_link }}
        />
      </>
    ) : (
      <></>
    );
  };

  const validateAndSubmit = async () => {
    onAnalyticsEvent(RegAnalyticsEventJumpRightInClick({}));
    const isUKCountry = selectedCountryValue?.appCountry === 'UK';
    try {
      setLoading(true);
      const contact = {
        firstName,
        lastName,
        country: selectedCountryValue?.appCountry,
      };

      const marketingConsent = [
        {
          accepted: isMarketingChecked,
          consentType: SFConsent.EV_MARKETING,
          channel: ['Email' as SFConsentChannel],
        },
      ];

      const newMarketingConsents = [
        {
          accepted: isEmailMarketingChecked,
          consentType: SFConsent.EV_MARKETING,
          channel: ['Email' as SFConsentChannel],
        },
        {
          accepted: isTextMarketingChecked,
          consentType: SFConsent.EV_MARKETING_SMS,
          channel: ['SMS' as SFConsentChannel],
        },
        {
          accepted: isPushMarketingChecked,
          consentType: SFConsent.EV_MARKETING_PUSH,
          channel: ['Push' as SFConsentChannel],
        },
      ];

      const consents = [
        {
          accepted: isTermsAndConditionsChecked,
          consentType: termsAndConditionsType,
          version: isUKCountry ? '1.0' : undefined,
        },
        {
          accepted: isTermsAndConditionsChecked,
          consentType: privacyPolicyType,
          version: isUKCountry ? '1.0' : undefined,
        },
        ...(enableNewMarketingConsents
          ? newMarketingConsents
          : marketingConsent),
      ];

      await Promise.all([updateContact(contact), updateConsent(consents)]);

      navigate(ScreenName.ACCOUNT_ONBOARDING);
    } catch (error) {
      onAnalyticsEvent(
        RegAnalyticsEventUnableToSaveDetailsErrorOpen({ errorMessage: error }),
      );
      setIsUnableToSave(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const countryNames = availableCountries.map(
      ({ displayName }) => displayName,
    );
    setAvailableCountriesNames(countryNames);
  }, [availableCountries]);

  const displayCountrySelectionContainer = () => {
    return (
      displayCountrySelector && (
        <>
          <S.DropdownContainer>
            <S.Text>{t.customise.selectCountryHeader}</S.Text>
            <Select
              options={availableCountriesNames}
              selectedOption={selectedCountryValue?.displayName || undefined}
              onChange={countryChanged}
              placeholder={t.customise.selectCountry}
              testID="countrySelect"
            />

            <Spacer vSpace={12} />
          </S.DropdownContainer>
          <S.GreyText>{t.customise.selectCountrySubHeader}</S.GreyText>
        </>
      )
    );
  };

  const spacerContainer = () => {
    return !enableNewMarketingConsents || !isMarketingChecked ? (
      <Spacer vSpace={30} />
    ) : (
      <Spacer vSpace={20} />
    );
  };

  const displayActivationMessage = () => {
    return enableAralActivationMessage &&
      selectedCountryValue?.appCountry === AppCountry.GERMANY ? (
      <S.Text>{t.customise.activationMessageText}</S.Text>
    ) : null;
  };

  return (
    <S.ScreenContainer showCountrySelector={displayCountrySelector}>
      <S.ScrollView>
        <S.UpperViewContainer>
          {displayCountrySelectionContainer()}
          <>
            <CustomInput
              error={
                firstNameErrorMessage === ValidationError.NO_EMPTY
                  ? t.customise.firstNameEmptyError ?? ''
                  : t.customise.firstNameError
              }
              errorIcon={ErrorIcon.TRIANGLE}
              hasCapitalLetter={true}
              isValid={isFirstNameValid}
              label={t.customise.firstNameLabel}
              markDirty={!!user?.firstName}
              onChange={setFirstName}
              placeholder={t.customise.firstNamePlaceholder}
              value={firstName}
            />
            <CustomInput
              error={
                lastNameErrorMessage === ValidationError.NO_EMPTY
                  ? t.customise.lastNameEmptyError ?? ''
                  : t.customise.lastNameError
              }
              errorIcon={ErrorIcon.TRIANGLE}
              hasCapitalLetter={true}
              isValid={isLastNameValid}
              label={t.customise.lastNameLabel}
              markDirty={!!user?.lastName}
              onChange={setLastName}
              placeholder={t.customise.lastNamePlaceholder}
              value={lastName}
            />
          </>
          {spacerContainer()}
          <S.MarketingViewContainer
            showBorder={enableNewMarketingConsents && isMarketingChecked}
          >
            <CheckBox
              accessibilityLabel={t.customise.marketing}
              onPress={() => {
                onAnalyticsEvent(
                  !isMarketingChecked
                    ? RegAnalyticsEventMarketingOptInSave({})
                    : RegAnalyticsEventMarketingOptOutSave({}),
                );
                setIsMarketingChecked(!isMarketingChecked);
              }}
              isChecked={isMarketingChecked}
            >
              <S.CheckBoxTextContainer>
                <S.CheckBoxTextContent>
                  {t.customise.marketing}
                </S.CheckBoxTextContent>
              </S.CheckBoxTextContainer>
            </CheckBox>
            {bpMarketingConsents()}
          </S.MarketingViewContainer>
          <S.TermsAndConditionsContainer>
            <CheckBox
              accessibilityLabel={t.customise.termsAndConditions}
              onPress={() =>
                setIsTermsAndConditionsChecked(!isTermsAndConditionsChecked)
              }
              disabled={!selectedCountryValue}
              isChecked={isTermsAndConditionsChecked}
              testID={t.customise.termsAndConditions}
            >
              <S.CheckBoxTextContainer>
                <DynamicText
                  content={t.customise.acknowledgement}
                  placeholders={{
                    termsAndConditions: t.customise.termsAndConditions,
                    privacyPolicy: t.customise.privacyPolicy,
                  }}
                  urls={{ privacyPolicy, termsAndConditions }}
                  disabled={!selectedCountryValue}
                />
              </S.CheckBoxTextContainer>
            </CheckBox>
            {displayActivationMessage()}
            {aboutPricingText()}
          </S.TermsAndConditionsContainer>
        </S.UpperViewContainer>
      </S.ScrollView>
      <LoadingOverlay active={loading} />
      <CustomAlert
        body={t.unableToSaveAlert.body}
        buttons={unableToSaveAlertButtons}
        isVisible={isUnableToSave}
        title={t.unableToSaveAlert.title}
      />
      <Button
        accessibilityHint={t.customise.continueButton}
        accessibilityLabel={t.customise.continueButton}
        onPress={() => validateAndSubmit()}
        disabled={!isFormValid}
        testID="continueButton"
        type={ButtonAction.PRIMARY}
        styleOverride={{
          marginHorizontal: 24,
          marginBottom: 24,
          borderRadius: 5,
          backgroundColor: isFormValid ? '#1d1d26' : '#afafaf',
        }}
      >
        {t.customise.jumpRightIn}
      </Button>
    </S.ScreenContainer>
  );
};
