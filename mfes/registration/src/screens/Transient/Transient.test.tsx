import React from 'react';

import { Theme } from '../../themes/themes';
import { render } from '../../utils/testing';
import { Transient } from './Transient';

const mockStyle = {
  registrationMfe: { buttons: { secondary: { grey: '#111111a2' } } },
};
const mockStyleFn = jest.fn().mockReturnValue(mockStyle);
jest.mock('styled-components', () => ({
  useTheme: () => mockStyleFn(),
}));

const renderComponent = () =>
  render(
    <Theme>
      <Transient />
    </Theme>,
  );

describe('Transient Screen', () => {
  beforeEach(() => {
    renderComponent();
  });
  it('renders', () => {
    const TransientTest = renderComponent();
    expect(TransientTest.toJSON()).toMatchSnapshot();
  });
});
