import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { registerLocale } from 'i18n-iso-countries';
import deLocale from 'i18n-iso-countries/langs/de.json';
import enLocale from 'i18n-iso-countries/langs/en.json';
import esLocale from 'i18n-iso-countries/langs/es.json';
import frLocale from 'i18n-iso-countries/langs/fr.json';
import nlLocale from 'i18n-iso-countries/langs/nl.json';

import de_DE from './locales/de-DE.json';
import en_GB from './locales/en-GB.json';
import en_US from './locales/en-US.json';
import es_ES from './locales/es-ES.json';
import fr_FR from './locales/fr-FR.json';
import nl_NL from './locales/nl-NL.json';
import pt_PT from './locales/pt-PT.json';
import template from './messages.json';
import { ITranslation } from './schema';

// Used for looking up country names based on CountryCodeISO and LanguageCodeISO
[deLocale, enLocale, esLocale, nlLocale, frLocale].forEach(registerLocale);

interface IExtraTranslations {
  ['en-US']: ITranslation;
  ['pt-PT']: ITranslation;
  template: ITranslation;
}

type ITranslations = {
  [key in SupportedLocale]: ITranslation;
} & IExtraTranslations;

export const TRANSLATIONS: ITranslations = {
  [SupportedLocale.DE_DE]: de_DE,
  [SupportedLocale.EN_GB]: en_GB,
  [SupportedLocale.ES_ES]: es_ES,
  [SupportedLocale.FR_FR]: fr_FR,
  [SupportedLocale.NL_NL]: nl_NL,
  ['pt-PT']: pt_PT,
  ['en-US']: en_US,
  template,
};
