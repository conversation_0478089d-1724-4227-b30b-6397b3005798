{"unableToSaveAlert": {"title": "Unable to save details", "body": "We were unable to save the details entered, please check them and try again. Alternatively, if you already have a bp account you can login with those details.\n\nIf the problem persists, please contact customer care via the Help tab.", "tryAgain": "Try again"}, "exitPopUpAlert": {"title": "Exit account setup?", "body": "Information will not be saved, are you sure you want to exit?", "exit": "Log out", "continue": "Continue setup"}, "emailVerification": {"sentEmailHeader": "Verification link sent", "openEmailLinkText0": "We’ve sent you a verification link to ", "openEmailLinkText1": " via email. Please click on the link in the email to continue.", "changeEmailButton": "Change email address", "emailVerifiedButton": "I have opened the {{brand}} pulse link"}, "emailAddressVerified": {"title": "Email address verified", "body": "Thank you for verifying your email address. You can now use this email to login.", "continue": "Continue"}, "unableToCompleteSetUpAlert": {"title": "Unable to complete set-up", "body": "We were unable to update your account, please try again.", "logOutButton": "Log out", "tryAgainButton": "Try again"}, "addEmail": {"screenTitle": "Add email address", "emailLabel": "Enter your email address", "emailPlaceholder": "email address", "emailHint": "We will send a verification link to you to enable login with this email address.", "emailError": "Invalid email format. Please try again.", "emailErrorDuplicate": "The email address entered already exists in our records. Please try another one.", "saveButton": "Send verification link"}, "addEmailError": {"title": "We couldn’t save your email", "body": "Check your internet connection and try again. If you still have problems, contact Customer care via the Help section.", "closeButton": "OK"}, "welcome": {"title": "Hi", "bannerText": "Welcome to {{brand}} pulse", "subBannerText": "As a signed-up member, you get all of these benefits:", "floatingCardTitle": "EV roaming", "floatingCardText": "In 24 hours, you’ll be able to charge at non-{{brand}} pulse locations too.", "buttonText": "Get charging", "benefitsSubTitle": {"lowerRates": "Lower rates", "fastCharging": "Fast charging", "easyCharging": "Easy charging", "favourite": "Favourite your locations"}, "benefitsText": {"lowerRates": "Enjoy better on-the-go rates.", "fastCharging": "Access one of the largest fast-charging networks.", "easyCharging": "Start and end your charge with the {{brand}} pulse app.", "favourite": "Save your most used locations for quick access."}}, "customise": {"screenTitle": "Customise the {{brand}} pulse app", "acknowledgement": "I have read and agreed to the {{brand}} pulse {{termsAndConditions}} and {{privacyPolicy}}.", "termsAndConditions": "terms and conditions", "privacyPolicy": "privacy policy", "selectCountryHeader": "Select your home country", "selectCountry": "select country", "selectCountrySubHeader": " Your home country is the location you’ll be primarily charging.", "firstNameLabel": "Enter your first name", "firstNamePlaceholder": "first name", "firstNameError": "Your name contains invalid characters.", "firstNameEmptyError": "Please enter your first name.", "lastNameLabel": "Enter your last name", "lastNamePlaceholder": "last name", "lastNameError": "Your name contains invalid characters.", "lastNameEmptyError": "Please enter your last name.", "marketing": "I would like to receive general & personalised communications and offers from {{brand}} pulse", "jumpRightIn": "Jump right in!", "email": "Email", "textMessage": "Text message", "pushNotifications": "Push notifications", "pricingHeader": "About pricing", "pricingSubText": "If you would like to see our current prices, they can be found on our {{pricingList}}.", "pricingList": "pricing list", "activationMessageText": "We will review your details and will usually complete the setup of your Aral pulse app account within 24 hours.", "continueButton": "Continue"}, "authenticateError": {"header": "Something went wrong", "subheader": "You can try again to log in/create an account or go back to the {{brand}} pulse app.", "retryButton": "Try again", "exitButton": "Exit back to {{brand}} pulse app"}, "genericError": {"title": "Something went wrong", "body": "Sorry, we're experiencing technical difficulties at the moment. Please try again later.", "buttonText": "Close"}, "accountOnboarding": {"title": "Setting up your account", "text": "We are updating your details and creating your account, this usually takes up to 60 seconds."}, "updateEmail": {"screenTitle": "Update email address", "subTitle": "Enter your email address", "emailPlaceholder": "email address", "informationText": "We will send a verification link to you to enable login with this email address.", "sendVerificationButton": "Send verification link", "emailError": "Invalid email format. Please try again.", "emailErrorDuplicate": "The email address entered already exists in our records. Please try another one."}, "updateEmailError": {"title": "We couldn’t save your email", "body": "Check your internet connection and try again. If you still have problems, contact Customer care via the Help section.", "closeButton": "OK"}, "verificationError": {"title": "Verification not completed", "body": "Sorry, we haven't been able to authenticate your verification from our side. \n \n Please go back and check the email address you entered is correct or check your spam folder for the verification link and try again.", "okayButton": "Okay"}}