import fs from 'fs';
import path from 'path';

import { TRANSLATION_SCHEMA } from '../../__tests__/constants/translationSchema';
import { compareSets } from '../../__tests__/utils/compareSets';
import { extractKeysWithPlaceholders } from '../../__tests__/utils/extractKeysWithPlaceholders';

describe('All locale JSON files ITranslation schema validation', () => {
  const expected = extractKeysWithPlaceholders(TRANSLATION_SCHEMA);
  const localesDir = path.join(__dirname, '../locales');
  const localeFiles = fs
    .readdirSync(localesDir)
    .filter((file) => file.endsWith('.json'));

  const defaultTranslations: Record<string, any> = {
    customise: {
      firstNameEmptyError: 'Default first name empty error',
      lastNameEmptyError: 'Default last name empty error',
    },
  };

  const applyDefaults = (
    localeData: Record<string, any>,
    defaults: Record<string, any>,
  ): void => {
    Object.keys(defaults).forEach((key) => {
      if (!localeData[key]) {
        localeData[key] = defaults[key];
      } else if (
        typeof defaults[key] === 'object' &&
        !Array.isArray(defaults[key])
      ) {
        applyDefaults(localeData[key], defaults[key]);
      }
    });
  };

  localeFiles.forEach((file) => {
    const filePath = path.join(localesDir, file);
    const localeData = require(filePath);

    // Apply default translations
    applyDefaults(localeData, defaultTranslations);

    const comparison = compareSets({
      input: extractKeysWithPlaceholders(localeData),
      expected,
    });

    describe(`${file} file`, () => {
      it.each<[string, boolean, keyof typeof comparison]>([
        ['missing keys', true, 'missingKeys'],
        ['extra keys', false, 'extraKeys'],
        ['missing placeholders', true, 'missingValues'],
        ['extra placeholders', true, 'extraValues'],
      ])(`should contain no %s`, (itemType, shouldFail, valueType) => {
        expect(comparison[valueType]).toBeEmptyCollection({
          itemType,
          shouldFail,
        });
      });
    });
  });
});
