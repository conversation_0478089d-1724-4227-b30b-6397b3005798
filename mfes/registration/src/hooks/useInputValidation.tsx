import { isValidPhoneNumber } from 'libphonenumber-js';
import { Dispatch, SetStateAction, useLayoutEffect, useState } from 'react';
import * as yup from 'yup';

import { extractDigits } from '../utils/textFormatter';

export enum ValidationError {
  INVALID_EMAIL_FORMAT = 'Invalid email format',
  INVALID_PHONE_FORMAT = 'Invalid phone format',
  NO_EMOJIS = 'Should not include emojis',
  NO_EMPTY = 'Should not be empty',
  NO_HYPHEN_ADJACENT_TO_WHITESPACE = 'Should not include hyphen adjacent to whitespace',
  NO_HYPHEN_CONSECUTIVE = 'Should not include consecutive hyphens',
  NO_HYPHEN_END = 'Should not end with hyphens',
  NO_HYPHEN_ONLY = 'Should not include only hyphens',
  NO_HYPHEN_START = 'Should not start with hyphens',
  NO_NUMBERS = 'Should not include numbers',
  NO_SPECIAL_CHARACTERS = 'Should not include special characters',
  NO_WHITESPACE_CONSECUTIVE = 'Should not include consecutive whitespace',
  NO_WHITESPACE_END = 'Should not end with whitespace',
  NO_WHITESPACE_ONLY = 'Should not include only whitespace',
  NO_WHITESPACE_START = 'Should not start with whitespace',
  NOT_ALLOWED = 'Value is not allowed',
  NOT_EXCEED_80_CHARACTERS = 'Should not exceed 80 characters',
  ONLY_NUMBERS = 'Must only contain numbers',
  ONLY_ONE_TO_FOUR_DIGITS = 'Must be between 1 and 4 digits long',
  ONLY_SIX_DIGITS = 'Must be exactly 6 digits long',
  ONLY_TRUE = 'Must be true',
  ONLY_TWO_CHARACTERS = 'Must be exactly 2 characters long',
}

const requiredTrue = yup
  .boolean()
  .required(ValidationError.NO_EMPTY)
  .oneOf([true], ValidationError.ONLY_TRUE);
const requiredString = yup.string().required(ValidationError.NO_EMPTY);

const noWhitespaceOnly = yup
  .string()
  .matches(/^(?!\s*$).+$/, ValidationError.NO_WHITESPACE_ONLY);
const noStartWithWhitespace = yup
  .string()
  .matches(/^\S/, ValidationError.NO_WHITESPACE_START);
const noEndWithWhitespace = yup
  .string()
  .matches(/\S$/, ValidationError.NO_WHITESPACE_END);
const noConsecutiveWhitespace = yup
  .string()
  .matches(/^(?!.*\s{2,}).*$/, ValidationError.NO_WHITESPACE_CONSECUTIVE);

const noHyphenOnly = yup
  .string()
  .matches(/^(?!-*$).+$/, ValidationError.NO_HYPHEN_ONLY);
const noStartWithHyphen = yup
  .string()
  .matches(/^[^-]/, ValidationError.NO_HYPHEN_START);
const noEndWithHyphen = yup
  .string()
  .matches(/[^-]$/, ValidationError.NO_HYPHEN_END);
const noConsecutiveHyphen = yup
  .string()
  .matches(/^(?!.*-{2,}).*$/, ValidationError.NO_HYPHEN_CONSECUTIVE);

const combinedWhitespaceSchema = yup
  .string()
  .concat(noWhitespaceOnly)
  .concat(noStartWithWhitespace)
  .concat(noEndWithWhitespace)
  .concat(noConsecutiveWhitespace);

const combinedHyphenSchema = yup
  .string()
  .concat(noHyphenOnly)
  .concat(noStartWithHyphen)
  .concat(noEndWithHyphen)
  .concat(noConsecutiveHyphen);

const noHyphenAdjacentToWhitespace = yup
  .string()
  .matches(
    /^(?!.*(-(?=\s)|\s(?=-))).*$/,
    ValidationError.NO_HYPHEN_ADJACENT_TO_WHITESPACE,
  );

const noEmoji = yup
  .string()
  .matches(/^\P{Emoji}*$/gu, ValidationError.NO_EMOJIS);

// Hyphen `-`, En Dash `–` (U+2013), & Em Dash `—` (U+2014) are all distinct characters but appear similar in monospace
const noSpecialCharacters = yup
  .string()
  .matches(
    /^[^!@#$%^&*()–—_+=[\]{};':"\\|,.<>/?]+$/,
    ValidationError.NO_SPECIAL_CHARACTERS,
  );

const noNumbers = yup.string().matches(/^[^0-9]+$/, ValidationError.NO_NUMBERS);
const onlyNumbers = yup
  .string()
  .matches(/^[0-9]+$/, ValidationError.ONLY_NUMBERS);

const requiredName = yup
  .string()
  .concat(requiredString)
  .concat(combinedWhitespaceSchema)
  .concat(combinedHyphenSchema)
  .concat(noHyphenAdjacentToWhitespace)
  .concat(noSpecialCharacters)
  .concat(noNumbers)
  .concat(noEmoji);

const requiredStringNumber = yup
  .string()
  .concat(requiredString)
  .concat(onlyNumbers);

const emailSchema = yup.object().shape({
  email: requiredString.email(ValidationError.INVALID_EMAIL_FORMAT).max(80),
});

const firstNameSchema = yup.object().shape({
  firstName: requiredName.max(80, ValidationError.NOT_EXCEED_80_CHARACTERS),
});

const lastNameSchema = yup.object().shape({
  lastName: requiredName.max(80, ValidationError.NOT_EXCEED_80_CHARACTERS),
});

const phoneNumberSchema = yup.object().shape({
  countryCode: requiredString
    .min(2)
    .max(2)
    .matches(/^[A-Z]{2}$/, ValidationError.ONLY_TWO_CHARACTERS),
  dialCode: requiredString
    .min(1)
    .max(5)
    .matches(/^\+[0-9]{1,4}$/, ValidationError.ONLY_ONE_TO_FOUR_DIGITS),
  number: requiredStringNumber.test(
    'Phone Number Validation',
    ValidationError.INVALID_PHONE_FORMAT,
    function faxNumberValidation(value) {
      const { countryCode, dialCode } = this.parent;

      if (countryCode && dialCode && value) {
        const dialCodeNumbersOnly = extractDigits(dialCode);
        return isValidPhoneNumber(dialCodeNumbersOnly + value, countryCode);
      }

      return false;
    },
  ),
});

const privacyPolicySchema = yup.object().shape({
  privacyPolicy: requiredTrue,
});

const smsCodeSchema = yup.object().shape({
  smsCode: requiredString.matches(
    /^[0-9]{6}$/,
    ValidationError.ONLY_SIX_DIGITS,
  ),
});

const termsAndConditionsSchema = yup.object().shape({
  termsAndConditions: requiredTrue,
});

export enum InputType {
  EMAIL = 'email',
  FIRST_NAME = 'firstName',
  LAST_NAME = 'lastName',
  PHONE_NUMBER = 'phoneNumber',
  PRIVACY_POLICY = 'privacyPolicy',
  SMS_CODE = 'smsCode',
  TERMS_AND_CONDITIONS = 'termsAndConditions',
}

const validations = {
  [InputType.EMAIL]: emailSchema,
  [InputType.FIRST_NAME]: firstNameSchema,
  [InputType.LAST_NAME]: lastNameSchema,
  [InputType.PHONE_NUMBER]: phoneNumberSchema,
  [InputType.PRIVACY_POLICY]: privacyPolicySchema,
  [InputType.SMS_CODE]: smsCodeSchema,
  [InputType.TERMS_AND_CONDITIONS]: termsAndConditionsSchema,
};

const notInArray = (array: Array<any>): yup.BaseSchema => {
  return yup
    .mixed()
    .test('Not In Array', ValidationError.NOT_ALLOWED, (value) => {
      return !array.includes(value);
    });
};

export type InputValidation<T> = [
  inputValue: T,
  setInputValue: Dispatch<SetStateAction<T>>,
  isValid: boolean,
  message: string,
  setValuesToExclude: Dispatch<SetStateAction<Array<T>>>,
];

export function useInputValidation<T>(
  initialValue: T,
  inputType: InputType,
): InputValidation<T> {
  const [inputValue, setInputValue] = useState<T>(initialValue);
  const [isValid, setIsValid] = useState(true);
  const [valuesToExclude, setValuesToExclude] = useState<Array<T>>([]);
  const [message, setMessage] = useState('');

  useLayoutEffect(() => {
    let valid = true;

    if (validations[inputType]) {
      const value =
        typeof inputValue === 'object'
          ? inputValue
          : { [inputType]: inputValue };

      try {
        validations[inputType].validateSync({ ...value });
        setMessage('');
      } catch (error) {
        valid = false;

        if (error instanceof yup.ValidationError) {
          setMessage(error.message);
        }
      }
    }

    if (
      valid &&
      inputValue &&
      valuesToExclude?.length &&
      typeof inputValue === 'string' &&
      valuesToExclude.every((v) => typeof v === 'string')
    ) {
      try {
        notInArray(valuesToExclude).validateSync(inputValue);
      } catch (error) {
        valid = false;

        if (error instanceof yup.ValidationError) {
          setMessage(error.message);
        }
      }
    }

    setIsValid(valid);
  }, [inputType, inputValue, valuesToExclude]);

  return [inputValue, setInputValue, isValid, message, setValuesToExclude];
}
