import { useEffect, useRef, useState } from 'react';

export interface IUseTimer {
  hasTimerCompleted: boolean;
  isTimerRunning: boolean;
  resetTimer: () => void;
  startTimer: () => void;
  stopTimer: () => void;
  timeElapsed: number;
  timeRemaining: number;
}

export const useTimer = (duration: number): IUseTimer => {
  const oneSecond = 1000;
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(duration * oneSecond);
  const [hasTimerCompleted, setHasTimerCompleted] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimestampRef = useRef<number | null>(null);

  const resetFields = () => {
    setHasTimerCompleted(false);
    setTimeElapsed(0);
    setTimeRemaining(duration * oneSecond);
  };

  const startTimer = () => {
    resetFields();
    startTimestampRef.current = Date.now();
    setIsTimerRunning(true);
  };

  const stopTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      setIsTimerRunning(false);
      startTimestampRef.current = null;
    }
  };

  const resetTimer = () => {
    stopTimer();
    resetFields();
  };

  useEffect(() => {
    if (isTimerRunning) {
      intervalRef.current = setInterval(() => {
        const timeLapsed = Date.now() - (startTimestampRef.current ?? 0);
        setTimeElapsed(timeLapsed);

        setTimeRemaining(Math.max(duration * oneSecond - timeLapsed, 0));

        if (timeLapsed >= duration * oneSecond) {
          setHasTimerCompleted(true);
          stopTimer();
        }
      }, oneSecond);
    } else {
      stopTimer();
    }

    return stopTimer;
  }, [duration, isTimerRunning]);

  return {
    hasTimerCompleted,
    isTimerRunning,
    resetTimer,
    startTimer,
    stopTimer,
    timeElapsed: Math.floor(timeElapsed / oneSecond),
    timeRemaining: Math.ceil(timeRemaining / oneSecond),
  };
};
