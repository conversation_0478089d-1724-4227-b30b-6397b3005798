// Collect all keys from the nested translation object structure
export function extractKeysFromSchema(
  schema: any,
  base?: string,
): Record<string, Array<string>> {
  const keys: Record<string, Array<string>> = {};

  for (const section in schema) {
    const qualifiedSection = base ? `${base}.${section}` : section;

    if (
      schema[section] !== null &&
      typeof schema[section] === 'object' &&
      !Array.isArray(schema[section])
    ) {
      Object.assign(
        keys,
        extractKeysFromSchema(schema[section], qualifiedSection),
      );

      continue;
    }

    const baseKey = base || 'root';
    keys[baseKey] = keys[baseKey] || [];
    keys[baseKey].push(section);
  }

  return keys;
}
