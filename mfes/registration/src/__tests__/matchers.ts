expect.extend({
  toBeEmptyCollection(
    received,
    { itemType = 'items', shouldFail = true } = {},
  ) {
    const pass = Array.isArray(received) && received.length === 0;
    const message = `expected no ${itemType}`;
    const emptyMessage = `${message} and none were found, which is correct`;
    const notEmptyMessage = `${message}, but found: [${received.join(', ')}]`;

    if (!pass && !shouldFail) {
      console.warn(notEmptyMessage);
    }

    return {
      message: () => (pass ? emptyMessage : notEmptyMessage),
      pass: pass || !shouldFail,
    };
  },
});
