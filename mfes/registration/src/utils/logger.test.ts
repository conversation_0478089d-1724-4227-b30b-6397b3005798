import clc from 'cli-color';

import packageJson from '../../package.json';
import { logger } from './logger';

describe('Logger functions', () => {
  it('should console debug', () => {
    const debugSpy = jest.spyOn(console, 'debug');

    logger.debug('Test');

    expect(debugSpy).toHaveBeenCalledWith(
      clc.white(`${packageJson.name} (${packageJson.version})`),
      'Test',
    );
  });
  it('should console error', () => {
    const errorSpy = jest.spyOn(console, 'error');

    logger.error('Test');

    expect(errorSpy).toHaveBeenCalledWith(
      clc.white(`${packageJson.name} (${packageJson.version})`),
      'Test',
    );
  });
  it('should console info', () => {
    const infoSpy = jest.spyOn(console, 'info');

    logger.info('Test');

    expect(infoSpy).toHaveBeenCalledWith(
      clc.white(`${packageJson.name} (${packageJson.version})`),
      'Test',
    );
  });
  it('should console log', () => {
    const logSpy = jest.spyOn(console, 'log');

    logger.log('Test');

    expect(logSpy).toHaveBeenCalledWith(
      clc.white(`${packageJson.name} (${packageJson.version})`),
      'Test',
    );
  });
  it('should console trace', () => {
    const traceSpy = jest.spyOn(console, 'trace');

    logger.trace('Test');

    expect(traceSpy).toHaveBeenCalledWith(
      clc.white(`${packageJson.name} (${packageJson.version})`),
      'Test',
    );
  });
  it('should console warn', () => {
    const warnSpy = jest.spyOn(console, 'warn');

    logger.warn('Test');

    expect(warnSpy).toHaveBeenCalledWith(
      clc.white(`${packageJson.name} (${packageJson.version})`),
      'Test',
    );
  });
});
