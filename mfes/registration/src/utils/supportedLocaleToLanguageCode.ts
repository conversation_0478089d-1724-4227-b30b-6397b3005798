import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';

import { LanguageCodeISO } from '../common/enums';

/**
 * Returns the ISO Language Code from the received locale.
 *
 * Locale codes are valid in any language (ISO 639) and region (ISO 3166)
 * combination but not part of any standard as a single entity.
 *
 * @example
 * supportedLocaleToLanguageCode('en-GB');
 * // returns "en"
 */
export const supportedLocaleToLanguageCode = (
  supportedLocale: SupportedLocales,
): LanguageCodeISO => {
  const langCode = supportedLocale?.slice(0, 2) as LanguageCodeISO;
  return Object.values(LanguageCodeISO).includes(langCode)
    ? langCode
    : LanguageCodeISO.ENGLISH;
};
