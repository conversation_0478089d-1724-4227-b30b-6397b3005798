// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ErrorBoundary when there are no errors should render the children 1`] = `
<Text>
  Test
</Text>
`;

exports[`ErrorBoundary when there is an error when FallbackComponent is defined as a prop should catch the error and render the props.FallbackComponent 1`] = `
<Text>
  Error!
</Text>
`;

exports[`ErrorBoundary when there is an error when FallbackComponent is not defined as a prop should catch the error and render the default FallbackComponent 1`] = `
<View
  onLayout={[Function]}
  style={
    Array [
      Object {
        "backgroundColor": "#fff",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "paddingBottom": 24,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 56,
      },
      Object {
        "paddingBottom": 0,
      },
    ]
  }
>
  <View
    style={
      Object {
        "alignItems": "center",
        "flexBasis": 0,
        "flexDirection": "column",
        "flexGrow": 1,
        "flexShrink": 1,
        "height": "50%",
        "justifyContent": "flex-start",
        "paddingTop": 266.8,
      }
    }
  >
    <View
      accessibilityLabel="Image"
      accessible={true}
      style={
        Object {
          "alignItems": "center",
          "maxHeight": 290,
          "width": "100%",
        }
      }
      testID="Image"
    >
      <SvgMock />
    </View>
    <View
      style={
        Object {
          "height": 24,
          "width": 1,
        }
      }
    />
    <Text
      style={
        Object {
          "color": "#000000",
          "fontSize": 24,
          "letterSpacing": 1.05,
          "lineHeight": 24,
        }
      }
    >
      Something went wrong
    </Text>
    <View
      style={
        Object {
          "height": 24,
          "width": 1,
        }
      }
    />
    <Text
      style={
        Object {
          "color": "#000000",
          "fontSize": 14,
          "letterSpacing": 0.7,
          "lineHeight": 23,
          "opacity": 0.9,
          "textAlign": "center",
        }
      }
    >
      Sorry, we're experiencing technical difficulties at the moment. Please try again later.
    </Text>
    <View
      style={
        Object {
          "height": 24,
          "width": 1,
        }
      }
    />
  </View>
  <View
    style={
      Object {
        "height": "50%",
        "justifyContent": "flex-end",
        "marginBottom": 32,
      }
    }
  >
    <View
      accessibilityLabel="Close"
      accessibilityRole="button"
      accessibilityState={
        Object {
          "busy": undefined,
          "checked": undefined,
          "disabled": false,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        Object {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        Object {
          "alignItems": "center",
          "backgroundColor": "#000096",
          "borderBottomLeftRadius": 23,
          "borderBottomRightRadius": 23,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 23,
          "borderTopRightRadius": 23,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 46,
          "opacity": 1,
          "paddingHorizontal": 25.5,
          "paddingVertical": 12,
        }
      }
    >
      <View>
        <Text
          disabled={false}
          inverted={false}
          isIconComponentRight={false}
          size="large"
          style={
            Object {
              "color": "#ffffff",
              "fontFamily": "Roboto-Regular",
              "fontSize": 15,
              "letterSpacing": 0.7,
              "textAlign": "center",
              "width": "auto",
            }
          }
          type="primary"
        >
          Close
        </Text>
      </View>
    </View>
  </View>
</View>
`;
