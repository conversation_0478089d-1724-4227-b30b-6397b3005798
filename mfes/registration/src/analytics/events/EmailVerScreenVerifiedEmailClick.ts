import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventEmailVerScreenVerifiedEmailClickPayload = {};

export type RegAnalyticsEventEmailVerScreenVerifiedEmailClickType = {
  type: RegAnalyticsEvent.EMAIL_VER_SCREEN_VERIFIED_EMAIL_CLICK;
  payload: RegAnalyticsEventEmailVerScreenVerifiedEmailClickPayload;
};

export const RegAnalyticsEventEmailVerScreenVerifiedEmailClick = (
  payload: RegAnalyticsEventEmailVerScreenVerifiedEmailClickPayload,
): RegAnalyticsEventEmailVerScreenVerifiedEmailClickType => ({
  type: RegAnalyticsEvent.EMAIL_VER_SCREEN_VERIFIED_EMAIL_CLICK,
  payload,
});
