import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventUnableToSaveDetailsErrorGoBackClickPayload = {};

export type RegAnalyticsEventUnableToSaveDetailsErrorGoBackClickType = {
  type: RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_GO_BACK_CLICK;
  payload: RegAnalyticsEventUnableToSaveDetailsErrorGoBackClickPayload;
};

export const RegAnalyticsEventUnableToSaveDetailsErrorGoBackClick = (
  payload: RegAnalyticsEventUnableToSaveDetailsErrorGoBackClickPayload,
): RegAnalyticsEventUnableToSaveDetailsErrorGoBackClickType => ({
  type: RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_GO_BACK_CLICK,
  payload,
});
