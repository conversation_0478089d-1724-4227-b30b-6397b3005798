import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventUnableToVerifyEmailCloseClickPayload = {};

export type RegAnalyticsEventUnableToVerifyEmailCloseClickType = {
  type: RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_CLOSE_CLICK;
  payload: RegAnalyticsEventUnableToVerifyEmailCloseClickPayload;
};

export const RegAnalyticsEventUnableToVerifyEmailCloseClick = (
  payload: RegAnalyticsEventUnableToVerifyEmailCloseClickPayload,
): RegAnalyticsEventUnableToVerifyEmailCloseClickType => ({
  type: RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_CLOSE_CLICK,
  payload,
});
