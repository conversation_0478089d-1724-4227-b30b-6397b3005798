import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventMarketingOptOutSavePayload = {};

export type RegAnalyticsEventMarketingOptOutSaveType = {
  type: RegAnalyticsEvent.MARKETING_OPT_OUT_SAVE;
  payload: RegAnalyticsEventMarketingOptOutSavePayload;
};

export const RegAnalyticsEventMarketingOptOutSave = (
  payload: RegAnalyticsEventMarketingOptOutSavePayload,
): RegAnalyticsEventMarketingOptOutSaveType => ({
  type: RegAnalyticsEvent.MARKETING_OPT_OUT_SAVE,
  payload,
});
