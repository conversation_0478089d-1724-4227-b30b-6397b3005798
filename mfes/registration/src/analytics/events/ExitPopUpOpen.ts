import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventExitPopUpOpenPayload = {};

export type RegAnalyticsEventExitPopUpOpenType = {
  type: RegAnalyticsEvent.EXIT_POP_UP_OPEN;
  payload: RegAnalyticsEventExitPopUpOpenPayload;
};
export const RegAnalyticsEventExitPopUpOpen = (
  payload: RegAnalyticsEventExitPopUpOpenPayload,
): RegAnalyticsEventExitPopUpOpenType => ({
  type: RegAnalyticsEvent.EXIT_POP_UP_OPEN,
  payload,
});
