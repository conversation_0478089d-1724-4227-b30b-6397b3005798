import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventUnableToSaveDetailsErrorOpenPayload = {
  errorMessage: any;
};

export type RegAnalyticsEventUnableToSaveDetailsErrorOpenType = {
  type: RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_OPEN;
  payload: RegAnalyticsEventUnableToSaveDetailsErrorOpenPayload;
};

export const RegAnalyticsEventUnableToSaveDetailsErrorOpen = (
  payload: RegAnalyticsEventUnableToSaveDetailsErrorOpenPayload,
): RegAnalyticsEventUnableToSaveDetailsErrorOpenType => ({
  type: RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_OPEN,
  payload,
});
