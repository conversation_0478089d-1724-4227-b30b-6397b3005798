import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventUnableToVerifyEmailOpenPayload = { errorMessage: any };

export type RegAnalyticsEventUnableToVerifyEmailOpenType = {
  type: RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_OPEN;
  payload: RegAnalyticsEventUnableToVerifyEmailOpenPayload;
};

export const RegAnalyticsEventUnableToVerifyEmailOpen = (
  payload: RegAnalyticsEventUnableToVerifyEmailOpenPayload,
): RegAnalyticsEventUnableToVerifyEmailOpenType => ({
  type: RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_OPEN,
  payload,
});
