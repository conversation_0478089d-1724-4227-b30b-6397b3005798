import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventUnableToSaveDetailsErrorLoginClickPayload = {};

export type RegAnalyticsEventUnableToSaveDetailsErrorLoginClickType = {
  type: RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_LOGIN_CLICK;
  payload: RegAnalyticsEventUnableToSaveDetailsErrorLoginClickPayload;
};

export const RegAnalyticsEventUnableToSaveDetailsErrorLoginClick = (
  payload: RegAnalyticsEventUnableToSaveDetailsErrorLoginClickPayload,
): RegAnalyticsEventUnableToSaveDetailsErrorLoginClickType => ({
  type: RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_LOGIN_CLICK,
  payload,
});
