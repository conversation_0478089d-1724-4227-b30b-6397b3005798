import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventEvAccountSetupSuccessfulPayload = {};

export type RegAnalyticsEventEvAccountSetupSuccessfulType = {
  type: RegAnalyticsEvent.EV_ACCOUNT_SETUP_SUCCESSFUL;
  payload: RegAnalyticsEventEvAccountSetupSuccessfulPayload;
};

export const RegAnalyticsEventEvAccountSetupSuccessful = (
  payload: RegAnalyticsEventEvAccountSetupSuccessfulPayload,
): RegAnalyticsEventEvAccountSetupSuccessfulType => ({
  type: RegAnalyticsEvent.EV_ACCOUNT_SETUP_SUCCESSFUL,
  payload,
});
