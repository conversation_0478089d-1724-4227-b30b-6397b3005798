import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventAddEmailAddressSendVerifClickPayload = {};

export type RegAnalyticsEventAddEmailAddressSendVerifClickType = {
  type: RegAnalyticsEvent.ADD_EMAIL_ADDRESS_SEND_VERIF_CLICK;
  payload: RegAnalyticsEventAddEmailAddressSendVerifClickPayload;
};

export const RegAnalyticsEventAddEmailAddressSendVerifClick = (
  payload: RegAnalyticsEventAddEmailAddressSendVerifClickPayload,
): RegAnalyticsEventAddEmailAddressSendVerifClickType => ({
  type: RegAnalyticsEvent.ADD_EMAIL_ADDRESS_SEND_VERIF_CLICK,
  payload,
});
