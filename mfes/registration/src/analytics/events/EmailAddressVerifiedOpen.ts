import { RegAnalyticsEvent } from '../index';

type RegAnalyticsEventEmailAddressVerifiedOpenPayload = {};

export type RegAnalyticsEventEmailAddressVerifiedOpenType = {
  type: RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_OPEN;
  payload: RegAnalyticsEventEmailAddressVerifiedOpenPayload;
};

export const RegAnalyticsEventEmailAddressVerifiedOpen = (
  payload: RegAnalyticsEventEmailAddressVerifiedOpenPayload,
): RegAnalyticsEventEmailAddressVerifiedOpenType => ({
  type: RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_OPEN,
  payload,
});
