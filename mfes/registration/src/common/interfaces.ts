import { ContextProps } from '@bp/pulse-auth-sdk';
import { SupportedBrands } from '@bp/pulse-shared-types/lib/types/SupportedBrands';
import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';
import { NavigationContainerRef } from '@react-navigation/native';
import { GraphQLError } from 'graphql';
import React from 'react';

import { RegAnalyticsEventType } from '../analytics';
import { AppCountry, ScreenName } from './enums';

export interface UserInfo {
  email?: string;
  firstName?: string;
  lastName?: string;
}

export interface AppCountryMap {
  [key: string]: AppCountry;
}

export interface GqlError {
  type: 'graphql';
  details: ReadonlyArray<GraphQLError>;
}

export interface NetworkError {
  type: 'network';
  details: Error;
}

export interface BusinessError {
  type: 'business';
  details: string;
}

export interface CustomiseCountry {
  appCountry: AppCountry;
  displayName: string;
  name: string;
  icon: string;
}

export interface BrandCountry {
  type: AppCountry;
  icon: string;
}

export interface FeatureFlags {
  enableCountrySelection: boolean;
  enableNewMarketingConsents: boolean;
  enableSpain: boolean;
  enableAralActivationMessage: boolean;
  brandCountries: Array<BrandCountry>;
}

export interface RegistrationProviderProps {
  children?: React.ReactNode;
  /**
   * Configuration object to enable/disable certain registration mfe features
   */
  featureFlags: FeatureFlags;
  /**
   * Locale set by user or device, used to localise strings, currencies etc
   */
  locale: SupportedLocales;
  /**
   * Host applications reference navigation container
   */
  navigation: NavigationContainerRef<{}>;
  /**
   * User Onboarding Completed
   */
  onboardingComplete: boolean;
  /**
   * User Onboarding Failed
   */
  onboardingFailed: boolean;
  /**
   * Privacy Policy URL
   * based on home country
   */
  privacyPolicy: string;
  /**
   * Terms and Conditions URL
   * based on home country
   */
  termsAndConditions: string;
  /**
   * Auth context
   */
  authContext: ContextProps;
  /**
   * Pricing Links
   */
  externalLinks?: ExternalLinkProps;
  /**
   * Brand determined by host app
   *
   */
  brand: SupportedBrands;
  /**
   * Allows the host application to hook into analytics events emitted from the MFE
   */
  onAnalyticsEvent: (event: RegAnalyticsEventType) => void;
  /**
   * Callback function to handle on email update/verification completion
   */
  onEmailVerificationCompleted: (verifiedEmail: string) => void;
  /**
   * Handles capture of errors that may be encountered within the MFE, use to surface errors in a host application
   */
  onError?: (errorPayload: GqlError | NetworkError | BusinessError) => any;
  /**
   * Handles the desired host applications action when exiting the MFE through user interaction i.e. pressing the close icon
   */
  onExitMFE: () => void;
  /**
   * Callback function to handle request for initiating onboarding
   */
  onInitiateOnboarding: () => void;
  /**
   * Callback function to handle triggering login functionality within the host app
   */
  onLogin: (loginUrl?: string) => void;
  /**
   * Callback function to handle triggering logout functionality within the host app
   */
  onLogout: () => void;
  /**
   * Callback function to handle completion of passwordless registration process
   */
  onRegistrationCompleted: () => void;
  /**
   * Callback function to handle completion of creation of SF IDP account
   */
  onUserIdCreated: (userId: string | null) => void;
}

/**
 * Represents a country as either an AppCountry enumerable or a string value literal of AppCountry.
 */
export type AppCountryType = AppCountry | `${AppCountry}`;

export type ExternalLinkProps = {
  de_pricing_link: string;
};

export type RootStackParamList = {
  [ScreenName.ACCOUNT_ONBOARDING]: {
    mockTriggerModal?: {
      mockTriggerDisplayOnboardingModal?: boolean;
    };
  };
  [ScreenName.ADD_EMAIL]: undefined;
  [ScreenName.AUTHENTICATE_ERROR]: undefined;
  [ScreenName.CUSTOMISE]: undefined;
  [ScreenName.EMAIL_ADDRESS_VERIFIED]: undefined;
  [ScreenName.EMAIL_VERIFICATION]: {
    hasPreviousVerificationEmail: boolean;
    registrationFlow: boolean;
    mockTriggerModal?: {
      mockTriggerEnableNewVerificationModal?: boolean;
      mockTriggerDisplayTakingLongerText?: boolean;
      mockTriggerDisplayOnboardingModal?: boolean;
    };
  };
  [ScreenName.TRANSIENT]: undefined;
  [ScreenName.UPDATE_EMAIL]: undefined;
  [ScreenName.WELCOME]: undefined;
};

/**
 * Key-value pairs where both the key and value are strings
 */
export interface KeyValuePairs {
  [key: string]: string;
}

export type NestedKeyValuePairs = {
  [key: string]: string | NestedKeyValuePairs;
};
