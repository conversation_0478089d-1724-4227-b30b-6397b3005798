{"name": "@bp/registration-mfe", "version": "4.3.2", "description": "MFE for handling registration of new users within SF IDP & account creation in Pulse platform", "license": "UNLICENSED", "author": "", "main": "src/index.ts", "types": "src/index.d.ts", "files": ["assets/", "dist/"], "scripts": {"android": "yarn pulse android", "prebuild": "npm run clean", "build": "tsc -p tsconfig.build.json && npm run copy && yarn postbuild", "postbuild": "cp README.md dist && cd dist && npm pkg set main='src/index.js'", "ci:danger": "yarn danger && DANGER_REPORT_PATH='../../../danger-report.json' npx pulse danger", "clean": "rm -rf dist", "copy": "copyfiles src/assets/**/*.png  dist/src/assets/images -f --error && copyfiles src/assets/**/*.svg dist/src/assets/images -f --error", "create:analytics:dir": "mkdir -p src/analytics && mkdir -p src/analytics/events && plop generateAnalyticsIndex", "danger": "DANGER_REPORT_PATH='./danger-report.json' npx danger local -b remotes/origin/main", "dev": "tsc --watch", "eject-react": "rm -rf node_modules/@bp/pulse-mobile-sdk/node_modules/react", "eject-react-native": "rm -rf node_modules/@bp/pulse-mobile-sdk/node_modules/react-native", "generate": "plop", "generate-analytics": "yarn create:analytics:dir && node scripts/analyticsScript.js && yarn lint:postAnalyticsGen", "postinstall": "yarn run eject-react && yarn run eject-react-native", "ios": "yarn pulse ios", "link": "yarn pulse link", "lint:postAnalyticsGen": "eslint --fix src/analytics/", "lint:prettier": "prettier --check .", "lint:scripts": "eslint --cache --ext .js,.jsx,.ts,.tsx .", "lint:styles": "stylelint '**/*.styles.ts' --allow-empty-input", "lint:vectors": "svgo $(find ./assets -name '*.svg')", "package:name": "echo $npm_package_name", "package:version": "echo $npm_package_version", "package:version:bump": "npm --no-git-tag-version version patch", "pod:install": "yarn pulse pods", "pre-push": "yarn types && yarn test:ci --silent", "start": "yarn pulse start", "test": "jest --watchAll --coverage -c jest.config.js", "test:ci": "jest --ci --coverage --passWithNoTests -c jest.config.js", "types": "tsc --noEmit"}, "dependencies": {"@bp/pulse-auth-sdk": "0.2.61", "@bp/pulse-shared-types": "1.3.0", "@bp/ui-components": "^12.12.0", "@react-navigation/native-stack": "^6.9.12", "axios": "^1.7.8", "debug": "4.3.7", "decode-uri-component": "0.3.0", "i18n-iso-countries": "^7.7.0", "json5": "1.0.2", "libphonenumber-js": "^1.10.15", "lodash.defaultsdeep": "^4.6.1", "yaml": "2.2.2", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "7.18.6", "@babel/preset-env": "7.19.4", "@babel/preset-typescript": "7.18.6", "@babel/runtime": "7.18.6", "@bp/eslint-plugin": "0.0.0-alpha-2", "@bp/pulse-mobile-sdk": "patch:@bp/pulse-mobile-sdk@patch%3A@bp/pulse-mobile-sdk@npm%253A3.11.0%23~/.yarn/patches/@bp-pulse-mobile-sdk-npm-3.9.0-0a9ae682e7.patch%3A%3Aversion=3.11.0&hash=d09af1#~/.yarn/patches/@bp-pulse-mobile-sdk-patch-46bef3765e.patch", "@bp/stylelint-config": "^0.0.0-alpha-2", "@calm/eslint-plugin-react-intl": "^1.4.1", "@react-navigation/stack": "^6.3.16", "@types/cli-color": "^2.0.2", "@types/jest": "^29.1.2", "@types/lodash.defaultsdeep": "^4.6.7", "@types/react": "^18.0.15", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "babel-jest": "^27.4.6", "babel-plugin-module-resolver": "4.1.0", "copyfiles": "^2.4.1", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.2.6", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-ft-flow": "^2.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react-native": "^4.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-sonarjs": "^0.21.0", "eslint-plugin-you-dont-need-lodash-underscore": "^6.12.0", "is-ci": "^3.0.1", "jest": "^26.6.3", "jest-environment-node": "^26.6.2", "jest-junit": "^13.0.0", "metro-react-native-babel-preset": "0.67.0", "plop": "^3.1.2", "postcss-scss": "^4.0.4", "prettier": "^2.7.1", "react-i18next": "11.18.1", "react-native-reanimated-carousel": "^3.3.0", "shelljs": "^0.8.5", "sort-package-json": "^1.54.0", "stylelint": "^14.9.1", "stylelint-config-styled-components": "^0.1.1", "stylelint-prettier": "^2.0.0", "stylelint-processor-styled-components": "^1.10.0", "stylelint-react-native": "^2.4.0", "svgo": "^2.8.0", "typescript": "^4.7.4"}, "peerDependencies": {"@bp/stylelint-config": "0.0.0-alpha-2", "@react-native-async-storage/async-storage": ">=1.17.9", "@react-native-community/eslint-config": "^3.0.3", "@testing-library/react-hooks": ">=8.0.1", "@testing-library/react-native": ">=10.1.1", "react": ">=18.2.0", "react-native": ">=0.72.8", "react-native-device-info": ">=9.0.2", "react-native-encrypted-storage": ">=4.0.3", "react-native-gesture-handler": ">=2.19.0", "react-native-get-random-values": ">=1.10.0", "react-native-inappbrowser-reborn": ">=3.7.0", "react-native-screens": ">=3.15.0", "react-native-tracking-transparency": ">=0.1.1", "styled-components": ">=6.0.0"}, "publishConfig": {"main": "src/index.js"}, "installConfig": {"hoistingLimits": "workspaces"}}