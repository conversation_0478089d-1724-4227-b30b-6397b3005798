/* eslint-disable no-undef */
require('react-native-gesture-handler/jestSetup');
jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

jest.mock('react-native-encrypted-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
}));

jest.mock('query-string', () => ({
  parse: jest.fn(),
  stringify: jest.fn(),
}));

const mockRNDeviceInfo = require('react-native-device-info/jest/react-native-device-info-mock');
jest.mock('react-native-device-info', () => mockRNDeviceInfo);

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  // So we override it with a no-op
  Reanimated.default.call = () => {};

  return Reanimated;
});

jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

jest.mock('styled-components', () => ({
  useTheme: () =>
    jest.fn().mockReturnValue({
      registrationMfe: { buttons: { secondary: { grey: '#111111a2' } } },
    }),
}));

jest.mock('./src/providers/HostNavigationProvider', () => {
  const React = require('react');
  return {
    useHostNavigation: jest.fn().mockReturnValue({
      canGoBack: jest.fn(),
      navigate: jest.fn(),
      navigateWithKey: jest.fn(),
      navigation: jest.fn(),
    }),
    HostNavigationProvider: ({ children }) =>
      React.createElement('div', {}, children),
  };
});

jest.mock('./src/providers/SettingsProvider', () => {
  const React = require('react');
  const originalModule = jest.requireActual('./src/providers/SettingsProvider');
  const messages = require('./src/translations/messages.json');
  const { iterateAndReplace } = require('./src/utils/iterateAndReplace.ts');
  const Enums = require('./src/common/enums.ts');
  const {
    SupportedBrand,
  } = require('@bp/pulse-shared-types/lib/enums/SupportedBrands');
  const {
    SupportedLocale,
  } = require('@bp/pulse-shared-types/lib/enums/SupportedLocale');

  const {
    AppCountry,
    PRIVACY_POLICY_URL,
    PRIVACY_POLICY_TYPE,
    TERMS_AND_CONDITIONS_TYPE,
    TERMS_AND_CONDITIONS_URL,
    DEFAULT_BRAND_APP_COUNTRY,
  } = Enums;

  const brand = SupportedBrand.BP;
  const country = AppCountry.UK;
  const defaultAppCountry = DEFAULT_BRAND_APP_COUNTRY[brand];
  const locale = SupportedLocale.EN_GB;
  const privacyPolicy = PRIVACY_POLICY_URL[country];
  const privacyPolicyType = PRIVACY_POLICY_TYPE[country];
  const termsAndConditions = TERMS_AND_CONDITIONS_URL[country];
  const termsAndConditionsType = TERMS_AND_CONDITIONS_TYPE[country];

  const email = '<EMAIL>';
  const firstName = 'first name';
  const lastName = 'last name';

  const t = iterateAndReplace(messages, { brand });

  const mockSettingsOutput = {
    authContext: {
      authenticated: true,
      consents: [],
      consentsValid: false,
      getAccessToken: jest.fn(),
      getConsents: jest.fn(),
      getIdToken: jest.fn(),
      getUser: jest.fn(),
      initialised: true,
      iOSTrackingAllowed: true,
      loading: false,
      loginOrRegister: jest.fn(),
      logout: jest.fn(),
      updateConsent: jest.fn(),
      updateContact: jest.fn(),
      user: {
        country,
        email,
        emailVerified: true,
        externalId: 'SF IDP ID',
        firstName,
        isActive: true,
        lastName,
        nickName: '',
        userId: 'CIP ID',
      },
    },
    brand,
    country,
    defaultAppCountry,
    featureFlags: {
      enableCountrySelection: true,
      enableSpain: true,
      enableNewMarketingConsents: true,
      brandCountries: [],
    },
    locale,
    onboardingComplete: false,
    onboardingFailed: false,
    privacyPolicy,
    privacyPolicyType,
    t,
    termsAndConditions,
    termsAndConditionsType,
    onAnalyticsEvent: jest.fn(),
    onEmailVerificationCompleted: jest.fn(),
    onError: jest.fn(),
    onExitMFE: jest.fn(),
    onInitiateOnboarding: jest.fn(),
    onLogin: jest.fn(),
    onLogout: jest.fn(),
    onRegistrationCompleted: jest.fn(),
    onUserIdCreated: jest.fn(),
    setCountry: jest.fn(),
  };

  return {
    ...originalModule,
    useSettings: jest.fn(() => mockSettingsOutput),
    SettingsContext: {
      ...originalModule.SettingsContext,
      Provider: ({ children }) => (
        <originalModule.SettingsContext.Provider value={mockSettingsOutput}>
          {children}
        </originalModule.SettingsContext.Provider>
      ),
    },
  };
});

jest.mock('./src/providers/ErrorBoundary', () => {
  const React = require('react');
  return {
    ErrorBoundary: ({ children }) =>
      React.createElement(React.Fragment, {}, children),
  };
});
