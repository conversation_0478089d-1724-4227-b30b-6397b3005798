const transformIgnorePackages = [
  '@bp/*',
  '@react-native/*',
  '@react-navigation/*',
  'react-native-*',
  'react-native',
  'react-navigation',
  'axios',
  'decode-uri-component',
  'filter-obj',
  'split-on-first',
  'query-string',
];

const transformIgnorePatterns = `node_modules/(?!${transformIgnorePackages.join(
  '|',
)})`;

// all tests which are date dependent will use the GMT time zone
process.env.TZ = 'GMT';

module.exports = {
  preset: 'react-native',
  testEnvironment: 'node',
  timers: 'fake',
  transformIgnorePatterns: [transformIgnorePatterns],
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: 'test-results/jest' }],
  ],
  moduleNameMapper: {
    '\\.svg': '<rootDir>/.jest/modules/svg.js',
    '^react$': '<rootDir>/../../node_modules/react',
    '^react-native$': '<rootDir>/../../node_modules/react-native',
  },
  coverageReporters: [
    'json',
    'lcov',
    'text',
    'clover',
    'text-summary',
    'cobertura',
  ],
  collectCoverageFrom: ['<rootDir>/src/**/*.{ts,tsx}'],
  rootDir: '.',
  roots: ['<rootDir>/src'],
  testTimeout: 20000,
  setupFiles: ['./jest.setup.js'],
  setupFilesAfterEnv: ['./src/__tests__/matchers.ts'],
  testPathIgnorePatterns: [
    '<rootDir>/src/__tests__/matchers.ts',
    '<rootDir>/src/__tests__/constants/',
    '<rootDir>/src/__tests__/utils/',
  ],
  modulePaths: ['<rootDir>/../../node_modules', '<rootDir>/node_modules'],
};
