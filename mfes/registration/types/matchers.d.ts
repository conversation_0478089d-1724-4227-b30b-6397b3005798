import 'jest';

declare global {
  namespace jest {
    interface Matchers<R> {
      /**
       * Ensures that a collection is empty. If it's not, and shouldFail is true, the test will fail.
       * @param config Object containing an optional `itemType` describing the items in the collection
       * and an optional `shouldFail` flag. Defaults to 'items' if itemType is not provided.
       */
      toBeEmptyCollection(config?: {
        itemType?: string;
        shouldFail?: boolean;
      }): R;
    }
  }
}
