import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput } from 'react-native';

interface Props {
  onValueChange?: (value: string) => void;
  placeholder?: string;
  value?: string;
}

export const SandboxInput: React.FC<Props> = ({
  onValueChange,
  placeholder,
  value,
}) => {
  const [inputValue, setInputValue] = useState<string>(value || '');

  useEffect(() => {
    setInputValue(value ?? '');
  }, [value]);

  const handleChangeText = (text: string) => {
    setInputValue(text);
    if (onValueChange) {
      onValueChange(text);
    }
  };

  return (
    <TextInput
      style={styles.input}
      placeholder={placeholder}
      value={inputValue}
      onChangeText={handleChangeText}
    />
  );
};

const styles = StyleSheet.create({
  input: {
    height: 40,
    width: 150,
    borderColor: 'gray',
    borderWidth: 1,
    paddingLeft: 10,
    paddingRight: 10,
  },
});
