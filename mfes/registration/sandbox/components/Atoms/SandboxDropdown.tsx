import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { s } from '../../../src/utils/scale';

export interface SandboxDropdownOption<T = string> {
  label: string;
  value: T;
}

export interface SandboxDropdownProps<T = string> {
  options: SandboxDropdownOption<T>[];
  selectedValue: SandboxDropdownOption<T>;
  onSelect: (option: SandboxDropdownOption<T>) => void;
}

export const SandboxDropdown = <T,>({
  options,
  selectedValue,
  onSelect,
}: SandboxDropdownProps<T>) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <View>
      <TouchableOpacity onPress={() => setIsVisible(true)}>
        <Text
          style={styles.selectedText}
          numberOfLines={1}
          ellipsizeMode={'clip'}
        >
          {selectedValue.label}
        </Text>
      </TouchableOpacity>

      {isVisible && (
        <Modal
          transparent={true}
          animationType="fade"
          visible={isVisible}
          onRequestClose={() => setIsVisible(false)}
        >
          <TouchableOpacity
            onPress={() => setIsVisible(false)}
            style={styles.modalContainer}
            activeOpacity={1}
          >
            <View style={styles.optionsContainer}>
              {options.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.option,
                    option.value === selectedValue.value
                      ? styles.selectedOption
                      : {},
                  ]}
                  onPress={() => {
                    onSelect(option);
                    setIsVisible(false);
                  }}
                >
                  <Text style={styles.text}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </TouchableOpacity>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  selectedText: {
    padding: s(12),
    borderWidth: 1,
    borderColor: 'grey',
    width: 150,
  },
  selectedOption: {
    backgroundColor: '#ebecf3',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: s(24),
  },
  optionsContainer: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 5,
    elevation: 10,
  },
  option: {
    paddingLeft: 16,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: 'grey',
    backgroundColor: 'white', // background color is necessary for shadows to appear on iOS

    // iOS shadow properties
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,

    // Android shadow
    elevation: 8,
  },
  text: {
    fontSize: 24,
  },
});
