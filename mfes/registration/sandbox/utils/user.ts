import { UserCIP, UserIdentity } from '@bp/pulse-auth-sdk/dist/types';
import {
  ContextProps,
  SFConsentUpdate,
  SFUserConsent,
  UpdateUserIdentityResponse,
  UserContact,
} from '@bp/pulse-auth-sdk';
import { SupportedLocales } from '@bp/pulse-shared-types/src/types/SupportedLocales';

export const MOCK_USER_IDENTITY: UserIdentity = {
  creationDateTime: '',
  email: '',
  emailVerified: false,
  externalId: '',
  locale: '',
  phoneNumber: '',
  phoneNumberVerified: false,
};

export const MOCK_USER_CONTACT: UserContact = {
  addressLine1: '',
  addressLine2: '',
  city: '',
  country: '',
  firstName: '',
  isActive: false,
  languageKey: '',
  lastName: '',
  loyaltyCardNumber: '',
  mobileWalletActivated: false,
  nickName: '',
  postCode: '',
  state: '',
};

export const MOCK_USER: UserCIP = {
  ...MOCK_USER_CONTACT,
  ...MOCK_USER_IDENTITY,
  userId: '1234567890',
};

export const MOCK_AUTH_PROPERTIES = {
  authenticated: false,
  consents: [],
  consentsValid: false,
  iOSTrackingAllowed: true,
  initialised: false,
  loading: false,
  user: { ...MOCK_USER },
};
export const MOCK_AUTH_METHODS = {
  getAccessToken(_forceNew: boolean | undefined): Promise<string | null> {
    return Promise.resolve('');
  },
  getConsents(): Promise<SFUserConsent[] | undefined> {
    return Promise.resolve([]);
  },
  getIdToken(_forceNew: boolean | undefined): Promise<string | null> {
    return Promise.resolve('');
  },
  getUser(): Promise<UserCIP | undefined> {
    return Promise.resolve(undefined);
  },
  loginOrRegister(_userLocale: `${SupportedLocales}`): Promise<void> {
    return Promise.resolve(undefined);
  },
  logout(): Promise<void> {
    return Promise.resolve(undefined);
  },
  updateConsent(_updates: SFConsentUpdate[]): Promise<void> {
    return Promise.resolve(undefined);
  },
  updateContact(_contact: Partial<UserContact>): Promise<void> {
    return Promise.resolve(undefined);
  },
  updateEmail(_email: string): Promise<UpdateUserIdentityResponse | undefined> {
    return Promise.resolve(undefined);
  },
  updatePhone(
    _phoneNumber: string,
  ): Promise<UpdateUserIdentityResponse | undefined> {
    return Promise.resolve(undefined);
  },
  updateTrackingConsent(_consent: boolean): Promise<void> {
    return Promise.resolve(undefined);
  },
  verifyPhone(_sessionId: string, _code: string): Promise<void> {
    return Promise.resolve(undefined);
  },
};

export const MOCK_AUTH: ContextProps = {
  ...MOCK_AUTH_METHODS,
  ...MOCK_AUTH_PROPERTIES,
};
