import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';

export const navigation = createNavigationContainerRef();

// Returns the current navigation stack
export const getNavigation = () => navigator;

// Custom navigate handler allowing stack to be reset in props
export const navigate = (
  screenName: string,
  params?: any,
  resetStack: boolean | undefined = false,
) => {
  const isNavigation = getNavigation();

  // Prevent navigation actions if stack does not exist
  if (!isNavigation) {
    return null;
  }
  if (resetStack) {
    return navigation.reset({
      index: 0,
      routes: [{ name: screenName, params }],
    });
  }
  return navigation.dispatch(
    CommonActions.navigate({
      name: screenName,
      params,
    }),
  );
};

// Returns a boolean based on if the user can go back further than the current screen
export const canGoBack = () => navigation.canGoBack();
