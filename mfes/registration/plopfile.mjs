import * as fs from 'fs';

const formatAnalyticsEventName = (eventName) => {
  const regExp = /(?<=[a-z])(?=[A-Z])/;
  const removedUnders = removeName(eventName).split('_').join('');
  return removedUnders.split(regExp).join('_').toUpperCase();
};

const removeName = (eventName) => {
  return eventName.split('Reg_').join('');
};

const formatAnalyticsEventFileName = (eventName) => {
  return removeName(eventName).split('_').join('');
};

export default function (plop) {
  plop.setGenerator('analyticsEvent', {
    description: '🐸 Generates new Analytics Event 🐸',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: "What's the name of the analytics event?",
        validate: function (value) {
          let message = true;
          const fileName = formatAnalyticsEventFileName(value);
          if (!/.+/.test(value)) {
            message = console.error('Missing', [
              'you must define an analytics event name',
            ]);
          } else if (fs.existsSync(`./src/analytics/events/${fileName}.ts`)) {
            message = console.error(' already exists', [
              `"${value}" is not valid`,
            ]);
          }
          return message;
        },
      },
    ],
    actions: (data) => {
      const fileName = formatAnalyticsEventFileName(data.name);
      const eventName = formatAnalyticsEventName(data.name);
      console.log({ fileName, eventName });
      return [
        {
          type: 'add',
          path: `./src/analytics/events/${fileName}.ts`,
          templateFile: 'generator/analyticsTemplate.hbs',
          data: {
            fileName,
            eventName,
          },
        },
        {
          type: 'append',
          path: './src/analytics/index.ts',
          pattern: /^(?<![\s\S\r])/,
          data: {
            fileName,
          },
          template: `import { RegAnalyticsEvent{{fileName}}Type } from './events/{{fileName}}';`,
        },
        {
          type: 'append',
          path: './src/analytics/index.ts',
          pattern: /export enum RegAnalyticsEvent \{/,
          data: {
            eventName,
          },
          template: `{{eventName}} = 'RegAnalyticsEvent.{{eventName}}',`,
        },
        {
          type: 'append',
          path: './src/analytics/index.ts',
          pattern: /export type RegAnalyticsEventType \=/,
          data: {
            fileName,
          },
          separator: `| \n`,
          template: `RegAnalyticsEvent{{fileName}}Type `,
        },
      ];
    },
  });
  plop.setGenerator('generateAnalyticsIndex', {
    description: '🐸 Generates analytics index if none exists 🐸',
    prompts: [],
    actions: [
      {
        type: 'add',
        skipIfExists: true,
        path: `./src/analytics/index.ts`,
        templateFile: 'generator/analyticsIndexTemplate.hbs',
      },
    ],
  });
}
