{"name": "@bp/credit-mfe", "version": "1.2.0", "description": "BP MFE for handling Credit flow", "license": "UNLICENSED", "author": "", "main": "index.ts", "types": "index.d.ts", "scripts": {"android": "yarn pulse android", "prebuild": "yarn clean", "build": "yarn prebuild && tsc --project tsconfig.build.json && npm run copy && yarn postbuild", "postbuild": "cp README.md dist && cd dist && npm pkg set main='index.js'", "ci:danger": "yarn danger && DANGER_REPORT_PATH='../../../danger-report.json' npx pulse danger", "clean": "rm -rf dist", "cm": "cz", "copy": "copyfiles src/assets/**/*.ttf dist/src/assets/fonts -f && copyfiles src/assets/**/*.png dist/src/assets/images/png -f & copyfiles src/assets/**/*.svg dist/src/assets/images/svg -f", "create": "npx react-native init exampleapp --template react-native-template-typescript@6.10.4 --version 0.68.2", "danger": "DANGER_REPORT_PATH='./danger-report.json' npx danger local -b remotes/origin/next", "dev": "tsc --watch", "eject-react": "rm -rf node_modules/@bp/pulse-mobile-sdk/node_modules/react", "eject-react-native": "rm -rf node_modules/@bp/pulse-mobile-sdk/node_modules/react-native", "postinstall": "yarn run eject-react && yarn run eject-react-native", "ios": "yarn pulse ios", "link": "yarn pulse link", "lint": "yarn lint:prettier && yarn lint:scripts && yarn lint:styles", "lint:prettier": "prettier --write .", "lint:scripts": "eslint --cache --ext .js,.jsx,.ts,.tsx .", "lint:styles": "stylelint '**/*.styles.ts' --allow-empty-input", "lint:vectors": "svgo $(find ./assets -name '*.svg')", "package:name": "echo $npm_package_name", "package:version": "echo $npm_package_version", "package:version:bump": "npm --no-git-tag-version version patch", "pod:install": "yarn pulse pods", "pre-push": "yarn types && yarn test:ci --silent", "start": "yarn pulse start", "test": "jest --watchAll --coverage -c jest.config.js", "test:ci": "jest --ci --coverage --passWithNoTests -c jest.config.js", "types": "tsc --noEmit"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@apollo/client": "3.7.3", "@bp/mfe-helper-apollo": "0.0.1-alpha-1", "@bp/ui-components": "^12.15.0", "@types/crypto-js": "^4.1.1", "@types/lodash.defaultsdeep": "^4.6.9", "axios": "^1.7.9", "crypto-js": "^4.1.1", "date-fns": "^2.29.3", "graphql": "^16.5.0", "graphql-tag": "2.11.0", "is-ci": "^3.0.1", "jwt-decode": "^3.1.2", "react-native-device-info": "10.2.0", "react-native-firebase": "^5.6.0"}, "devDependencies": {"@babel/core": "7.18.6", "@babel/plugin-proposal-decorators": "^7.20.5", "@babel/preset-env": "7.19.4", "@babel/preset-typescript": "7.18.6", "@babel/runtime": "7.18.6", "@bp/eslint-plugin": "0.0.0-alpha-2", "@bp/pulse-mobile-sdk": "patch:@bp/pulse-mobile-sdk@patch%3A@bp/pulse-mobile-sdk@npm%253A3.11.0%23~/.yarn/patches/@bp-pulse-mobile-sdk-npm-3.9.0-0a9ae682e7.patch%3A%3Aversion=3.11.0&hash=d09af1#~/.yarn/patches/@bp-pulse-mobile-sdk-patch-46bef3765e.patch", "@bp/stylelint-config": "0.0.0-alpha-2", "@react-native-community/eslint-config": "^3.0.3", "@types/jest": "^29.1.2", "@types/react": "^18.0.15", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "babel-jest": "^27.4.6", "babel-plugin-module-resolver": "5.0.0", "copyfiles": "^2.4.1", "cz-conventional-changelog": "3.3.0", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.2.6", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-ft-flow": "2.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react-native": "^4.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-sonarjs": "^0.13.0", "eslint-plugin-you-dont-need-lodash-underscore": "^6.12.0", "jest": "^26.6.3", "jest-environment-node": "^26.6.2", "jest-junit": "^13.0.0", "lodash": "^4.17.21", "lodash.defaultsdeep": "^4.6.1", "metro-react-native-babel-preset": "0.67.0", "postcss-scss": "^4.0.4", "prettier": "^2.7.1", "react-native-get-random-values": "^1.8.0", "react-native-reanimated-carousel": "^3.3.0", "react-test-renderer": "^17.0.2", "sort-package-json": "^1.54.0", "stylelint": "^14.9.1", "stylelint-config-styled-components": "^0.1.1", "stylelint-prettier": "^2.0.0", "stylelint-processor-styled-components": "^1.10.0", "stylelint-react-native": "^2.4.0", "svgo": "^2.8.0", "tsconfig-paths": "^4.1.0", "typescript": "^4.7.4"}, "peerDependencies": {"@react-native-async-storage/async-storage": "^1.17.9", "@react-navigation/native": ">=6.0.10", "@react-navigation/stack": ">=6.2.1", "@testing-library/react-native": ">=12.3.0", "react": ">=18.2.0", "react-native": ">=0.72.8", "react-native-gesture-handler": ">=2.19.0", "react-native-screens": ">=3.15.0", "react-native-svg": ">=12.4.4", "react-native-webview": ">=12.0.0", "styled-components": ">=6.0.0"}, "publishConfig": {"main": "index.js"}, "installConfig": {"hoistingLimits": "workspaces"}}