import React, {
  createContext,
  ReactElement,
  useContext,
  useState,
} from 'react';

interface IConfigContext {
  config: any;
  setConfig: React.Dispatch<React.SetStateAction<Object>>;
}

export const ConfigContext = createContext<IConfigContext>({
  config: {},
  setConfig: () => {},
});

export const useConfig = (): IConfigContext => useContext(ConfigContext);
const ConfigProvider = ({ children }: { children: ReactElement }) => {
  const [config, setConfig] = useState({});

  return (
    <ConfigContext.Provider
      value={{
        config,
        setConfig,
      }}
    >
      {children}
    </ConfigContext.Provider>
  );
};

export default ConfigProvider;
