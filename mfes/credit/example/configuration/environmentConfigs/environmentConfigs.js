import env from 'react-native-config';

import { UK } from '../users';

const {
  DEV_GATEWAY_API_KEY_NL,
  DEV_GATEWAY_API_KEY_DE,
  DEV_STRIPE_URL,
  TEST_GATEWAY_API_KEY_NL,
  TEST_GATEWAY_API_KEY_DE,
  TEST_STRIPE_URL,
  PREPROD_GATEWAY_API_KEY_NL,
  PREPROD_GATEWAY_API_KEY_DE,
  PREPROD_STRIPE_URL,
  PERF_GATEWAY_API_KEY_NL,
  PERF_GATEWAY_API_KEY_DE,
  PERF_STRIPE_URL,
} = env;

export const locales = ['en', 'de', 'nl', 'us', 'es', 'fr'];

export const environmentConfigs = {
  LOCAL: {
    privateGateway: 'http://localhost:4030/graphql',
    publicGateway: 'http://localhost:4031/graphql',
    stripeMicrosite: '',
    apiKey: {
      UK: 'xxxx',
      US: 'xxxx',
      NL: 'xxxx',
      DE: 'xxxx',
    },
    apiKeyUS: 'xxxx',
    apiKeyNL: 'xxxx',
    apiKeyDE: 'xxxx',
    accounts: {
      UK: UK.LOCAL,
    },
  },
  DEV: {
    privateGateway: 'https://api-ev-app-dev.bp.com/vx/user',
    publicGateway: 'https://api-ev-app-dev.bp.com/vx/guest',
    stripeMicrosite: DEV_STRIPE_URL,
    apiKey: {
      UK: DEV_GATEWAY_API_KEY_NL,
      US: DEV_GATEWAY_API_KEY_NL,
      NL: DEV_GATEWAY_API_KEY_NL,
      DE: DEV_GATEWAY_API_KEY_DE,
    },
    accounts: {
      UK: UK.DEV,
    },
  },
  TEST: {
    privateGateway: 'https://api-ev-app-test.bp.com/vx/user',
    publicGateway: 'https://api-ev-app-test.bp.com/vx/guest',
    stripeMicrosite: TEST_STRIPE_URL,
    apiKey: {
      UK: TEST_GATEWAY_API_KEY_NL,
      US: TEST_GATEWAY_API_KEY_NL,
      NL: TEST_GATEWAY_API_KEY_NL,
      DE: TEST_GATEWAY_API_KEY_DE,
    },
    accounts: {
      UK: UK.TEST,
    },
  },
  PREPROD_V7: {
    privateGateway: 'https://preprod.ev-plaform-bjk9ynhzkw.com/v7/user',
    publicGateway: 'https://preprod.ev-plaform-bjk9ynhzkw.com/v7/guest',
    stripeMicrosite: PREPROD_STRIPE_URL,
    apiKey: {
      UK: PREPROD_GATEWAY_API_KEY_NL,
      US: PREPROD_GATEWAY_API_KEY_NL,
      NL: PREPROD_GATEWAY_API_KEY_NL,
      DE: PREPROD_GATEWAY_API_KEY_DE,
    },
    accounts: {
      UK: UK.PREPROD,
    },
  },
  PERFORMANCE: {
    privateGateway: 'https://api-ev-app-performance.bp.com/v7/user',
    publicGateway: 'https://api-ev-app-performance.bp.com/v7/guest',
    stripeMicrosite: PERF_STRIPE_URL,
    apiKey: {
      UK: PERF_GATEWAY_API_KEY_NL,
      US: PERF_GATEWAY_API_KEY_NL,
      NL: PERF_GATEWAY_API_KEY_NL,
      DE: PERF_GATEWAY_API_KEY_DE,
    },
    accounts: {
      UK: UK.PREPROD,
    },
  },
};
