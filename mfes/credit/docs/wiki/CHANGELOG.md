# [@bp/credit-mfe-v1.2.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/credit/v1.1.0...credit/v1.2.0) (2025-06-26)

# [@bp/credit-mfe-v1.1.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/credit/v1.0.1...credit/v1.1.0) (2025-06-05)

### Bug Fixes

- **translations:** new translations messages.json from Crowdin ([279a198](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/279a198bf51b743e8f9bdf07a07299ee5659ba7e))
- Types in monorepo ([3c4c7cc](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/3c4c7ccae5e8b8d1c2c58ce53fe7102eb6f2e249))

### Features

- airship secrets ([1f3d24a](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/1f3d24a660b4e513aa60d75020240908324d3261))

# [@bp/credit-mfe-v1.0.1](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/credit/v1.0.0...credit/v1.0.1) (2024-10-03)

# [@bp/credit-mfe-v1.0.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/branchCompare?baseVersion=GTcredit/v0.17.0&targetVersion=GTcredit/v1.0.0&_a=commits) (2024-07-26)

- refactor(credit): Publish the dist folder and modify package main ([ca4c6b4](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/ca4c6b40bfcbc6f080c412e32d8a5ae9a6b8e769)) #6593830

### BREAKING CHANGES

- refactor(credit): Publish the dist folder and modify main for local & published use

Instead of the published package including the dist folder, we publish the contents of dist.
This allows us to have consistant subdir imports when using the package in a local monorepo workspace & from the registry.

Also added a postbuild script to add the readme & modify the package.json main value from ts to the compiled js index

# [0.17.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.16.1&targetVersion=GTv0.17.0&_a=commits) (2024-03-26)

### Features

- new CrowdIn translations ([5054828](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/505482870d937bef5ea6d88e5c3e81f7badd5b9f)) #5604944

## [0.16.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.16.0&targetVersion=GTv0.16.1&_a=commits) (2024-02-07)

### Bug Fixes

- added new languages to the selector ([49b9daa](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/49b9daadbd87aa35fb0871cdb765a660d33f1482)) #5724244

# [0.16.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.15.0&targetVersion=GTv0.16.0&_a=commits) (2024-01-31)

### Features

- create fallback for translations ([7d8a9c5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/7d8a9c5064a2c0a5a840142313b6c4f12f1ff0cb)) #5724244

# [0.15.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.14.0&targetVersion=GTv0.15.0&_a=commits) (2024-01-17)

### Features

- update min credit to be feature flag ([839f857](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/839f85756408bc3b491b61313ef049422258701f)) #6087641

# [0.14.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.13.0&targetVersion=GTv0.14.0&_a=commits) (2024-01-15)

### Features

- new CrowdIn translations ([6a136b4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/6a136b41d16915149496c3f1d1e30aa4726b01a7)) #5799981

# [0.13.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.12.1&targetVersion=GTv0.13.0&_a=commits) (2024-01-15)

### Features

- new CrowdIn translations ([fd79213](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/fd792131b951171070d326c04326a3c1e2badd89)) #5799981

## [0.12.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.12.0&targetVersion=GTv0.12.1&_a=commits) (2023-12-14)

### Bug Fixes

- release credit-mfe ([fb9820b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/fb9820be69e38b942ded76ae966bde039e22d8ea)) #5848828

# [0.12.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.11.1&targetVersion=GTv0.12.0&_a=commits) (2023-12-08)

### Features

- new CrowdIn translations ([b979300](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/b9793003cd36360fb3c5351cf21636d4f82f7d56)) #5933764

### Reverts

- **reverting the dummy translations:** discard the dummy translations ([8de16fd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/8de16fdc8d318c4ba796ac0f8b1334ada330ab1e)) #5799981

## [0.11.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.11.0&targetVersion=GTv0.11.1&_a=commits) (2023-11-27)

### Bug Fixes

- **adding dummy translations to re-update crowdin:** added dummy translations... ([17b432e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/17b432e8ae2804ecd54e45c26a0dae641f674296)) #5799981

# [0.11.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.10.0&targetVersion=GTv0.11.0&_a=commits) (2023-11-24)

### Features

- new CrowdIn translations ([88fd924](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/88fd9244f298979abe171201dfe8c4155c96b793)) #5604944

# [0.10.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.9.2&targetVersion=GTv0.10.0&_a=commits) (2023-11-24)

### Features

- new CrowdIn translations ([64e0e49](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/64e0e49d079649a75da21a468b8df6cd025d2f73)) #5604944

## [0.9.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.9.1&targetVersion=GTv0.9.2&_a=commits) (2023-11-24)

### Bug Fixes

- **updated credit amount text:** updated credit amount text ([d2dda1c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/d2dda1c4baf1fa7814ac0b17fc1c3fc958046fbc)) #5799981

## [0.9.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.9.0&targetVersion=GTv0.9.1&_a=commits) (2023-11-23)

### Bug Fixes

- **updated credit amount text:** updated credit amount text ([3050ad0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/3050ad0b44bebbc0a30bed3804c53d713478acd2)) #5799981

# [0.9.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.8.1&targetVersion=GTv0.9.0&_a=commits) (2023-09-28)

### Features

- **packages:** remove the vulnerabilities and packages we do not use ([7017660](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/7017660e13e22706926a1c087c95a31ca8c0a952)) #4687915

## [0.8.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.8.0&targetVersion=GTv0.8.1&_a=commits) (2023-09-18)

### Bug Fixes

- **translations:** replace hardcoded strings ([f8e5ae3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/f8e5ae3ed38ef3469e88cf8635e07a60311d8a24)) #5551529

# [0.8.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.7.0&targetVersion=GTv0.8.0&_a=commits) (2023-09-17)

### Features

- new CrowdIn translations ([24333c8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/24333c82eaf2a6f519ba405ab0cdfa61930e1642)) #5498937

# [0.7.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.6.0&targetVersion=GTv0.7.0&_a=commits) (2023-09-15)

### Features

- **crowdin:** configure crowdin ([dd436d9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/dd436d97c1701b1e5b9cc737c11d30c0216c7aa6)) #5498937
  #5499103

# [0.6.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.5.2&targetVersion=GTv0.6.0&_a=commits) (2023-07-17)

### Features

- **security:** updated checkmarx config and removed old config ([378b97b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/378b97b80966fe1c1b5ce5f5320cefbbf1456f20)) #5078031

## [0.5.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.5.1&targetVersion=GTv0.5.2&_a=commits) (2023-07-03)

### Bug Fixes

- **all repo:** sonarqube fixes ([c4d038c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/c4d038cf9f8faf1699e793ad7d281af3997379f3)) #4747080

## [0.5.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.5.0&targetVersion=GTv0.5.1&_a=commits) (2023-06-22)

### Bug Fixes

- **addcredit:** move onTopUpCredit to correct place ([328feaa](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/328feaa1f074c8364e0cb67bea25afc590baba60)) #5138892

# [0.5.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.4.0&targetVersion=GTv0.5.0&_a=commits) (2023-05-02)

### Features

- add creditBalance to analytics object ([ccbb14f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/ccbb14f595471710978dbd92b9899de55ce4b203)) #4708286

# [0.4.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.3.0&targetVersion=GTv0.4.0&_a=commits) (2023-03-15)

### Features

- **credit:** Add API error modal ([69204bb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/69204bb95e8669a8ea120bbad5f9e1bd5b1eb7a3)) #4351152

# [0.3.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.2.2&targetVersion=GTv0.3.0&_a=commits) (2023-02-03)

### Features

- adds dependabot ([b43c19f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/b43c19f5a83856b6b2199a4e147cff59751c4441)) #4393372

## [0.2.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.2.1&targetVersion=GTv0.2.2&_a=commits) (2023-01-18)

### Bug Fixes

- copy assets to src folder on dist folder ([370785d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/370785d97858baffaf7017e8fa53b8dc102dc4dc)) #4360258

## [0.2.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.2.0&targetVersion=GTv0.2.1&_a=commits) (2023-01-18)

### Bug Fixes

- fix package.json imports path to symlink, add missing build scripts ([3a5065d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/3a5065da749a1b4e41e81643cbcccc27830015f8)) #4360258

# [0.2.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.1.1&targetVersion=GTv0.2.0&_a=commits) (2023-01-11)

### Features

- add onTopUpCredit callback ([478a705](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/478a7053e2aef6a868a6aad9698f78f015f8ab85)) #4299499

## [0.1.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.1.0&targetVersion=GTv0.1.1&_a=commits) (2023-01-11)

### Bug Fixes

- fix bug where checks on auto-generated changelog fails ([df6b005](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/df6b005b6b2a0be50d41949c5e156c0e082fb755)) #4320575

# [0.1.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/branchCompare?baseVersion=GTv0.0.5&targetVersion=GTv0.1.0&_a=commits) (2023-01-04)

### Features

- **userdetailprovider.test:** added UserDetailProvider test ([9e22df8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-credit/commit/9e22df8455f524190319041272b5ccd253123526)) #4204768
