import { WebViewNavigation } from 'react-native-webview';

import { CreditAnalyticsEvent } from '../analytics';

const checkNavigation = (
  event: WebViewNavigation,
  analyticsEvent: CreditAnalyticsEvent,
): boolean => {
  let toTriggerEvent = false;
  if (
    event.title === '' &&
    analyticsEvent === CreditAnalyticsEvent.CREDIT_STRIPE_WEBVIEW_CLOSED
  ) {
    toTriggerEvent = true;
  }

  return toTriggerEvent;
};

export default checkNavigation;
