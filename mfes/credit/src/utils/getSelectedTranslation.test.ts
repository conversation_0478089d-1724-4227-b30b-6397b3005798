import { LanguageLocale } from '../common/enums';
import translationsSets from '../translations';

import getSelectedTranslation from './getSelectedTranslation';

describe('getSelectedTranslation tests', () => {
  it('Should return all the translations for German language', () => {
    const setGermanLocale = getSelectedTranslation(LanguageLocale.GERMAN);
    expect(setGermanLocale).toBe(translationsSets.de_DE);
  });

  it('Should return all the translations for Dutch language', () => {
    const setDutchLocale = getSelectedTranslation(LanguageLocale.NETHERLANDS);
    expect(setDutchLocale).toBe(translationsSets.nl_NL);
  });

  it('Should return all the translations for American English language', () => {
    const setUSLocale = getSelectedTranslation(LanguageLocale.US_ENGLISH);
    expect(setUSLocale).toBe(translationsSets.en_US);
  });

  it('Should return all the translations for British English language when no parameter is specified', () => {
    const setEnglishLocale = getSelectedTranslation();
    expect(setEnglishLocale).toBe(translationsSets.en_GB);
  });
});
