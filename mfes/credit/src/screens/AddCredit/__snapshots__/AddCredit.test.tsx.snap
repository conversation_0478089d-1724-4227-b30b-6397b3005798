// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddCredit should render AddCredit component 1`] = `
Array [
  <Text>
    Webview
  </Text>,
  <View
    accessibilityRole="button"
    accessibilityState={
      Object {
        "busy": undefined,
        "checked": undefined,
        "disabled": false,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      Object {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      Object {
        "alignSelf": "stretch",
        "backgroundColor": "#000096",
        "borderBottomLeftRadius": 28,
        "borderBottomRightRadius": 28,
        "borderColor": "black",
        "borderStyle": "solid",
        "borderTopLeftRadius": 28,
        "borderTopRightRadius": 28,
        "borderWidth": 0,
        "height": 55,
        "justifyContent": "center",
        "lineHeight": 55,
        "opacity": 1,
      }
    }
    testID="primaryButton"
  >
    <Text
      disabled={false}
      size="normal"
      style={
        Object {
          "alignItems": "center",
          "color": "#FFFFFF",
          "display": "flex",
          "fontSize": 16,
          "fontWeight": "normal",
          "justifyContent": "center",
          "textAlign": "center",
          "textDecorationColor": "black",
          "textDecorationLine": "none",
          "textDecorationStyle": "solid",
        }
      }
      type="primary"
    >
      TopUp Success
    </Text>
  </View>,
  <View
    accessibilityRole="button"
    accessibilityState={
      Object {
        "busy": undefined,
        "checked": undefined,
        "disabled": false,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      Object {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      Object {
        "alignSelf": "stretch",
        "backgroundColor": "#000096",
        "borderBottomLeftRadius": 28,
        "borderBottomRightRadius": 28,
        "borderColor": "black",
        "borderStyle": "solid",
        "borderTopLeftRadius": 28,
        "borderTopRightRadius": 28,
        "borderWidth": 0,
        "height": 55,
        "justifyContent": "center",
        "lineHeight": 55,
        "opacity": 1,
      }
    }
    testID="primaryButton"
  >
    <Text
      disabled={false}
      size="normal"
      style={
        Object {
          "alignItems": "center",
          "color": "#FFFFFF",
          "display": "flex",
          "fontSize": 16,
          "fontWeight": "normal",
          "justifyContent": "center",
          "textAlign": "center",
          "textDecorationColor": "black",
          "textDecorationLine": "none",
          "textDecorationStyle": "solid",
        }
      }
      type="primary"
    >
      TopUp Failure
    </Text>
  </View>,
]
`;
