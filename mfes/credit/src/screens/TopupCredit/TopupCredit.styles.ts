import { Platform, StatusBar } from 'react-native';
import { styled } from 'styled-components/native';

export const TopupContainer = styled.ScrollView`
  flex: 1;
  background-color: #f9f9f9;
`;

export const AmountText = styled.Text`
  text-align: center;
  font-size: 32px;
  color: ${({ theme }) => theme.text.color.primary};
  padding-top: 80px;
`;

export const CreditText = styled.Text`
  text-align: center;
  font-size: 16px;
  color: ${({ theme }) => theme.text.color.primary};
  padding-top: 8px;
`;

export const CreditDescText = styled.Text`
  text-align: center;
  font-size: 12px;
  padding-left: 70px;
  padding-right: 70px;
  padding-top: 10px;
  padding-bottom: 33px;
`;

export const TablerowContainer = styled.View`
  border-top-width: 1px;
  border-color: ${({ theme }) => theme.lightGrey};
  background-color: ${({ theme }) => theme.color.white};
`;

export const InnerViewContainer = styled.View`
  justify-content: space-between;
  height: 83px;
  background: #f4f4f4;
  flex-direction: row;
  margin-left: 16px;
  margin-right: 16px;
  padding-left: 16px;
  padding-right: 16px;
`;

export const ImageViewContainer = styled.View`
  justify-content: center;
  align-items: center;
  flex-direction: column;
`;

export const LogoImageView = styled.View`
  align-self: center;
  justify-content: center;
  width: 85px;
  height: 26px;
`;

export const PolarPlusLogo = styled.Image`
  width: 85px;
  height: 26px;
  padding: 10px;
`;

export const TextView = styled.View`
  flex-direction: row;
  justify-content: flex-start;
  padding-left: 8px;
`;

export const CreditColorText = styled.Text`
  text-align: center;
  font-size: 12px;
  color: ${({ theme }) => theme.text.color.tertiary};
`;

export const CreditBottomText = styled.Text`
  text-align: center;
  font-size: 12px;
  color: ${({ theme }) => theme.text.color.strongSecondary};
`;

export const CreditBottomDescText = styled.Text`
  text-align: left;
  font-size: 14px;
  padding: 33px;
  color: ${({ theme }) => theme.text.color.strongSecondary};
`;

export const ComingSoonText = styled.Text`
  text-align: left;
  font-size: 12px;
  color: ${({ theme }) => theme.text.color.strongSecondary};
  font-weight: bold;
  padding-top: 10px;
`;

export const ComingSoonViewContainer = styled.View`
  justify-content: flex-end;
  flex-direction: row;
`;

export const ScreenWrapper = styled.SafeAreaView<{ backgroundColor?: string }>`
  flex: 1 0 0;
  background-color: ${({ backgroundColor }) => backgroundColor || 'white'};
  padding-top: ${Platform.OS === 'android'
    ? `${StatusBar.currentHeight}px`
    : '0px'};
`;
