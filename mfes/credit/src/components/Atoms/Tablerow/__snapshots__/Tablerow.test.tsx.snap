// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tablerow component renders with params 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={false}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "alignItems": "center",
      "borderBottomWidth": 1,
      "borderColor": "#D8D8D8",
      "borderTopWidth": 0,
      "flexDirection": "row",
      "justifyContent": "space-between",
      "opacity": 1,
      "paddingBottom": 16,
      "paddingTop": 16,
    }
  }
>
  <View
    style={
      Object {
        "alignItems": "center",
        "flexDirection": "row",
        "flexShrink": 1,
        "justifyContent": "flex-start",
        "marginBottom": 0,
      }
    }
    type="primary"
  >
    <View
      style={
        Object {
          "flexDirection": "column",
          "flexShrink": 1,
          "justifyContent": "center",
          "paddingLeft": 28,
          "paddingRight": 8,
        }
      }
    >
      <Text
        ellipsizeMode="tail"
        style={
          Object {
            "alignItems": "flex-start",
            "color": "#1D1D26",
            "fontSize": 16,
            "textAlign": "left",
          }
        }
        textColor="#1D1D26"
      >
        text
      </Text>
      <Text
        ellipsizeMode="tail"
        numberOfLines={1}
        style={
          Object {
            "color": "#616365",
            "fontSize": 12,
            "textAlign": "left",
          }
        }
      >
        subtext
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "backgroundColor": "#CCCCEA",
        "borderBottomLeftRadius": 13.5,
        "borderBottomRightRadius": 13.5,
        "borderTopLeftRadius": 13.5,
        "borderTopRightRadius": 13.5,
        "height": 24,
        "marginRight": 5,
        "paddingLeft": 4,
        "paddingRight": 4,
      }
    }
  >
    <Text
      style={
        Object {
          "color": "#000096",
          "fontSize": 14,
          "height": 24,
          "letterSpacing": 0,
          "lineHeight": 24,
          "paddingBottom": 0,
          "paddingLeft": 4,
          "paddingRight": 4,
          "paddingTop": 0,
          "textAlign": "center",
        }
      }
    >
      status
    </Text>
  </View>
  <View
    style={
      Object {
        "alignItems": "center",
        "height": 30,
        "justifyContent": "center",
        "paddingBottom": 32,
        "transform": Array [
          Object {
            "rotate": "-90deg",
          },
        ],
        "width": 30,
      }
    }
  />
</View>
`;
