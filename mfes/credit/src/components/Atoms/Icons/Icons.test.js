import React from 'react';
import { render } from '@testing-library/react-native';

import * as Icons from './';

const testProps = {
  color: '#000',
  width: '24px',
  height: '24px',
};

Object.values(Icons).map((Icon) => {
  describe('<Icon /> component', () => {
    test('its snapshot matches', () => {
      const icon = render(<Icon {...testProps} />);
      expect(icon.toJSON()).toMatchSnapshot();
    });
  });
});

describe('<ArrowRight Icon', () => {
  test('it rotates', () => {
    const icon = render(<Icons.ArrowRight rotate />);
    expect(icon.toJSON()).toMatchSnapshot();
  });
});
