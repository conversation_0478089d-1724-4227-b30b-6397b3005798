// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ArrowRight Icon it rotates 1`] = `
<Image
  source={
    Object {
      "testUri": "../../../mfes/credit/src/assets/images/png/arrow-right.png",
    }
  }
  style={
    Array [
      Object {
        "height": 24,
        "width": 24,
      },
      Object {
        "transform": Array [
          Object {
            "rotate": "180deg",
          },
        ],
      },
    ]
  }
/>
`;

exports[`<Icon /> component its snapshot matches 1`] = `
<Image
  color="#000"
  height="24px"
  source={
    Object {
      "testUri": "../../../mfes/credit/src/assets/images/png/arrow-right.png",
    }
  }
  style={
    Array [
      Object {
        "height": 24,
        "width": 24,
      },
      Object {
        "transform": Array [
          Object {
            "rotate": "0deg",
          },
        ],
      },
    ]
  }
  width="24px"
/>
`;

exports[`<Icon /> component its snapshot matches 2`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24px"
  bbWidth="24px"
  focusable={false}
  height="24px"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    Array [
      Object {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      Object {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  vbHeight={8}
  vbWidth={14}
  width="24px"
>
  <RNSVGGroup
    fill={
      Object {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        Array [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          Object {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          Array [
            1,
            0,
            0,
            1,
            -593,
            -1706,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            Object {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            Array [
              1,
              0,
              0,
              1,
              588,
              1698,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-dropdown"
        >
          <RNSVGPath
            d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
            fill={
              Object {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Path-8"
            propList={
              Array [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 3`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24px"
  bbWidth="24px"
  focusable={false}
  height="24px"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    Array [
      Object {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      Object {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      Object {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        Array [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          Object {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          Array [
            1,
            0,
            0,
            1,
            -32,
            -124,
          ]
        }
      >
        <RNSVGGroup
          fill={
            Object {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            Array [
              1,
              0,
              0,
              1,
              16,
              96,
            ]
          }
        >
          <RNSVGGroup
            fill={
              Object {
                "payload": 4278190080,
                "type": 0,
              }
            }
            matrix={
              Array [
                1,
                0,
                0,
                1,
                16,
                28,
              ]
            }
          >
            <RNSVGPath
              d="M20.8963187,3.48393467 L21.9315949,7.34763797 C22.2174785,8.41457018 21.5848669,9.51109581 20.5186173,9.79679653 L17.6558968,10.5628101 C17.4578869,11.0806968 17.0805355,11.6495897 16.5329648,12.2703155 C16.5325427,12.2719898 16.5331105,12.2740758 16.5336659,12.2761685 C16.739154,13.0504334 16.6045346,13.9405162 16.1560518,14.7173113 C15.6078974,15.6667425 14.6809049,16.2886049 13.7297708,16.3484902 C13.6077629,16.356172 13.4932535,16.3193912 13.4020405,16.2520858 C11.3304193,20.0467978 9.21697768,22.0004402 6.99860913,22.0004402 C6.72264344,22.0004402 6.49892904,21.7765826 6.49892904,21.5004402 C6.49892904,21.2242978 6.72264344,21.0004402 6.99860913,21.0004402 C9.01450464,21.0004402 11.1626886,18.7210428 13.3571242,14.1419441 C13.5100265,13.822885 13.7288734,13.5399408 13.999191,13.3118252 L14.2287702,13.1158954 L14.6570001,12.7372012 C16.5598297,11.0109793 17.1306132,9.85955053 16.6223877,9.30584234 C16.3238631,8.98060177 15.7398551,9.05689415 14.8637091,9.62040319 L14.5608313,9.82640213 C14.1425435,10.1254409 13.6664033,10.5226006 13.1318266,11.0254035 C12.2212009,11.8819039 11.1514591,12.5492174 9.98463041,12.9902979 L9.63173122,13.1157981 L8.65385385,13.4419659 C8.39204981,13.5292898 8.10907099,13.3877098 8.02180297,13.1257381 C7.9442314,12.8928744 8.04740644,12.6432693 8.25516525,12.529346 L8.33782841,12.4932826 L9.31570511,12.167115 C9.60603397,12.0702763 9.89027202,11.9580277 10.1672815,11.8310486 C9.79454267,11.5698602 9.50916174,11.1824928 9.38259326,10.7101328 L9.05886448,9.5058311 L6.99860913,10.685323 C6.35451461,11.0536126 5.8458347,11.6162237 5.54340095,12.2898927 L5.45892903,12.4952419 L2.96754506,19.1431859 C2.87064703,19.4017461 2.58262535,19.5327489 2.32423061,19.4357888 C2.09454641,19.3496021 1.96558381,19.1123074 2.00781564,18.8790442 L2.03181496,18.7920625 L4.52319893,12.1441185 C4.8632492,11.2367372 5.48608431,10.4655347 6.29636698,9.94258141 L6.50278763,9.81707981 L8.74761047,8.53350265 L8.79286448,8.5118311 L8.34731708,6.84642951 C8.06143346,5.77949731 8.69404505,4.68297167 9.76029462,4.39727096 L18.448065,2.06938992 C19.5143145,1.7836892 20.6104351,2.41700246 20.8963187,3.48393467 Z M15.6189374,13.2091377 L15.5450247,13.2779417 C15.2686137,13.5363679 14.9679248,13.802461 14.6434706,14.0762675 C14.4812808,14.2131363 14.3499745,14.3829005 14.2582336,14.5743348 C14.1365059,14.8283424 14.0146633,15.075983 13.892693,15.3172333 C14.4240917,15.2031091 14.9518477,14.8043337 15.2905805,14.2176312 C15.4782469,13.8925834 15.5879636,13.5447095 15.6189374,13.2091377 Z M18.8212005,3.01164977 L18.706884,3.03531574 L10.0191137,5.36319679 C9.52406922,5.49584355 9.21597585,5.97805513 9.28893546,6.47338855 L9.3126249,6.58777606 L10.3479011,10.4514794 C10.4680524,10.8998903 10.8744374,11.195116 11.3175891,11.1930201 C11.7175006,10.9267679 12.0956966,10.6275323 12.4473699,10.2967616 C14.6946002,8.18310068 16.3399655,7.51980928 17.3584177,8.62940611 C17.5992031,8.89174014 17.7440818,9.17888051 17.7962603,9.49111778 L20.2597983,8.8308707 C20.7548427,8.69822394 21.0629361,8.21601236 20.9899765,7.72067893 L20.966287,7.60629142 L19.9310109,3.74258812 C19.7982792,3.24722674 19.3162362,2.93880822 18.8212005,3.01164977 Z"
              fill={
                Object {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              propList={
                Array [
                  "fill",
                ]
              }
            />
          </RNSVGGroup>
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;
