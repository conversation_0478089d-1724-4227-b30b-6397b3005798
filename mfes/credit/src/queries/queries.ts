import type { DocumentNode } from 'graphql';
import { default as gql } from 'graphql-tag';
// to be changed to the GQL query getUserInfo and notifyRegistration
export const USER_INFO: DocumentNode = gql`
  query getUserInfo($userId: String!, $appCountry: String) {
    userInfo(userId: $userId, appCountry: $appCountry) {
      balance
      type
    }
  }
`;

export const GET_STRIPE_CUSTOMER: DocumentNode = gql`
  query getCustomer($userId: String!, $email: String!) {
    getCustomer(sfId: $userId, email: $email) {
      data
    }
  }
`;

export const GET_SUBS_CREDIT: DocumentNode = gql`
  query getgetSubscriptionCredit($userId: String!) {
    getSubscriptionCredit(userId: $userId) {
      payload {
        data {
          tagPlanCreditTotal
          currency
        }
      }
    }
  }
`;
