import type { DocumentNode } from 'graphql';
import { default as gql } from 'graphql-tag';

export const START_CHARGE: DocumentNode = gql`
  mutation startCharge($event: NewChargeEvent) {
    startCharge(event: $event) {
      status
      message
    }
  }
`;

export const STOP_CHARGE: DocumentNode = gql`
  mutation stopCharge($event: NewChargeEvent) {
    stopCharge(event: $event) {
      status
      message
    }
  }
`;

export const CLEAR_EVEN: DocumentNode = gql`
  mutation clearChargeSession($userId: String) {
    clearChargeSession(userId: $userId) {
      status
      message
    }
  }
`;

export const GET_CHARGE_EVENT: DocumentNode = gql`
  query getUserChargeEvent {
    getUserChargeEvent {
      eventDetails
      eventTime
      userId
      chargePayload {
        apolloInternalId
        connectorInternalId
        tagId
        status
        message
      }
    }
  }
`;

export const GET_CHARGEPOINT_BY_ID: DocumentNode = gql`
  query chargepoints($chargepointIds: [String!]!) {
    chargepoints(chargepointIds: $chargepointIds) {
      availability {
        available
        connectors {
          state
          connectorExternalId
          lastUpdate
        }
      }
      site {
        siteDetails {
          siteName
          address
          city
          postcode
        }
      }
      provider
      providerExternalId
      schemes {
        schemeId
        schemeName
      }
      privateCharge: private
      free
      overstayFee
      connectors {
        connectorInternalId
        connectorExternalId
        connectorNum: connectorExternalId
        type
        rating
      }
      costOfCharge {
        tariff {
          type
          cost
          unit
          multiplier
        }
        minimumCharge {
          type
          cost
          unit
        }
      }
    }
  }
`;

export const GET_USER_DATA: DocumentNode = gql`
  query userInfo($userId: String!) {
    userInfo(userId: $userId, appCountry: "UK") {
      status
      balance
      type
      gocardless {
        mandateId
        mandateStatus
        userId
      }
      tagIds {
        tagId
      }
      schemes {
        schemeId
        schemeName
      }
    }
    getSubscriptionPreference(userId: $userId) {
      status
      payload {
        data {
          cardPreference
          addressLine
          addressCity
          addressCountry
          addressPostcode
          nextBillingDate
          toBeCancelled
        }
        error
        code
        eventDetails
        eventTime
        salesforceId
      }
    }
  }
`;

export const LATEST_HISTORY_RECORD: DocumentNode = gql`
  query getLatestHistoryRecord($userId: String!, $appCountry: String!) {
    getLatestHistoryRecord(userId: $userId, appCountry: $appCountry) {
      eventTime
      chargeSessionId
      userId
      timestamp {
        start
        stop
      }
      duration {
        days
        hours
        minutes
        seconds
        formatted
      }
      chargepoint {
        provider
        apolloInternalId
        apolloExternalId
        providerExternalId
        chargepointId
        displayAddress
        address
        postcode
        city
        country
        connector {
          type
          connectorInternalId
          connectorExternalId
        }
      }
      chargeDetails {
        payback
        currency
        chargeGross
        chargeNet
        chargeTax
        chargeTaxPercentage
        unitCost
        minCost
        totalGross
        unitOfMeasure
        energyConsumed {
          value
          units
        }
        stateOfCharge {
          value
          units
        }
        power {
          value
          units
        }
        overstay {
          fineUnitCost
          fineGross
          fineNet
          fineTax
          fineTaxPercentage
          duration
        }
        co2Saving
      }
    }
  }
`;

export const CHARGE_HISTORY: DocumentNode = gql`
  query getUserChargeHistory(
    $userId: String
    $from: String
    $to: String
    $tagCardNumber: String
  ) {
    getUserChargeHistory(
      userId: $userId
      from: $from
      to: $to
      tagCardNumber: $tagCardNumber
    ) {
      chargeId: charge_id
      timestamp {
        startTime: start
        endTime: stop
      }
      chargepoint {
        chargepointSerial: serial
        address
        socket {
          type
          number
        }
      }
      charge {
        currency
        chargeGross: charge_gross
        netCost: charge_net
        vatCost: charge_vat
        vatPct: charge_vat_percentage
        energyUsed: energy_used
        unitCost: unit_cost
        unitMinCost: unit_min_cost
        overstay {
          fineUnitCost: fine_unit_cost
          fine: fine_gross
          fineNet: fine_net
          fineVat: fine_vat
          finevatPct: fine_vat_percentage
          duration
        }
        totalCost: total_gross
      }
    }
  }
`;
