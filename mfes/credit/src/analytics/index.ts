import type { CreditAnalyticsEventGetCustomerErrorType } from './events/CreditGetCustomerError';
import type { CreditAnalyticsEventGetCustomerSuccessType } from './events/CreditGetCustomerSuccess';
import type { CreditAnalyticsEventStripeWebviewCloseType } from './events/CreditStripeWebviewClose';
import type { CreditAnalyticsEventTopUpButtonClickType } from './events/CreditTopUpButtonClick';
import type { CreditAnalyticsEventCreditTopUpFailedType } from './events/CreditTopUpFailed';
import type { CreditAnalyticsEventTopUpCreditSuccessType } from './events/CreditTopUpSuccess';
import type { CreditAnalyticsEventYourCreditScreenErrorViewType } from './events/CreditYourCreditScreenErrorView';
import type { CreditAnalyticsEventYourCreditScreenOpenType } from './events/CreditYourCreditScreenOpen';

export enum CreditAnalyticsEvent {
  CREDIT_TOP_UP_SCREEN_OPEN = 'CreditAnalyticsEvent.CREDIT_TOP_UP_SCREEN_OPEN',
  CREDIT_GET_CUSTOMER_SUCCESS = 'CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_SUCCESS',
  CREDIT_GET_CUSTOMER_ERROR = 'CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_ERROR',
  CREDIT_YOUR_CREDIT_SCREEN_OPEN = 'CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_OPEN',
  CREDIT_YOUR_CREDIT_SCREEN_ERROR_VIEW = 'CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_ERROR_VIEW',
  CREDIT_TOP_UP_CREDIT_BUTTON_CLICK = 'CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_BUTTON_CLICK',
  CREDIT_TOP_UP_CREDIT_SUCCESS = 'CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_SUCCESS',
  CREDIT_TOP_UP_CREDIT_FAILED = 'CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_FAILED',
  CREDIT_STRIPE_WEBVIEW_CLOSED = 'CreditAnalyticsEvent.CREDIT_STRIPE_WEBVIEW_CLOSED',
}

export type CreditAnalyticsEventType =
  | CreditAnalyticsEventGetCustomerSuccessType
  | CreditAnalyticsEventGetCustomerErrorType
  | CreditAnalyticsEventYourCreditScreenOpenType
  | CreditAnalyticsEventYourCreditScreenErrorViewType
  | CreditAnalyticsEventTopUpButtonClickType
  | CreditAnalyticsEventTopUpCreditSuccessType
  | CreditAnalyticsEventCreditTopUpFailedType
  | CreditAnalyticsEventStripeWebviewCloseType;
