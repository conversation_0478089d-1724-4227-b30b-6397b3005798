import { CreditAnalyticsEvent } from '../index';

/* Event payload type */
type CreditAnalyticsEventTopUpCreditSuccessPayload = {
  creditBalance: string;
};

/* Event type */
export type CreditAnalyticsEventTopUpCreditSuccessType = {
  type: CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_SUCCESS;
  payload: CreditAnalyticsEventTopUpCreditSuccessPayload;
};

/* Event generator */
export const CreditAnalyticsEventTopUpCreditSuccess = (
  payload: CreditAnalyticsEventTopUpCreditSuccessPayload,
): CreditAnalyticsEventTopUpCreditSuccessType => ({
  type: CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_SUCCESS,
  payload,
});
