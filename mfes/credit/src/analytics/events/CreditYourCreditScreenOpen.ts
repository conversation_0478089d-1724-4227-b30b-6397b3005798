import { CreditAnalyticsEvent } from '../index';

/* Event payload type */
type CreditAnalyticsEventYourCreditScreenOpenPayload = {};

/* Event type */
export type CreditAnalyticsEventYourCreditScreenOpenType = {
  type: CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_OPEN;
  payload: CreditAnalyticsEventYourCreditScreenOpenPayload;
};

/* Event generator */
export const CreditAnalyticsEventYourCreditScreenOpen = (
  payload: CreditAnalyticsEventYourCreditScreenOpenPayload,
): CreditAnalyticsEventYourCreditScreenOpenType => ({
  type: CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_OPEN,
  payload,
});
