import { ThemeProvider } from '@bp/ui-components/mobile';
import { theme as bpPulseOverride } from '@bp/ui-components/mobile/core/themes/pulse';

import { CreditSupportedCountries } from '../common/enums';
export default ThemeProvider;
import React, { PropsWithChildren } from 'react';

import { useSettings } from '../providers/Settings';

export const Theme = ({ children }: PropsWithChildren) => {
  const { country } = useSettings();

  const theme = country === CreditSupportedCountries.DE ? {} : bpPulseOverride;

  return (
    // @ts-ignore
    <ThemeProvider customTheme={{ ...theme, topUpCustomColor: '#1D1D26' }}>
      {children}
    </ThemeProvider>
  );
};
