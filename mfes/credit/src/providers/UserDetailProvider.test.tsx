import React from 'react';
import { Text } from 'react-native';
import { MockedProvider } from '@apollo/client/testing';
import { render, waitFor } from '@testing-library/react-native';
import { GraphQLError } from 'graphql';

import { CreditAnalyticsEvent } from '../analytics';
import {
  GET_STRIPE_CUSTOMER,
  GET_SUBS_CREDIT,
  USER_INFO,
} from '../queries/queries';

import {
  UserDetailsContextProvider,
  useUserDetailsContext,
} from './UserDetailProvider';

const mockOnAnalytics = jest.fn();
jest.mock('./Settings', () => ({
  useSettings: () => ({
    userId: '12345',
    country: 'UK',
    userInfo: {
      email: '<EMAIL>',
    },
    onAnalyticsEvent: mockOnAnalytics,
  }),
}));

const MockComponent = () => {
  const { userDetails } = useUserDetailsContext();
  return (
    <>
      <Text>UserDetails Type is {userDetails.type}</Text>
      <Text>
        UserDetails balanceFormatted is {userDetails.balanceFormatted}
      </Text>
    </>
  );
};

const generateMockUserInfo = (balance: number | undefined, type?: string) => {
  const successResult = {
    data: {
      userInfo: {
        balance: balance || 0,
        type: type || 'PAYG',
      },
    },
  };
  return {
    request: {
      query: USER_INFO,
      variables: {
        userId: '12345',
        appCountry: 'UK',
      },
    },
    result: successResult,
  };
};

const generateGetStripeCustomer = () => {
  const successResult = {
    data: {
      getCustomer: {
        data: {},
      },
    },
  };
  return {
    request: {
      query: GET_STRIPE_CUSTOMER,
      variables: {
        userId: '12345',
        email: '<EMAIL>',
      },
    },
    result: successResult,
  };
};

const generateGetSUBSCustomer = ({ error }: { error?: boolean }) => {
  const successResult = {
    data: {
      getSubscriptionCredit: {
        payload: {
          data: {
            tagPlanCreditTotal: 15.3,
            currency: 'EUR',
          },
        },
      },
    },
  };
  const errorResult = {
    errors: [new GraphQLError('mockSetDefaultCard Backend Error')],
  };
  return {
    request: {
      query: GET_SUBS_CREDIT,
      variables: {
        userId: '12345',
      },
    },
    result: error ? errorResult : successResult,
  };
};

const renderComponent = (mocks: any) =>
  render(
    <MockedProvider mocks={mocks}>
      <UserDetailsContextProvider>
        <MockComponent />
      </UserDetailsContextProvider>
    </MockedProvider>,
  );

describe('UserDetailsProvider', () => {
  beforeEach(() => {
    mockOnAnalytics.mockClear();
  });

  it('should use default state for the userdetails', () => {
    const { getByText } = renderComponent([]);
    expect(getByText('UserDetails Type is')).toBeDefined();
    expect(getByText('UserDetails balanceFormatted is 0.00')).toBeDefined();
  });

  it('should display the results of the user info request', async () => {
    const { getByText } = renderComponent([generateMockUserInfo(12)]);
    await waitFor(() => {
      expect(getByText('UserDetails Type is PAYG')).toBeDefined();
      expect(getByText('UserDetails balanceFormatted is 12.00')).toBeDefined();
    });
  });

  it('should trigger getting the get stripe customer api call if get user info is successful', async () => {
    renderComponent([generateMockUserInfo(12), generateGetStripeCustomer()]);
    await waitFor(() => {
      expect(mockOnAnalytics).toHaveBeenCalledWith({
        type: CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_SUCCESS,
        payload: { creditBalance: '12.00' },
      });
    });
  });

  it('should trigger getting the SUBS user data from BPCM if required', async () => {
    renderComponent([
      generateMockUserInfo(12, 'SUBS'),
      generateGetSUBSCustomer({}),
    ]);
    await waitFor(() => {
      expect(mockOnAnalytics).toHaveBeenCalledWith({
        type: CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_SUCCESS,
        payload: { creditBalance: '15.30' },
      });
    });
  });

  it('should trigger an error if fails to get SUBS user data ', async () => {
    renderComponent([
      generateMockUserInfo(12, 'SUBS'),
      generateGetSUBSCustomer({ error: true }),
    ]);
    await waitFor(() => {
      expect(mockOnAnalytics).toHaveBeenCalledWith({
        type: CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_ERROR,
        payload: { errorMessage: 'mockSetDefaultCard Backend Error' },
      });
    });
  });
});
