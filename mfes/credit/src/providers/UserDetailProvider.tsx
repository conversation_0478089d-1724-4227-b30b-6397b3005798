import React, {
  createContext,
  ReactElement,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useLazyQuery, useQuery } from '@apollo/client';

import {
  CreditAnalyticsEventGetCustomerError,
  CreditAnalyticsEventGetCustomerSuccess,
} from '../analytics/events';
import {
  GET_STRIPE_CUSTOMER,
  GET_SUBS_CREDIT,
  USER_INFO,
} from '../queries/queries';

import { useSettings } from './Settings';

interface IUserData {
  balanceFormatted: string;
  type: string;
}

interface IUserDetailsContext {
  userDetails: IUserData;
  updateUserDetails: () => void;
  loading: boolean;
  error: string | undefined;
  createStripeId: (options: any) => void;
}

export const UserDetailsContext = createContext<IUserDetailsContext>({
  userDetails: { balanceFormatted: '0.00', type: 'PAYG' },
  loading: false,
  error: undefined,
  updateUserDetails: () => {
    console.log(
      'You are trying to trigger updateUserDetails without wrapping the request in the UserDetailsContextProvider',
    );
  },
  createStripeId: () => {},
});

export const useUserDetailsContext = (): IUserDetailsContext =>
  useContext(UserDetailsContext);

const fetchPolicyNetwork = 'network-only';
export const UserDetailsContextProvider = ({
  children,
}: {
  children: ReactElement;
}) => {
  const [error, setError] = useState<string | undefined>(undefined);
  const [userDetails, setUserDetails] = useState({
    balanceFormatted: '0.00',
    type: '',
  });
  const {
    userId,
    country,
    userInfo: idpInfo,
    onAnalyticsEvent,
  } = useSettings();
  const [getStripeCustomer] = useLazyQuery(GET_STRIPE_CUSTOMER, {
    fetchPolicy: fetchPolicyNetwork,
    variables: {
      userId,
      email: idpInfo.email,
    },
    onCompleted: () =>
      onAnalyticsEvent(
        CreditAnalyticsEventGetCustomerSuccess({
          creditBalance: userDetails.balanceFormatted,
        }),
      ),
    onError: (e: Error) => {
      setError(e.message);
      onAnalyticsEvent(
        CreditAnalyticsEventGetCustomerError({
          errorMessage: e.message,
        }),
      );
    },
  });
  const [getSubsCredit, { loading: subsCreditLoading }] = useLazyQuery(
    GET_SUBS_CREDIT,
    {
      fetchPolicy: fetchPolicyNetwork,
      variables: {
        userId,
      },
      onCompleted: ({ getSubscriptionCredit: { payload } }) => {
        const balance =
          (payload && payload.data.tagPlanCreditTotal.toFixed(2)) || '0.00';
        setUserDetails({
          balanceFormatted: balance,
          type: userDetails.type,
        });
        onAnalyticsEvent(
          CreditAnalyticsEventGetCustomerSuccess({
            creditBalance: balance,
          }),
        );
      },
      onError: (e: Error) => {
        onAnalyticsEvent(
          CreditAnalyticsEventGetCustomerError({
            errorMessage: e.message,
          }),
        );
        setError(e.message);
      },
    },
  );

  const { loading: userLoading, refetch } = useQuery(USER_INFO, {
    variables: {
      userId,
      appCountry: country,
    },
    fetchPolicy: fetchPolicyNetwork,
    notifyOnNetworkStatusChange: true,
    skip: !userId,
    onCompleted: ({ userInfo }: any) => {
      const balance =
        (userInfo && userInfo.balance && userInfo.balance.toFixed(2)) || '0.00';
      const type = (userInfo && userInfo.type) || 'PAYG';
      setUserDetails({
        balanceFormatted: balance,
        type,
      });
    },
    onError: (e: Error) => {
      setError(e.message);
      console.log('error', e);
    },
  });

  useEffect(() => {
    if (userDetails.type === 'PAYG') {
      // Checks that the user is enrolled in the top up service and if they are not it enrols them
      getStripeCustomer();
    }
    if (userDetails.type === 'SUBS') {
      getSubsCredit();
    }
  }, [getStripeCustomer, getSubsCredit, userDetails.type]);

  const loading = subsCreditLoading || userLoading;

  return (
    <UserDetailsContext.Provider
      value={{
        updateUserDetails: refetch,
        userDetails,
        loading,
        error,
        createStripeId: () => {},
      }}
    >
      {children}
    </UserDetailsContext.Provider>
  );
};
