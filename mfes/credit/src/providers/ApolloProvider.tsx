import React, { useMemo } from 'react';
import { getApplicationName, getVersion } from 'react-native-device-info';
import {
  ApolloClient,
  ApolloLink,
  ApolloProvider,
  createHttpLink,
  InMemoryCache,
} from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { ErrorResponse, onError } from '@apollo/client/link/error';
import { name, version } from '@bp/credit-mfe/package.json';
import {
  createAbortLink,
  createRequestLink,
  createResponseLink,
  createTimerLink,
  createXrayLink,
} from '@bp/mfe-helper-apollo';

import { useSettings } from './Settings';

const GraphQLProvider = ({ children }: { children: React.ReactNode }) => {
  const { apiKey, apiURL, getToken, onError: handleOnError } = useSettings();

  // Create authentication middleware to add id_token to requests
  const authLink = setContext(async (_, { headers }) => {
    // return the headers to the context so http<PERSON><PERSON> can read them
    return {
      headers: {
        ...headers,
        authorization: await getToken(),
      },
    };
  });

  // Handle errors client side
  const errorLink = onError(
    ({ graphQLErrors, networkError }: ErrorResponse) => {
      if (handleOnError) {
        if (graphQLErrors) {
          handleOnError({ type: 'graphql', details: graphQLErrors });
        }
        if (networkError) {
          handleOnError({ type: 'network', details: networkError });
        }
      }
    },
  );

  // Create Http link
  const link = createHttpLink({
    headers: {
      'x-api-key': apiKey,
      'x-app-name': getApplicationName(),
      'x-app-version': getVersion(),
      'x-mfe-name': name,
      'x-mfe-version': version,
    },
    uri: apiURL,
  });

  // create an inmemory cache instance for caching graphql data
  const cache = new InMemoryCache();

  const requestLink = createRequestLink();
  const responseLink = createResponseLink();
  const timerLink = createTimerLink();
  const abortLink = createAbortLink();
  const xrayLink = createXrayLink();

  // Create apollo client
  const client = useMemo(
    () =>
      new ApolloClient({
        link: ApolloLink.from([
          errorLink,
          authLink,
          requestLink,
          abortLink,
          xrayLink,
          timerLink,
          responseLink,
          link,
        ]),
        cache,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [apiKey, apiURL, getToken],
  );

  return <ApolloProvider client={client}>{children}</ApolloProvider>;
};

export default GraphQLProvider;
