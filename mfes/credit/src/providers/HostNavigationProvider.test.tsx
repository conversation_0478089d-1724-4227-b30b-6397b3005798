/*
    Needs improvements!
*/
import React from 'react';
import { BackHandler, Text } from 'react-native';
import { render, waitFor } from '@testing-library/react-native';

import {
  mockNavigation,
  MockScreens,
  NavigationContainer,
} from '../utils/NavigationContainer';

import HostNavigationProvider from './HostNavigationProvider';

let mockedNavigation: any = jest.fn();

jest.mock('./Settings', () => ({
  useSettings: () => ({ navigationKey: mockedNavigation }),
}));

jest.mock('../common/enums', () => ({
  WalletScreenNames: { Screen1: 'Screen1' },
}));

jest.mock(`react-native/Libraries/Utilities/BackHandler`, () =>
  jest.requireActual(
    `react-native/Libraries/Utilities/__mocks__/BackHandler.js`,
  ),
);

jest.spyOn(global.console, 'error').mockImplementation(jest.fn());

describe('HostNavigationProvider', () => {
  it('Should render children and prevent going back', () => {
    const { getByTestId } = render(
      <HostNavigationProvider navigation={mockNavigation}>
        <Text testID="child" />
      </HostNavigationProvider>,
    );

    expect(getByTestId('child')).toBeDefined();
  });

  it('should call navigation', async () => {
    const { getByTestId } = render(
      <NavigationContainer navigation={mockNavigation}>
        <HostNavigationProvider navigation={mockNavigation}>
          <MockScreens navigation={mockNavigation} />
        </HostNavigationProvider>
      </NavigationContainer>,
    );

    await waitFor(() => {
      expect(mockNavigation.isReady()).toBe(true),
        {
          timeout: 1e4,
        };
    });

    await waitFor(() => {
      expect(getByTestId('navigateScreen')).toBeDefined();
    });
  });

  it('should not call navigation', () => {
    mockedNavigation = null;
    render(
      <NavigationContainer navigation={mockNavigation}>
        <HostNavigationProvider navigation={mockNavigation}>
          <MockScreens navigation={mockNavigation} />
        </HostNavigationProvider>
      </NavigationContainer>,
    );
    // @ts-ignore
    BackHandler.mockPressBack();
  });
});
