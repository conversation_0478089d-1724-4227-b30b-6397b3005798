# MFE Pipeline structure

Each MFE has a trigger pipeline stored in the `triggers` folder

Each trigger applies to changes in the specified MFE directory

There are 3 scenarios that can be triggered

1. [PR](#pr)
2. [Merge to main](#merge)
3. [Manual](#manual)

## <a id="pr" name="pr"></a> PR

This will run the following stages

- Lint
- Test
- Types
- Sonarqube
- Security scans

## <a id="merge" name="merge"></a> Merge to main

This will run the following stages

- Test
- Publish

## <a id="manual" name="manual"></a> Manual

There is also a manual pipeline that can be run for any of the available MFEs
