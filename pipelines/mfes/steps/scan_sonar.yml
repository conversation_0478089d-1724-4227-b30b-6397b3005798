parameters:
  - name: "project<PERSON><PERSON>"
    default: ""
  - name: "projectName"
    default: ""
  - name: "projectVersion"
    default: ""
  - name: "sonarJavaBinaries"
    default: ""
  - name: "sonarLoggingLevel"
    default: "info"
  - name: "sonarPollingTimeout"
    default: 300
  - name: "sonarProfile"
    default: "Sonar way"
  - name: "sonarPublish"
    default: true
  - name: "sonarService"
    default: ""
  - name: "sonarSources"
    default: "."
  - name: "sonarBuildBreaker"
    default: false
  - name: "additionalArgs"
    default: ""
  - name: "path"

steps:
  - pwsh: |
      if([string]::isnullorempty('${{ parameters.sonarService }}')) {
        Write-Host "##vso[task.logissue type=error;]The sonarService parameter is empty"
        exit 1
      }
    displayName: Check mandatory parameters

  - pwsh: |
      $sonarProperties = @"
        sonar.projectKey=${{ parameters.projectKey }}
        sonar.projectName=${{ parameters.projectName }}
        sonar.projectVersion=${{ parameters.projectVersion }}
        sonar.profile=${{ parameters.sonarProfile }}
        sonar.sources=${{ parameters.sonarSources }}
        sonar.log.level=${{ parameters.sonarLoggingLevel }}
        sonar.java.binaries=${{ parameters.sonarJavaBinaries }}
        ${{ parameters.additionalArgs }}
      "@
      if(!(test-path "./${{ parameters.path }}/sonar-project.properties")) {
        Write-Host "No sonar-project.properties file found. Generating one"
        out-file -inputobject $sonarProperties -filepath ./${{ parameters.path }}/sonar-project.properties -encoding ascii
      }
      write-host (get-content ./${{ parameters.path }}/sonar-project.properties)
    displayName: Prepare Sonar properties

  - task: SonarQubePrepare@7
    inputs:
      SonarQube: ${{ parameters.sonarService }}
      scannerMode: "CLI"
      configMode: "file"
      configFile: "${{ parameters.path }}/sonar-project.properties"
    displayName: Prepare Sonar analysis

  - task: SonarQubeAnalyze@7
    displayName: Execute Sonar analysis

  - ${{ if eq(parameters.sonarPublish, true) }}:
      - task: SonarQubePublish@7
        inputs:
          pollingTimeoutSec: ${{ parameters.sonarPollingTimeout }}
        displayName: Publish Sonar results

  - ${{ if eq(parameters.sonarBuildBreaker, true) }}:
      - task: sonar-buildbreaker@9
        inputs:
          SonarQube: ${{ parameters.sonarService }}
        displayName: Check Sonar results
