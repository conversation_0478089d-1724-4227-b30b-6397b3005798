import type { Serverless } from 'serverless/aws';

// This configuration uses the Serverless type from 'serverless/aws'
// and reflects your original serverless.yml settings.
const serverlessConfiguration: Serverless = {
  org: 'bppulse', // Your organization name
  service: 'hello-world-sls', // Your service name
  provider: {
    name: 'aws',
    runtime: 'nodejs20.x', // Your specified runtime
  },
  functions: {
    // Your 'hello' function definition
    hello: {
      handler: 'handler.hello', // Path to your handler function
      events: [
        {
          http: { // HTTP event configuration (matches your YML)
            path: '/',
            method: 'get',
          },
        },
      ],
    },
  },
  // Add other sections like 'plugins', 'package', 'custom', or 'resources' as needed.
  // For example:
  // resources: {}, 
  // plugins: [],
};

// Export the configuration object
module.exports = serverlessConfiguration;