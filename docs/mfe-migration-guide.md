# MFE Migration Guide

This document details the steps required to import and integrate an MFE into the monorepo

## Task list

- [Import the MFE commit history](#import)
- [Configure the yarn workspace](#workspace)
- [Migrate yarn.lock file](#yarn.lock)
- [Migrate any patches to yarn patches](#patches)
- [Setup metro to watch folder](#metro)
- [Correct any MFE subdir imports in the app](#distimports)
- [Update main entry point to ts index](#entry)
- [Update the pulse SDK to >= 3.6.0](#pulsesdk)
- [Add the SDK path to TSconfig](#tsconfig)
- [Remove files](#removefiles)
- [Validate the app builds using MFE in monorepo](#buildvalidation)
- [Migrate the crowdin.yml file to root](#crowdin)
- [Update jest config with SDK compatibility](#jest)
- [Fix any broken tests](#tests)
- [Resolve any type errors](#types)
- [Update eslint config with SDK compatiblity](#eslint)
- Fix any lint issues
- [Migrate husky hooks](#husky)
  - Silence pre-push test run
- [Migrate .vscode folder to root](#vscode)
- [Migrate .editorconfig to root](#editorconfig)
- [Pipelines](#pipelines)
  - [Setup trigger](#trigger)
  - [Migrate to unified MFE pipeline](#migrate)
  - [SonarQube](#sonarqube)
  - [Checkmarx](#checkmarx)
  - Cleanup
- [Refactor the published package](#publishedpackage)
- [Configure semantic release](#semanticrelease)
- [Final commit history import](#finalimport)
- [Migrate secrets to the monorepo](#secrets)
- [Push the most recent tag](#tag)
- [Validate published package](#validate)

## Task Explanation

In the following sections replace {NAME} with the name of the MFE, such as: "charge"

### 1. <a id="import" name="import"></a> Import the commit history of the MFE using [tomono](https://github.com/hraban/tomono)

- Set the monorepo name

  `export MONOREPO_NAME=bp-pulse-mobile`

- Create a file called `new-repos.txt` containing the git origin of the MFE, the name and the folder structure.

  ```
  *********************:v3/bp-digital/DCM_Frameworks/bp-mfe-{NAME} {NAME} mfes/{NAME}
  ```

- In the parent directory to the monorepo run tomono

  `tomono --continue < new-repos.txt`

### 2. <a id="workspace" name="workspace"></a> Configure the yarn workspace

- Configure metro

- Remove `packageManager` from `mfes/{NAME}/package.json` if it exists

### 3. <a id="yarn.lock" name="yarn.lock"></a> Migrate yarn.lock file

- Limit dependency hoisting to the MFE workspace

  ```json
  "installConfig": {
      "hoistingLimits": "workspaces"
  }
  ```

- Install deps using yarn

  `yarn install`

- Delete yarn.lock file from mfe

  `rm mfes/{NAME}/yarn.lock`

### 4. <a id="patches" name="patches"></a> Migrate any patches to yarn patches

See https://yarnpkg.com/cli/patch

Remove other patch dependencies

Add the following eject-react-native script to the MFE package.json

`"eject-react-native": "rm -rf node_modules/@bp/pulse-mobile-sdk/node_modules/react-native"`

Add the eject-react-script to postinstall

`"postinstall": "yarn run eject-react-native"`

This script removes react-native from the SDK repo inside the MFE so that we only have one installation of react-native used in the project at the root level. The postinstall script will work on the first install but is flaky on any following installs so may need to be run manually.

### 5. <a id="metro" name="metro"></a> Setup metro to watch folder

In `app/metro.config.js`

add the following to watchFolders

```js
path.resolve(__dirname, "../mfes/{NAME}");
```

### 6. <a id="distimports" name="distimports"></a> Correct any MFE subdir imports in the app

Search for `@bp/{NAME}-mfe/dist` to find all mfe imports using the dist folder

Replace with `@bp/{NAME}-mfe`

### 7. <a id="entry" name="entry"></a> Update main entry point to ts index

In `mfes/{NAME}/package.json`

Modify the main value from `dist/index.js` to `index.ts`

### 8. <a id="pulsesdk" name="pulsesdk"></a> Update the pulse SDK to >= 3.6.0

You can find the [latest version available on the registry here](https://dev.azure.com/bp-digital/DST-Digital_Cross-Business_Platforms/_artifacts/feed/apolloxi-component-library/Npm/@bp%2Fpulse-mobile-sdk/overview)

Manually update the `mfes/{NAME}/package.json` file with the updated version for `@bp/pulse-mobile-sdk`

It needs to include the patch SDK patch included in this repo

### 9. <a id="tsconfig" name="tsconfig"></a> Add the SDK paths to TSconfig

In `mfes/{NAME}/tsconfig.json`

Add the following to `paths`

```json
"@bp/{NAME}-mfe/*": ["./*"],
"*": ["./node_modules/@bp/pulse-mobile-sdk/node_modules/*"]
```

### 10. <a id="removefiles" name="removefiles"></a> Remove files

Remove the following files if they exist

`rm mfes/{NAME}/.npmrc`

`rm mfes/{NAME}/.nvmrc`

`rm mfes/{NAME}/.yarnrc`

`rm mfes/{NAME}/.yarnrc.yml`

### 11. <a id="buildvalidation" name="buildvalidation"></a> Validate the app builds using MFE in monorepo

Run the following commands

`yarn`

`yarn app:ios`

The app should build and install onto an iOS simulator, once complete run

`yarn workspace bp-pulse-app start`

This will run the metro server, at which point you should be able to load the app.

Any errors during these steps will need to be resolved.

To validate the MFE integration & live reload, open the app to a screen that uses the MFE. Then you could modify the components displayed on the screen, or add a console log to verify that the screen updates with the new code.

### 12. <a id="crowdin" name="crowdin"></a> Migrate the crowdin.yml file to root

From `mfes/{NAME}/crowdin.yml`

Merge the files value into the root `crowdin.yml` and update the paths to point to `mfes/{NAME}`

Then delete the mfe crowdin file

`rm mfes/{NAME}/crowdin.yml`

### 13. <a id="jest" name="jest"></a> Update jest config with SDK compatibility

See `mfes/credit/jest.config.js` and `mfes/credit/jest.setUp.js` for an example

Add `jest-environment-node` as a dev dependency of the mfe

TODO: Add more details here

### 14. <a id="tests" name="tests"></a> Fix any broken tests

It is likely some tests will be broken. This if often due to conflicting or misinstalled dependencies.

You may need to move `@testing-library/react-native` to peer dependencies in the mfe.
We will use the install at the root of the repo so it will be compatible with the react-native install.

### 15. <a id="types" name="types"></a> Resolve any type errors

Remove `@types/react-native` dependency if it exists

Add `"../../node_modules/@types"` to tsconfig typeRoots

The latest versions of react-native provide the types themselves

### 16. <a id="eslint" name="eslint"></a> Update eslint config with SDK compatiblity

See `mfes/credit/eslintrc.js` for an example

TODO: Add more details here

### 17. <a id="eslint" name="eslint"></a> Fix any lint issues

### 18. <a id="eslint" name="eslint"></a> Migrate husky hooks

See `/mfes/{NAME}/.husky/` for the current hooks used in the mfe

See `/.husky/` for the current supported hooks in the monorepo

To migrate hooks they will be moved to the mfe's package.json scripts

For example, a `pre-commit` hook containing the command `yarn test` becomes `"pre-commit": "yarn test"`

Hooks are now installed at the monorepo level, if they are for commands that need to run across multiple workspaces then they will call a script in the monorepo package that will call the script across all workspaces using `yarn workspaces foreach --since -p run pre-commit`

Some hooks will not need to be migrated, as they are now controlled at the monorepo root, rather than on a per MFE basis. (Eg: commit linting)
In some cases you may also want to merge a hook into the monorepo root or delete the hook all together if it's no longer required.

Once migrated, remove the `/mfes/{NAME}/.husky/` folder

Silence pre-push test run: To prevent spam in the terminal when all workspaces are running tests at the same time, please add `--silent` to test runs executed by hooks.

### 19. <a id="vscode" name="vscode"></a> Migrate .vscode folder to root

Merge any vscode settings or extensions that should remain in the repo to the root

Delete the `mfes/{NAME}/.vscode/` directory after migrating.

### 20. <a id="editorconfig" name="editorconfig"></a> Migrate .editorconfig to root

Merge any settings that should remain in the repo to the root

Delete the `mfes/{NAME}/.editorconfig` file after migrating.

### 21. <a id="pipelines" name="pipelines"></a> Pipelines

#### <a id="trigger" name="trigger"></a> Setup trigger

Setup pipeline trigger in `/pipelines/mfes/triggers/`

Copy one of the existing triggers and replace the name with your incoming MFE

In `pipelines/mfes/publish_manual.yml` add the name of your new MFE to the values list

Once pushed, setup a pipeline in [this folder in Azure](https://dev.azure.com/bp-digital/bp_pulse/_build?definitionScope=%5Cbp-pulse-mobile%5Cmfes)

Make sure to name the pipeline `pulse-mfe-{NAME}`

You can use our refactor branch for now, after merge we will change this to main

On the review page, press the dropdown next to run and press save

Go to the [main branch policies in Azure](https://dev.azure.com/bp-digital/bp_pulse/_settings/repositories?repo=d029cee7-9f91-46bb-8d0a-77d4b8f649ad&_a=policiesMid&refs=refs/heads/main).

Under build validation, add the pulse-mfe-{NAME} pipeline with the path filter `/mfes/{NAME}/*`

#### <a id="migrate" name="migrate"></a> Migrate to unified pipeline

When migrating MFEs into the monorepo we are attempting to unify the pipelines into a single standard

The unified pipelines can be found in `/pipelines/mfes/`

The standard currently supports the following stages

- Lint
- Test
- SonarQube
- Build & Publish

If your MFE has additional pipeline stages that must be run they will either need to be added to the unified pipeline standard for all MFEs, added individually as an addditional stage on the trigger or you may need to setup an entirely separate trigger depending on the context

#### <a id="sonarqube" name="sonarqube"></a> SonarQube

Modify the `mfe/{NAME}/sonar-project.properties` file to include

`sonar.projectBaseDir=mfes/{NAME}/`

#### <a id="checkmarx" name="checkmarx"></a> Checkmarx

Checkmarx SAST & SCA scans are handled by the app monorepo pipeline which is run with PR

#### Cleanup

_There is still ambiguity on which pipelines need to be migrated so skip this step for now_

Once all pipelines have been migrated the `mfes/{NAME}/pipelines` directory can be deleted

### 22. <a id="publishedpackage" name="publishedpackage"></a> Refactor the published package

Add this postbuild script to the package.json file and to the end of the build script

Check if the index.js file is inside the src folder and update accordingly

```json
"postbuild": "cp README.md dist && cd dist && npm pkg set main='index.js'"
```

After this, add the postbuild script to the end of the build script

```json
"build": "{BUILD_SCRIPT...} && yarn postbuild"
```

(Deprecated?)
Add publishConfig to the package.json

```json
"publishConfig": {
  "main": "index.js"
}
```

### 23. <a id="semanticrelease" name="semanticrelease"></a> Configure semantic release

TODO: Create a common release config to be extended by every MFE

Copy the contents of `release.config.js` from one of the existing MFEs into the new MFE.

Replace `tagFormat` with the correct MFE name `{NAME}/v${version}`

### 24. <a id="finalimport" name="finalimport"></a> Final commit history import

Since some time will have passed since the original import, it's important to do a final import to make sure all changes are migrated

At this point there should be a code freeze on the MFE repo

The following commands should be enough but there is further documentation on the [tomono github page](#https://github.com/hraban/tomono?tab=readme-ov-file#pull-in-new-changes-from-a-remote) if required

Fetch the latest changes from the remote repository (this is the MFE repo)

`git fetch {NAME}`

Merge the remote default branch (usually main) into the monorepo where the MFE is located.

`git merge -X subtree=mfes/{NAME}/ {NAME}/main`

Once complete you'll also want to merge the latest main from the monorepo

### 25. <a id="secrets" name="secrets"></a> Migrate secrets to the monorepo

You will require access to the git secrets of the original MFE repo to do this

Remove the imported `.secret` files inside the `mfes/{NAME}` directory

Manually copy the revealed files over to the monorepo, moving them into the `mfes/{NAME}` directory

Remove the `mfes/{NAME}/.gitsecret` directory

Follow the regular process of adding files to git secret to complete the migration with the copied files

If the MFE repo contains a GITSECRET.md document, merge its content with the centralized [GITSECRET.md](../docs/GITSECRET.md) and update the link in the MFE README.md to point to the centralized version

### 26. <a id="tag" name="tag"></a> Push the most recent tag

In order for semantic release to function correctly, we need to push the last version tag. These should have been imported to your local git during the tomono import.

Run `git tag` to view all tags.

Look for the latest version for your MFE, it will look like `{NAME}/v1.0.0`

Push the tag with `git push origin {TAG} --no-verify`

Verify that your tag appears in [the repo](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/tags)

### 27. <a id="validate" name="validate"></a> Validate published package

After merging the MFE migration PR the [pipeline trigger](#pipelines) will run for the main branch, attempting to publish a new version of the MFE using semanic-release

Given the changes made to [refactor the package](#publishedpackage), this should be a major release

If the pipeline does not release as expected, it must be debugged and fixed before the code freeze can end
