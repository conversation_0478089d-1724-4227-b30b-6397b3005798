{"name": "@bp/pulse-mobile", "version": "1.0.0", "description": "Monorepo for the bp pulse global ev app", "repository": "https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile", "author": "<PERSON> <<EMAIL>>", "license": "ISC", "private": true, "resolutions": {"@apollo/client": "3.7.3", "@babel/plugin-syntax-top-level-await": "7.14.5", "@bp/mfe-helper-apollo": "0.0.3", "@bp/react-native-cardinal-sdk": "0.0.78", "@graphql-typed-document-node/core": ">3.1.1", "@react-navigation/core": "6.2.2", "@types/react": "18.3.18", "@semantic-release/release-notes-generator@npm:^12.0.0": "patch:@semantic-release/release-notes-generator@npm%3A12.1.0#~/.yarn/patches/@semantic-release-release-notes-generator-npm-12.1.0-866cd1b0ca.patch", "@semantic-release/npm": "patch:@semantic-release/npm@npm%3A11.0.3#~/.yarn/patches/@semantic-release-npm-npm-11.0.3-fdd5bb6434.patch", "react-native@npm:0.72.7": "patch:react-native@npm%3A0.72.7#~/.yarn/patches/react-native-npm-0.72.7-1edea1acc1.patch", "braces": "3.0.3", "cross-spawn": "7.0.6", "decode-uri-component": "0.3.0", "debug": "4.3.7", "dset": "3.1.4", "fast-xml-parser": "4.5.0", "ip": "2.0.1", "jpeg-js": "0.4.4", "json5": "1.0.2", "micromatch": "4.0.8", "postcss": "8.4.31", "react-native-appsflyer": "6.12.2", "react-native-bootsplash": "5.1.3", "react-native-reanimated": "3.11.0", "react-native-svg": "14.1.0", "react-native-webview": "13.6.3", "semantic-release": "22.0.12", "styled-components": "6.1.11", "ws": "8.18.0", "yaml": "2.2.2"}, "scripts": {"app:ios": "yarn workspace bp-pulse-app ios", "sdk:eject-react": "yarn workspaces foreach --all -p run eject-react", "sdk:eject-react-native": "yarn workspaces foreach --all -p run eject-react-native", "postinstall": "yarn run sdk:eject-react && yarn run sdk:eject-react-native", "pre-commit": "yarn workspaces foreach --since -p run pre-commit", "pre-push": "yarn workspaces foreach --since -p run pre-push", "prepare": "husky"}, "workspaces": ["app", "mfes/*"], "packageManager": "yarn@4.6.0", "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@testing-library/react-hooks": "^8.0.1", "husky": "^9.0.11", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "semantic-release": "^22.0.12", "semantic-release-ado": "^1.4.0", "semantic-release-monorepo": "^8.0.2"}, "installConfig": {"hoistingLimits": "workspaces"}}